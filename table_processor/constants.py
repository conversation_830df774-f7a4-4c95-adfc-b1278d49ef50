"""常量定义"""

QUERY_ENHANCEMENT_PROMPT = """请根据以下上下文增强查询：

原始查询：{query}

上下文信息：
{context}

请特别注意：
1. 如果查询涉及表格的行列标题组合，确保包含两者
2. 如果是多行填写的情况，标注顺序信息
3. 如果有特殊标记，保留其语义

请生成一个更完整的查询，确保：
1. 包含必要的上下文信息
2. 保持查询的简洁性
3. 明确指出具体的信息需求

请直接返回增强后的查询文本，不需要其他解释。"""

QUERY_GENERATION_PROMPT = """请为以下表格生成查询：

{table_content}

请注意：
1. 只为空单元格生成查询
2. 使用行列标题组合生成查询
3. 保持查询简洁明确

请返回查询列表。"""

STRUCTURE_ANALYSIS_PROMPT = """请分析表格结构：

{table_content}

请关注：
1. 表格类型（一维、二维、复杂）
2. 行列标题关系
3. 空单元格位置

请返回分析结果。"""

SUBTABLE_DETECTION_PROMPT = """请检测子表格：

{table_content}

请识别：
1. 子表格边界
2. 表格类型
3. 行列关系

请返回检测结果。""" 