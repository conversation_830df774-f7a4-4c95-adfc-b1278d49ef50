"""复杂表格处理器

此模块用于处理复杂的混合表格，包括:
1. 表格结构分析
2. 智能切分
3. 子表格处理
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
from docx.table import Table, _Cell
from docx.oxml.table import CT_Tbl
from .utils.docx_utils import write_cell_content, write_table_cell, get_merged_cells
from .table_types import TableType, detect_table_type, AnalysisMode
from .hybrid_detector import HybridTableDetector
from .base_processor import BaseProcessor

logger = logging.getLogger(__name__)

class ComplexTableProcessor(BaseProcessor):
    """复杂表格处理器"""
    
    def __init__(self, mode: AnalysisMode = AnalysisMode.COMPLETE):
        """初始化复杂表格处理器
        
        Args:
            mode: 分析模式，默认为完整模式
        """
        self.mode = mode
        self.detector = HybridTableDetector(mode=mode)
        self.total_queries = 0
        self.total_marked_queries = 0
        
    def process_table(self, table: CT_Tbl) -> Dict[str, Any]:
        """处理表格
        
        Args:
            table: docx表格对象
            
        Returns:
            处理结果统计
        """
        try:
            # 获取表格行列数
            rows = len(table.tr_lst)
            cols = len(table.tr_lst[0].tc_lst) if rows > 0 else 0
            logger.info(f"\n开始处理表格:\n表格行数: {rows}\n表格列数: {cols}")
            
            # 检测表格类型
            table_type = detect_table_type(table)
            logger.info(f"检测到表格类型: {table_type}")
            
            if table_type == TableType.TWO_DIMENSIONAL:
                self._process_two_dimensional_table(table)
            else:
                self._process_complex_table(table)
                
            return {
                "total_queries": self.total_queries,
                "total_marked_queries": self.total_marked_queries
            }
            
        except Exception as e:
            logger.error(f"处理表格失败: {str(e)}", exc_info=True)
            return {
                "total_queries": 0,
                "total_marked_queries": 0
            }
    
    def process_complex_table(self, table: Table, mode: AnalysisMode = AnalysisMode.BALANCED) -> Dict[str, Any]:
        """处理复杂表格
        
        Args:
            table: docx表格对象
            mode: 处理模式
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 获取表格信息
            table_info = self.get_table_info(table)
            
            # 识别子表格
            subtables = self._identify_subtables(table_info)
            
            # 处理每个子表格
            total_queries = []
            marked_queries = []
            
            logger.info(f"\n识别到 {len(subtables)} 个子表格:")
            for idx, subtable in enumerate(subtables):
                logger.info(f"\n子表格 {idx + 1}:")
                logger.info(f"- 类型: {subtable['type']}")
                logger.info(f"- 行范围: {subtable['start_row']}-{subtable['end_row']}")
                
                # 处理子表格
                result = self._process_subtable(table, (subtable['start_row'], subtable['end_row']), mode)
                if result:
                    total_queries.extend(result.get('queries', []))
                    marked_queries.extend(result.get('marked_queries', []))
            
            return {
                'queries': total_queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            logger.error(f"处理复杂表格失败: {str(e)}", exc_info=True)
            return None
    
    def _analyze_table_structure(self, table: Table) -> Dict[str, Any]:
        """分析表格结构
        
        分析包括:
        1. 合并单元格
        2. 标题行
        3. 分隔行
        4. 表格层次
        """
        structure = {
            'merged_cells': [],
            'section_rows': [],
            'headers': [],
            'hierarchy': []
        }
        
        try:
            for row_idx, row in enumerate(table.rows):
                # 检查合并单元格
                for col_idx, cell in enumerate(row.cells):
                    if cell._tc.grid_span > 1 or cell._tc.vMerge:
                        structure['merged_cells'].append({
                            'row': row_idx,
                            'col': col_idx,
                            'rowspan': self._get_vertical_merge(cell),
                            'colspan': cell._tc.grid_span
                        })
                
                # 检查是否是分隔行
                if self._is_section_row(row):
                    structure['section_rows'].append(row_idx)
                
                # 检查是否是标题行
                if self._is_header_row(row):
                    structure['headers'].append(row_idx)
                
                # 分析层次结构
                hierarchy = self._analyze_row_hierarchy(row)
                if hierarchy:
                    structure['hierarchy'].append({
                        'row': row_idx,
                        'level': hierarchy
                    })
        
        except Exception as e:
            logger.error(f"分析表格结构失败: {str(e)}", exc_info=True)
        
        return structure
    
    def _identify_subtables(self, table_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """识别子表格
        
        Args:
            table_info: 表格信息
            
        Returns:
            List[Dict[str, Any]]: 子表格列表
        """
        try:
            structure = table_info.get('structure', [])
            logger.debug(f"表格结构: {len(structure)} 行")
            
            if not structure:
                logger.warning("表格结构为空")
                return []
                
            subtables = []
            current_start = 0
            current_type = None
            
            # 遍历每一行
            for row_idx, row in enumerate(structure):
                # 分析当前行的类型
                row_type = self._analyze_row_type(row)
                logger.debug(f"行 {row_idx}: 类型={row_type}")
                
                # 如果是新的表格类型，或者是表格分隔符
                if row_type != current_type:
                    logger.debug(f"检测到新的表格类型: {row_type} (之前: {current_type})")
                    # 如果已经有一个子表格在处理中
                    if current_type is not None:
                        # 添加当前子表格
                        subtables.append({
                            'type': current_type,
                            'start_row': current_start,
                            'end_row': row_idx
                        })
                        logger.debug(f"添加子表格: 类型={current_type}, 行范围={current_start}-{row_idx}")
                    
                    # 开始新的子表格
                    current_start = row_idx
                    current_type = row_type
            
            # 处理最后一个子表格
            if current_type is not None:
                subtables.append({
                    'type': current_type,
                    'start_row': current_start,
                    'end_row': len(structure)
                })
                logger.debug(f"添加最后一个子表格: 类型={current_type}, 行范围={current_start}-{len(structure)}")
            
            logger.info(f"识别到 {len(subtables)} 个子表格")
            for idx, subtable in enumerate(subtables):
                logger.info(f"子表格 {idx + 1}:")
                logger.info(f"- 类型: {subtable['type']}")
                logger.info(f"- 行范围: {subtable['start_row']}-{subtable['end_row']}")
            
            return subtables
            
        except Exception as e:
            logger.error(f"识别子表格失败: {str(e)}", exc_info=True)
            return []
            
    def _analyze_row_type(self, row: List[Dict[str, Any]]) -> str:
        """分析行类型
        
        Args:
            row: 行数据
            
        Returns:
            str: 行类型
        """
        # 统计行中的单元格类型
        cell_types = {
            'empty': 0,
            'header': 0,
            'data': 0,
            'merged': 0
        }
        
        for cell in row:
            text = cell.get('text', '').strip()
            if not text:
                cell_types['empty'] += 1
            elif cell.get('is_merged', False):
                cell_types['merged'] += 1
            elif self._is_header_cell(text):
                cell_types['header'] += 1
            else:
                cell_types['data'] += 1
                
        # 根据单元格类型分布判断行类型
        total_cells = len(row)
        if cell_types['empty'] / total_cells > 0.8:
            return 'separator'
        elif cell_types['header'] / total_cells > 0.5:
            return 'header'
        else:
            return 'data'
            
    def _is_header_cell(self, text: str) -> bool:
        """判断是否是标题单元格
        
        Args:
            text: 单元格文本
            
        Returns:
            bool: 是否是标题单元格
        """
        # 标题特征
        header_features = [
            '序号',
            '编号',
            '名称',
            '类型',
            '日期',
            '时间',
            '金额',
            '数量',
            '备注'
        ]
        
        # 检查是否包含标题特征
        for feature in header_features:
            if feature in text:
                return True
                
        return False
    
    def _process_subtable(self, table: Table, rows: Tuple[int, int], mode: AnalysisMode) -> Dict[str, Any]:
        """处理子表格
        
        Args:
            table: docx表格对象
            rows: 子表格的行范围
            mode: 处理模式
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            start_row, end_row = rows
            
            # 获取子表格信息
            table_info = self.get_table_info(table, start_row, end_row)
            
            # 使用检测器生成查询
            queries = []
            marked_queries = []
            
            # 处理每一行
            for row_idx in range(start_row, end_row):
                row = table.rows[row_idx]
                for col_idx, cell in enumerate(row.cells):
                    # 获取单元格文本
                    text = cell.text.strip()
                    if not text:
                        continue
                        
                    # 检查是否有注释
                    note = None
                    if '（注：' in text:
                        main_text, note = text.split('（注：', 1)
                        note = note.rstrip('）')
                        text = main_text.strip()
                        
                    # 生成查询
                    query = {
                        'text': text,
                        'row': row_idx,
                        'col': col_idx,
                        'note': note
                    }
                    queries.append(query)
                    
                    # 如果有注释，标记为特殊查询
                    if note:
                        marked_queries.append(query)
            
            return {
                'queries': queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            logger.error(f"处理子表格失败: {str(e)}", exc_info=True)
            return None
    
    def get_table_info(self, table: Table, start_row: int = 0, end_row: int = None) -> Dict[str, Any]:
        """获取表格信息
        
        Args:
            table: docx表格对象
            start_row: 开始行号
            end_row: 结束行号
            
        Returns:
            Dict[str, Any]: 表格信息
        """
        try:
            # 设置结束行号
            if end_row is None:
                end_row = len(table.rows)
                
            # 获取表格结构
            structure = []
            for row_idx in range(start_row, end_row):
                row = table.rows[row_idx]
                row_cells = []
                
                for cell in row.cells:
                    # 获取单元格文本
                    text = cell.text.strip()
                    
                    # 检查是否是合并单元格
                    is_merged = False
                    if hasattr(cell._tc, 'vMerge') and cell._tc.vMerge is not None:
                        is_merged = True
                    elif hasattr(cell._tc, 'gridSpan') and cell._tc.gridSpan is not None:
                        is_merged = True
                        
                    # 添加单元格信息
                    cell_info = {
                        'text': text,
                        'is_merged': is_merged,
                        'is_empty': not text
                    }
                    row_cells.append(cell_info)
                    
                structure.append(row_cells)
                
            # 统计空单元格
            empty_cells = sum(1 for row in structure for cell in row if cell['is_empty'])
            logger.debug(f"找到 {empty_cells} 个空单元格")
            
            return {
                'structure': structure,
                'empty_cells': empty_cells,
                'total_rows': end_row - start_row,
                'total_cols': len(table.rows[0].cells) if table.rows else 0
            }
            
        except Exception as e:
            logger.error(f"获取表格信息失败: {str(e)}", exc_info=True)
            return {
                'structure': [],
                'empty_cells': 0,
                'total_rows': 0,
                'total_cols': 0
            }
    
    def _get_vertical_merge(self, cell: _Cell) -> int:
        """获取单元格垂直合并数"""
        try:
            tc = cell._tc
            if not tc.vMerge:
                return 1
            
            # 计算垂直合并的单元格数
            row = tc.getparent()
            table = row.getparent()
            row_idx = table.tr_lst.index(row)
            span = 1
            
            for next_row in table.tr_lst[row_idx + 1:]:
                next_cell = next_row.tc_lst[table.tr_lst[row_idx].tc_lst.index(tc)]
                if next_cell.vMerge == "continue":
                    span += 1
                else:
                    break
                    
            return span
            
        except Exception:
            return 1
    
    def _is_section_row(self, row) -> bool:
        """判断是否是分隔行
        
        基于以下特征判断:
        1. 单元格合并
        2. 样式特征
        3. 内容特征
        """
        try:
            # 检查是否所有单元格都合并
            all_merged = all(cell._tc.grid_span > 1 for cell in row.cells)
            if all_merged:
                return True
            
            # 检查是否包含分隔标题
            text = extract_text(row).strip()
            if text and any(keyword in text for keyword in ['基本信息', '联系方式', '资质信息']):
                return True
            
            # 检查样式
            if len(row.cells) == 1:
                cell = row.cells[0]
                # 检查背景色等样式特征
                if cell._tc.get_or_add_tcPr().xpath('./w:shd'):
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _is_header_row(self, row) -> bool:
        """判断是否是标题行"""
        try:
            # 检查样式特征
            for cell in row.cells:
                if cell.paragraphs and cell.paragraphs[0].runs:
                    run = cell.paragraphs[0].runs[0]
                    if run.bold or run.font.color:
                        return True
            
            # 检查内容特征
            text = extract_text(row).strip()
            if text and any(char in text for char in [':', '：']):
                return True
            
            return False
            
        except Exception:
            return False
    
    def _analyze_row_hierarchy(self, row) -> Optional[int]:
        """分析行的层次结构"""
        try:
            # 基于缩进判断层次
            if row.cells and row.cells[0].paragraphs:
                paragraph = row.cells[0].paragraphs[0]
                if paragraph.paragraph_format and paragraph.paragraph_format.left_indent:
                    return int(paragraph.paragraph_format.left_indent / 100)  # 假设每级缩进100
            
            return None
            
        except Exception:
            return None
    
    def _analyze_subtable_type(self, table: Table, start_row: int, end_row: int) -> Dict[str, Any]:
        """分析子表格类型
        
        基于以下特征:
        1. 单元格合并模式
        2. 内容模式
        3. 结构特征
        """
        try:
            # 统计特征
            merged_cols = 0
            key_value_pairs = 0
            matrix_patterns = 0
            
            for row_idx in range(start_row, end_row):
                row = table.rows[row_idx]
                
                # 检查合并单元格
                for cell in row.cells:
                    if cell._tc.grid_span > 1:
                        merged_cols += 1
                
                # 检查键值对模式
                text = extract_text(row).strip()
                if ':' in text or '：' in text:
                    key_value_pairs += 1
                
                # 检查矩阵模式
                if len(row.cells) > 2 and all(cell.text.strip() for cell in row.cells):
                    matrix_patterns += 1
            
            # 基于特征判断类型
            total_rows = end_row - start_row
            if total_rows == 0:
                return {'type': TableType.UNKNOWN, 'confidence': 0.0}
            
            # 计算特征比例
            merge_ratio = merged_cols / total_rows
            kv_ratio = key_value_pairs / total_rows
            matrix_ratio = matrix_patterns / total_rows
            
            # 判断类型
            if kv_ratio > 0.6:
                return {'type': TableType.FORM, 'confidence': kv_ratio}
            elif matrix_ratio > 0.4:
                return {'type': TableType.MATRIX, 'confidence': matrix_ratio}
            elif merge_ratio > 0.3:
                return {'type': TableType.COMPLEX, 'confidence': merge_ratio}
            else:
                return {'type': TableType.UNKNOWN, 'confidence': 0.0}
            
        except Exception as e:
            logger.error(f"分析子表格类型失败: {str(e)}", exc_info=True)
            return {'type': TableType.UNKNOWN, 'confidence': 0.0}
            
    def _get_cell_style(self, cell: Any) -> Dict[str, Any]:
        """获取单元格样式"""
        style = {}
        try:
            # 提取字体样式
            if cell._tc.get_or_add_tcPr().xpath('./w:shd'):
                style['background'] = cell._tc.get_or_add_tcPr().xpath('./w:shd')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}fill')
                
            # 提取边框样式
            if cell._tc.get_or_add_tcPr().xpath('./w:tcBorders'):
                borders = cell._tc.get_or_add_tcPr().xpath('./w:tcBorders')[0]
                style['borders'] = {
                    'top': borders.xpath('./w:top')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:top') else None,
                    'bottom': borders.xpath('./w:bottom')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:bottom') else None,
                    'left': borders.xpath('./w:left')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:left') else None,
                    'right': borders.xpath('./w:right')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:right') else None
                }
                
            # 提取文本样式
            if cell.paragraphs and cell.paragraphs[0].runs:
                run = cell.paragraphs[0].runs[0]
                style['font'] = {
                    'bold': run.bold,
                    'italic': run.italic,
                    'color': run.font.color.rgb if run.font.color else None
                }
        except Exception:
            pass
            
        return style
        
    def _get_visual_features(self, table: Any) -> List[Dict[str, Any]]:
        """获取表格的视觉特征"""
        features = []
        
        try:
            # 检查表格边框
            if table._tbl.xpath('.//w:tblBorders'):
                borders = table._tbl.xpath('.//w:tblBorders')[0]
                features.append({
                    'type': 'table_borders',
                    'value': {
                        'top': borders.xpath('./w:top')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:top') else None,
                        'bottom': borders.xpath('./w:bottom')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:bottom') else None
                    }
                })
                
            # 检查表格网格
            if table._tbl.xpath('.//w:tblGrid'):
                features.append({
                    'type': 'table_grid',
                    'value': True
                })
                
            # 检查表格样式
            if table._tbl.xpath('.//w:tblStyle'):
                style = table._tbl.xpath('.//w:tblStyle')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
                features.append({
                    'type': 'table_style',
                    'value': style
                })
        except Exception:
            pass
            
        return features

    def _process_two_dimensional_table(self, table: CT_Tbl):
        """处理二维表格
        
        Args:
            table: docx表格对象
        """
        try:
            logger.info("开始处理二维表格")
            
            # 获取表头
            row_headers = []
            col_headers = []
            
            def get_cell_text(cell):
                text = ""
                for p in cell.p_lst:
                    text += p.text if p.text else ""
                return text.strip()
            
            # 获取第一列作为行标题
            for row in table.tr_lst[1:]:  # 跳过表头行
                if len(row.tc_lst) > 0:
                    cell_text = get_cell_text(row.tc_lst[0])
                    if cell_text:
                        row_headers.append(cell_text)
                        logger.info(f"找到行标题: {cell_text}")
                        
            # 获取第一行作为列标题
            if len(table.tr_lst) > 0:
                for cell in table.tr_lst[0].tc_lst[1:]:  # 跳过第一列
                    cell_text = get_cell_text(cell)
                    if cell_text:
                        col_headers.append(cell_text)
                        logger.info(f"找到列标题: {cell_text}")
                        
            logger.info(f"行标题: {row_headers}")
            logger.info(f"列标题: {col_headers}")
            
            # 生成查询
            for row_idx, row_header in enumerate(row_headers, start=1):
                for col_idx, col_header in enumerate(col_headers, start=1):
                    try:
                        # 生成查询文本
                        query_text = f"{row_header}的{col_header}"
                        logger.info(f"生成查询: {query_text}")
                        
                        # 获取目标单元格
                        cell = table.tr_lst[row_idx].tc_lst[col_idx]
                        cell_text = get_cell_text(cell)
                        
                        # 写入查询
                        write_cell_content(cell, cell_text, query_text)
                        self.total_queries += 1
                        self.total_marked_queries += 1
                        logger.info(f"成功写入查询: {query_text}")
                    except Exception as e:
                        logger.error(f"处理单元格失败: {str(e)}", exc_info=True)
                        continue
                        
            logger.info(f"二维表格处理完成，生成查询 {self.total_queries} 个，标记查询 {self.total_marked_queries} 个\n")
            
        except Exception as e:
            logger.error(f"处理二维表格失败: {str(e)}", exc_info=True)
            
    def _process_complex_table(self, table: CT_Tbl):
        """处理复杂表格
        
        Args:
            table: docx表格对象
        """
        # ... 保持原有的复杂表格处理逻辑 ... 