from docx import Document
from two_column_processor import TwoColumnProcessor
import logging
import sys

def process_document(file_path: str):
    """处理文档中的表格
    
    Args:
        file_path: 文档路径
    """
    try:
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        logger = logging.getLogger(__name__)
        
        print(f"\n开始处理文档: {file_path}")
        
        # 加载文档
        doc = Document(file_path)
        
        # 创建处理器
        processor = TwoColumnProcessor()
        
        # 处理所有表格
        total_queries = []
        total_marked_queries = []
        
        for table_idx, table in enumerate(doc.tables):
            print(f"\n处理第 {table_idx + 1} 个表格:")
            
            # 处理表格
            result = processor.process_table(table._tbl)
            
            # 收集查询
            queries = result['queries']
            marked_queries = result['marked_queries']
            
            print(f"找到 {len(queries)} 个查询")
            print(f"标记了 {len(marked_queries)} 个查询")
            
            total_queries.extend(queries)
            total_marked_queries.extend(marked_queries)
            
        # 保存文档
        output_path = file_path.replace('.docx', '_processed.docx')
        doc.save(output_path)
        
        print("\n处理完成:")
        print(f"总共找到 {len(total_queries)} 个查询")
        print(f"总共标记了 {len(total_marked_queries)} 个查询")
        print(f"处理后的文档已保存到: {output_path}")
        
        # 打印所有查询的详细信息
        print("\n查询详细信息:")
        for idx, query in enumerate(total_queries, 1):
            print(f"\n查询 {idx}:")
            print(f"文本: {query['text']}")
            print(f"类型: {query['type']}")
            
    except Exception as e:
        logger.error(f"处理文档失败: {str(e)}", exc_info=True)
        print(f"处理文档失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python test_two_column.py <文档路径>")
        sys.exit(1)
        
    file_path = sys.argv[1]
    process_document(file_path) 