from docx import Document
from enum import Enum
from typing import List, Tuple, Optional

class TableType(Enum):
    VERTICAL_1D = "vertical_1d"
    HORIZONTAL_1D = "horizontal_1d"
    TWO_DIMENSIONAL = "2d"
    MULTI_ROW = "multi_row"
    UNKNOWN = "unknown"

class TableProcessor:
    def __init__(self, input_path: str, output_path: str):
        self.input_path = input_path
        self.output_path = output_path
        self.doc = Document(input_path)
        
        # 配置项
        self.title_keywords = ["信息", "情况", "说明"]  # 标题特征词
        self.chinese_numbers = "一二三四五六七八九十"  # 中文数字
        self.number_endings = ["、", "．", ".", ") ", "）", "]", "】"]  # 序号后缀

    def is_empty_cell(self, cell) -> bool:
        """检查单元格是否为空"""
        return not cell.text.strip()

    def is_query_text(self, text: str) -> bool:
        """检查文本是否已经是查询格式"""
        text = text.strip()
        return text.startswith("{查询：") and text.endswith("}")

    def clean_cell_text(self, text: str) -> str:
        """清理单元格文本，去除已有的查询标记"""
        text = text.strip()
        while self.is_query_text(text):
            text = text[4:-1].strip()  # 移除 "{查询：" 和 "}"
        return text

    def is_question_cell(self, cell) -> bool:
        """检查单元格是否为问题单元格"""
        text = cell.text.strip()
        if not text or self.is_query_text(text):
            return False
            
        # 检查单元格文本是否包含标题特征
        if any(keyword in text for keyword in self.title_keywords):
            # 检查是否以序号开头
            if (text[0].isdigit() or 
                any(text.startswith(ch) for ch in self.chinese_numbers)):
                return False
        return True

    def is_answer_cell(self, cell) -> bool:
        """检查单元格是否为答案单元格（空白的就是答案）"""
        return self.is_empty_cell(cell)

    def is_title_row(self, row) -> bool:
        """检查是否是标题行（整行合并或单单元格）"""
        # 检查是否是合并单元格
        is_merged = (len(row.cells) == 1 or 
                    any(cell._tc.get_or_add_tcPr().xpath(".//w:gridSpan") 
                        for cell in row.cells))
        
        # 获取行的文本内容
        row_text = " ".join(cell.text.strip() for cell in row.cells)
        
        # 检查是否包含标题特征词
        has_title_keywords = any(keyword in row_text for keyword in self.title_keywords)
        
        # 检查是否以序号开头（如"一、"，"1、"等）
        starts_with_number = bool(row_text.strip() and 
                                (row_text[0].isdigit() or 
                                 any(row_text.startswith(ch) for ch in self.chinese_numbers)))
        
        # 如果是合并单元格且包含标题特征，或者以序号开头且包含标题特征，则认为是标题行
        return (is_merged and has_title_keywords) or (starts_with_number and has_title_keywords)

    def get_merged_cell_text(self, table, row_idx: int, col_idx: int) -> str:
        """获取合并单元格的文本，处理跨行合并的情况"""
        try:
            cell = table.rows[row_idx].cells[col_idx]
            # 检查垂直合并
            if cell._tc.get_or_add_tcPr().xpath(".//w:vMerge"):
                # 向上查找合并单元格的起始位置
                current_row = row_idx
                while current_row >= 0:
                    current_cell = table.rows[current_row].cells[col_idx]
                    if current_cell._tc.get_or_add_tcPr().xpath(".//w:vMerge[@w:val='restart']"):
                        return self.clean_cell_text(current_cell.text)
                    current_row -= 1
            # 检查水平合并
            elif cell._tc.get_or_add_tcPr().xpath(".//w:gridSpan"):
                return self.clean_cell_text(cell.text)
            return self.clean_cell_text(cell.text)
        except:
            return ""

    def get_cell_above(self, table, row_idx: int, col_idx: int) -> Optional[object]:
        """获取上方单元格"""
        if row_idx > 0:
            return table.rows[row_idx - 1].cells[col_idx]
        return None

    def get_cell_below(self, table, row_idx: int, col_idx: int) -> Optional[object]:
        """获取下方单元格"""
        if row_idx < len(table.rows) - 1:
            return table.rows[row_idx + 1].cells[col_idx]
        return None

    def get_cell_left(self, table, row_idx: int, col_idx: int) -> Optional[object]:
        """获取左侧单元格"""
        if col_idx > 0:
            return table.rows[row_idx].cells[col_idx - 1]
        return None

    def get_cell_right(self, table, row_idx: int, col_idx: int) -> Optional[object]:
        """获取右侧单元格"""
        if col_idx < len(table.rows[row_idx].cells) - 1:
            return table.rows[row_idx].cells[col_idx + 1]
        return None

    def find_question_above(self, table, row_idx: int, col_idx: int, stop_at_title: bool = True) -> Optional[str]:
        """向上查找问题，可选择是否在遇到标题行时停止"""
        current_row = row_idx - 1  # 从上一行开始
        while current_row >= 0:
            if stop_at_title and self.is_title_row(table.rows[current_row]):
                break
            cell = table.rows[current_row].cells[col_idx]
            if self.is_question_cell(cell):
                return self.clean_cell_text(cell.text)
            current_row -= 1
        return None

    def find_question_left(self, table, row_idx: int, col_idx: int) -> Optional[str]:
        """向左查找问题"""
        current_col = col_idx - 1  # 从左边一列开始
        while current_col >= 0:
            cell = table.rows[row_idx].cells[current_col]
            if self.is_question_cell(cell):
                return self.clean_cell_text(cell.text)
            current_col -= 1
        return None

    def find_column_header(self, table, row_idx: int, col_idx: int) -> Optional[str]:
        """查找列标题"""
        current_row = row_idx
        while current_row >= 0:
            if self.is_title_row(table.rows[current_row]):
                current_row -= 1
                continue
                
            cell = table.rows[current_row].cells[col_idx]
            header_text = self.clean_cell_text(cell.text)
            if header_text and not self.is_empty_cell(cell):
                return header_text
            current_row -= 1
        return None

    def find_row_header(self, table, row_idx: int, col_idx: int) -> Optional[str]:
        """查找行标题，包括处理合并单元格的情况"""
        if self.is_title_row(table.rows[row_idx]):
            return None

        # 获取可能的合并单元格文本
        merged_text = self.get_merged_cell_text(table, row_idx, 0)
        if merged_text:
            return merged_text

        # 常规查找行标题
        current_col = col_idx
        while current_col >= 0:
            cell = table.rows[row_idx].cells[current_col]
            if self.is_question_cell(cell):
                return self.clean_cell_text(cell.text)
            current_col -= 1
        return None

    def determine_table_type(self, table, row_idx: int, col_idx: int) -> TableType:
        """根据四种情况判断表格类型"""
        if not self.is_empty_cell(cell := table.rows[row_idx].cells[col_idx]):
            return TableType.UNKNOWN

        above_q = self.find_question_above(table, row_idx, col_idx)
        left_q = self.find_question_left(table, row_idx, col_idx)
        
        # 情况四：多行填写表格
        # 检查是否是第一列且上方有问题
        if col_idx == 0 and above_q:
            return TableType.MULTI_ROW
            
        # 检查是否是后续列
        if col_idx > 0:
            # 获取当前行之前的所有单元格
            prev_cells = [table.rows[row_idx].cells[i] for i in range(col_idx)]
            # 检查是否所有前面的单元格都是答案或空白
            all_prev_answers = all(self.is_empty_cell(cell) or not self.is_question_cell(cell) 
                                 for cell in prev_cells)
            if all_prev_answers and above_q:
                return TableType.MULTI_ROW

        # 情况二：一维横向表格
        if above_q and not left_q:
            # 检查下方是否有问题或是表格边界
            below_is_q = (row_idx < len(table.rows) - 1 and 
                         self.is_question_cell(table.rows[row_idx + 1].cells[col_idx]))
            below_is_border = (row_idx == len(table.rows) - 1)
            
            if below_is_q or below_is_border:
                return TableType.HORIZONTAL_1D

        # 情况三：二维表格
        if above_q and left_q:
            return TableType.TWO_DIMENSIONAL
        
        # 向上查找直到找到问题
        above_q_full = self.find_question_above(table, row_idx, col_idx, stop_at_title=False)
        left_q_full = None
        if not left_q:
            # 向左查找直到找到问题
            current_col = col_idx - 1
            while current_col >= 0:
                if self.is_question_cell(table.rows[row_idx].cells[current_col]):
                    left_q_full = self.clean_cell_text(table.rows[row_idx].cells[current_col].text)
                    break
                current_col -= 1

        if above_q_full and left_q_full:
            return TableType.TWO_DIMENSIONAL

        # 情况一：一维纵向表格（默认情况）
        if left_q:
            return TableType.VERTICAL_1D

        return TableType.UNKNOWN

    def find_first_row_question(self, table, col_idx: int) -> Optional[str]:
        """查找第一行的问题（表头）"""
        for row_idx in range(len(table.rows)):
            if self.is_title_row(table.rows[row_idx]):
                continue
            cell = table.rows[row_idx].cells[col_idx]
            if self.is_question_cell(cell):
                return self.clean_cell_text(cell.text)
        return None

    def find_header_row(self, table, current_row: int) -> Optional[int]:
        """查找表头行"""
        row = current_row
        while row >= 0:
            if any(cell.text.strip() in ["职务", "姓名", "部门", "部门负责人"] 
                   for cell in table.rows[row].cells):
                return row
            row -= 1
        return None

    def get_previous_answers(self, table, row_idx: int, col_idx: int) -> List[str]:
        """获取同一行之前的答案"""
        answers = []
        for i in range(col_idx):
            cell = table.rows[row_idx].cells[i]
            if not self.is_empty_cell(cell) and not self.is_question_cell(cell):
                answers.append(self.clean_cell_text(cell.text))
        return answers

    def generate_query_text(self, table, row_idx: int, col_idx: int, table_type: TableType) -> str:
        """根据表格类型生成查询文本"""
        if table_type == TableType.VERTICAL_1D:
            q = self.find_question_left(table, row_idx, col_idx)
            return f"{{查询：{q}}}" if q else "{待填写}"

        elif table_type == TableType.HORIZONTAL_1D:
            q = self.find_question_above(table, row_idx, col_idx)
            return f"{{查询：{q}}}" if q else "{待填写}"

        elif table_type == TableType.TWO_DIMENSIONAL:
            q1 = self.find_question_left(table, row_idx, col_idx) or self.find_question_left(table, row_idx, col_idx)
            q2 = self.find_question_above(table, row_idx, col_idx) or self.find_question_above(table, row_idx, col_idx, stop_at_title=False)
            if q1 and q2:
                return f"{{查询：{q1}{q2}}}"
            return "{待填写}"

        elif table_type == TableType.MULTI_ROW:
            # 不管是第一列还是后续列，都只使用第一行的问题
            q = self.find_first_row_question(table, col_idx)
            return f"{{查询：{q}}}" if q else "{待填写}"

        return "{待填写}"
    
    def process_table(self, table):
        """处理单个表格"""
        # 首先处理嵌套表格
        for row in table.rows:
            for cell in row.cells:
                if cell.tables:
                    for nested_table in cell.tables:
                        self.process_table(nested_table)
        
        # 然后处理当前表格
        for row_idx, row in enumerate(table.rows):
            if self.is_title_row(row):
                continue
            
            # 跳过表头行
            if self.find_header_row(table, row_idx) == row_idx:
                continue
                
            for col_idx, cell in enumerate(row.cells):
                if cell.tables:  # 跳过包含嵌套表格的单元格
                    continue
                    
                if self.is_empty_cell(cell):
                    table_type = self.determine_table_type(table, row_idx, col_idx)
                    query_text = self.generate_query_text(table, row_idx, col_idx, table_type)
                    cell.text = query_text

    def process(self):
        """处理文档中的所有表格"""
        for table in self.doc.tables:
            self.process_table(table)
        self.doc.save(self.output_path)
        # print(f"已生成高级标记文档: {self.output_path}")

if __name__ == "__main__":
    input_file = "尽职调查.docx"
    output_file = "尽职调查_高级标记后.docx"
    processor = TableProcessor(input_file, output_file)
    processor.process()
