from dataclasses import dataclass
from typing import Tuple
from enum import Enum
from typing import Dict, Any, Optional

@dataclass
class QueryInfo:
    """查询信息"""
    query: str  # 查询文本
    answer_position: Tuple[int, int]  # 答案位置 (行号, 列号) 

class TableProcessor:
    def __init__(self):
        pass

class TableProcessMode(Enum):
    BASIC = "BASIC"  # 基础处理模式
    BALANCED = "BALANCED"  # 平衡处理模式
    COMPLETE = "COMPLETE"  # 完整处理模式
    QUERY_ONLY = "QUERY_ONLY"  # 仅查询模式 