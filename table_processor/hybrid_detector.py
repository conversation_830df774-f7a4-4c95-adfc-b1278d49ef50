from typing import Dict, List, Any, Tuple, Optional
import re
from datetime import datetime
from .table_types import TableType, AnswerPosition, TableAnalysisResult, QueryInfo, AnalysisMode
from .llm_handler import LLMHandler
import jieba
import json
import os
import logging
from docx.table import Table
from docx.text.paragraph import Paragraph
from docx.oxml.table import CT_Tbl
from .config import TABLE_TYPE_CONFIG, QUERY_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.ERROR,  # 只输出错误信息
    format='%(message)s',
    handlers=[
        logging.FileHandler('table_processing.log', mode='w', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class HybridTableDetector:
    """混合表格检测器，结合规则和LLM"""
    
    def __init__(self, llm_handler: Optional[LLMHandler] = None, mode: AnalysisMode = AnalysisMode.BALANCED):
        self.llm_handler = llm_handler or LLMHandler()
        self.mode = mode
        self.confidence_threshold = AnalysisMode.get_confidence_threshold(mode)
        
        # 从配置文件加载关键词和模式
        self.load_patterns()
        
        # 初始化分词器
        jieba.initialize()
        
        logger.info(f"初始化混合表格检测器，模式: {mode.name}")
        
    def load_patterns(self):
        """从配置文件加载模式和关键词"""
        config_path = os.path.join(os.path.dirname(__file__), 'patterns.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                patterns = json.load(f)
                self.type_keywords = patterns.get('type_keywords', {})
                self.year_patterns = patterns.get('year_patterns', [])
                self.hint_patterns = patterns.get('hint_patterns', {})
                self.location_patterns = patterns.get('location_patterns', [])
                self.query_templates = patterns.get('query_templates', {})
        except FileNotFoundError:
            # 使用默认配置
            self.type_keywords = {}  # 改为空字典，完全依赖LLM判断
            self.year_patterns = []  # 年份模式也由LLM判断
            self.hint_patterns = {
                'symbols': ['□', '○', '●', '▢', '▣', '☐', '☑', '✓', '✔'],
                'prefixes': ['例如', '如：', '（', '('],
                'suffixes': ['等', '等等', '...', '……']
            }
            
    def _analyze_text_similarity(self, text1: str, text2: str) -> float:
        """分析文本相似度"""
        if not text1 or not text2:
            return 0.0
            
        words1 = set(jieba.cut(text1))
        words2 = set(jieba.cut(text2))
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
        
    def _extract_patterns(self, text: str) -> Dict[str, List[str]]:
        """提取文本中的模式"""
        patterns = {
            'years': [],
            'locations': [],
            'numbers': [],
            'keywords': []
        }
        
        # 提取年份
        for pattern in self.year_patterns:
            matches = re.finditer(pattern, text)
            patterns['years'].extend(match.group() for match in matches)
            
        # 提取地理位置
        for pattern in self.location_patterns:
            matches = re.finditer(pattern, text)
            patterns['locations'].extend(match.group() for match in matches)
            
        # 提取数字
        number_pattern = r'\d+\.?\d*'
        patterns['numbers'] = re.findall(number_pattern, text)
        
        # 提取关键词
        words = jieba.cut(text)
        for word in words:
            for type_keywords in self.type_keywords.values():
                if word in type_keywords:
                    patterns['keywords'].append(word)
                    
        return patterns
        
    def _is_empty_cell(self, cell: Any) -> bool:
        """检查单元格是否为空
        
        Args:
            cell: 单元格对象，可以是字典或其他类型
        """
        if isinstance(cell, dict):
            text = cell.get('text', '')
        elif hasattr(cell, 'text'):
            text = cell.text
        else:
            text = str(cell)
            
        if not isinstance(text, str):
            return True
            
        text = text.strip()
        if not text:
            return True
            
        # 检查是否只包含提示符号
        if text in self.hint_patterns['symbols']:
            return True
            
        # 检查是否是提示文本
        for prefix in self.hint_patterns['prefixes']:
            if text.startswith(prefix):
                return True
                
        for suffix in self.hint_patterns['suffixes']:
            if text.endswith(suffix):
                return True
                
        # 检查是否是短提示文本
        if len(text) < 2:
            return True
            
        return False
        
    def _analyze_column_patterns(self, structure: List[List[Dict]], col_idx: int) -> Dict[str, Any]:
        """分析列的模式"""
        patterns = {
            'empty_ratio': 0,
            'year_ratio': 0,
            'location_ratio': 0,
            'number_ratio': 0,
            'keyword_ratio': 0
        }
        
        total_cells = len(structure) - 1  # 排除表头
        if total_cells <= 0:
            return patterns
            
        empty_count = 0
        year_count = 0
        location_count = 0
        number_count = 0
        keyword_count = 0
        
        for row_idx in range(1, len(structure)):
            cell_text = structure[row_idx][col_idx].get('text', '').strip()
            
            if self._is_empty_cell({'text': cell_text}):
                empty_count += 1
                continue
                
            extracted = self._extract_patterns(cell_text)
            if extracted['years']:
                year_count += 1
            if extracted['locations']:
                location_count += 1
            if extracted['numbers']:
                number_count += 1
            if extracted['keywords']:
                keyword_count += 1
                
        patterns['empty_ratio'] = empty_count / total_cells
        patterns['year_ratio'] = year_count / total_cells
        patterns['location_ratio'] = location_count / total_cells
        patterns['number_ratio'] = number_count / total_cells
        patterns['keyword_ratio'] = keyword_count / total_cells
        
        return patterns

    def analyze_table(self, table_info: Dict[str, Any]) -> TableAnalysisResult:
        """分析表格类型和结构"""
        structure = table_info.get('table_structure', [])
        if not structure:
            return TableAnalysisResult(TableType.UNKNOWN, 0.5)
            
        # 首先使用规则分析
        rule_analysis = self._analyze_table_by_rules(structure)
        
        # 如果规则分析的置信度足够高，直接返回结果
        if rule_analysis.confidence >= 0.8:
            return rule_analysis
            
        # 如果是基础模式，直接返回规则分析结果
        if self.mode == AnalysisMode.BASIC:
            return rule_analysis
            
        # 如果是平衡模式，只在规则分析不确定时使用LLM
        if self.mode == AnalysisMode.BALANCED:
            # 如果规则分析置信度较高，直接返回
            if rule_analysis.confidence >= 0.6:
                return rule_analysis
                
            # 如果规则分析置信度太低，标记为未知
            if rule_analysis.confidence < 0.3:
                return TableAnalysisResult(TableType.UNKNOWN, 0.0)
                
        # 否则尝试使用LLM分析（仅在平衡和完整模式下）
        try:
            # 优化：只对较小的表格使用LLM分析
            if len(structure) > 10 or len(structure[0]) > 6:
                logger.info("表格过大，跳过LLM分析")
                return rule_analysis
                
            llm_analysis = self._analyze_table_with_llm(structure)
            if llm_analysis and llm_analysis.confidence > rule_analysis.confidence:
                result = llm_analysis
            else:
                result = rule_analysis
        except Exception as e:
            logger.warning(f"LLM分析失败，使用规则分析结果: {str(e)}")
            result = rule_analysis
            
        # 过滤不支持的表格类型
        if not result.is_supported_type(self.mode):
            result.table_type = TableType.UNKNOWN
            result.confidence = 0.0
            
        return result
        
    def _analyze_table_with_llm(self, structure: List[List[Dict]]) -> Optional[TableAnalysisResult]:
        """使用LLM分析表格结构"""
        try:
            # 构建提示
            prompt = "你是一个专业的表格分析助手。请分析下面的表格结构，并返回指定格式的JSON结果。\n\n"
            
            # 添加表格内容
            prompt += "表格内容：\n"
            for row in structure:
                row_text = [f"'{cell.get('text', '').strip()}'" for cell in row]
                prompt += " | ".join(row_text) + "\n"
            
            prompt += "\n分析要求：\n"
            prompt += "1. 判断表格类型：\n"
            prompt += "   - 如果每行第一列是字段名，后面是对应的值，判断为'一维表格'\n"
            prompt += "   - 如果有行标题和列标题，数据在交叉点，判断为'二维表格'\n"
            prompt += "   - 其他情况判断为'其他'\n"
            prompt += "2. 识别表格标题（如果有）\n"
            prompt += "3. 判断表格是否分为多个部分，记录每个部分的起止行号\n\n"
            
            prompt += "请严格按照以下JSON格式返回结果：\n"
            prompt += "{\n"
            prompt += '  "type": "一维表格",  // 或 "二维表格" 或 "其他"\n'
            prompt += '  "title": "表格标题",  // 如果没有标题则返回空字符串\n'
            prompt += '  "sections": [  // 表格分区信息\n'
            prompt += '    {\n'
            prompt += '      "title": "分区标题",  // 分区标题\n'
            prompt += '      "start": 0,  // 起始行号（从0开始）\n'
            prompt += '      "end": 5     // 结束行号\n'
            prompt += '    }\n'
            prompt += '  ],\n'
            prompt += '  "confidence": 0.9  // 分析结果的置信度，0.0-1.0\n'
            prompt += "}\n\n"
            prompt += "注意：请确保返回的是合法的JSON格式，不要添加任何额外的说明文字。\n"
            
            # 调用LLM
            response = self.llm_handler.analyze_table_structure(prompt)
            if not response:
                logger.warning("LLM返回空响应")
                return None
                
            # 解析LLM响应
            table_type = TableType.UNKNOWN
            if response.get('type') == '一维表格':
                table_type = TableType.FORM
            elif response.get('type') == '二维表格':
                table_type = TableType.MATRIX
            
            confidence = float(response.get('confidence', 0.5))
            logger.info(f"表格类型判断结果: {response.get('type')} (置信度: {confidence})")
            
            return TableAnalysisResult(
                table_type=table_type,
                confidence=confidence,
                metadata={
                    'sections': response.get('sections', []),
                    'title': response.get('title', ''),
                    'type_detail': response.get('type', 'unknown')
                }
            )
            
        except Exception as e:
            logger.error(f"LLM分析失败: {str(e)}", exc_info=True)
            return None
            
    def extract_queries(self, table_info: Dict[str, Any], analysis: TableAnalysisResult) -> List[QueryInfo]:
        """提取查询信息"""
        try:
            queries = []
            structure = table_info.get('table_structure', [])
            empty_cells = table_info.get('empty_cells', set())
            
            if not structure or len(structure) < 2:
                return []
                
            if analysis.table_type == TableType.FORM:
                # 表单型表格：第一列是字段名
                for row_idx, row in enumerate(structure):
                    if row_idx == 0:  # 跳过表头
                        continue
                    # 检查第二列是否为空
                    if len(row) > 1 and (row_idx, 1) in empty_cells:
                        field_name = row[0].strip()
                        if field_name:
                            query = QueryInfo(
                                query=field_name,
                                answer_position=(row_idx, 1)  # 答案在第二列
                            )
                            queries.append(query)
                            
            elif analysis.table_type == TableType.MATRIX:
                # 二维表格：结合行列标题
                for row_idx, row in enumerate(structure):
                    if row_idx == 0:  # 跳过表头
                        continue
                    for col_idx, cell in enumerate(row):
                        if col_idx == 0:  # 跳过行标题
                            continue
                        # 只处理空单元格
                        if (row_idx, col_idx) in empty_cells:
                            row_header = row[0].strip()
                            col_header = structure[0][col_idx].strip()
                            if row_header and col_header:
                                query = QueryInfo(
                                    query=f"{row_header}的{col_header}是什么？",
                                    answer_position=(row_idx, col_idx)
                                )
                                queries.append(query)
                                
            elif analysis.table_type == TableType.COMPLEX:
                # 复杂表格：只考虑基本和平衡模式
                for row_idx, row in enumerate(structure):
                    for col_idx, cell in enumerate(row):
                        # 只处理空单元格
                        if (row_idx, col_idx) in empty_cells:
                            # 尝试找到相关的行列标题
                            row_header = row[0].strip() if col_idx > 0 else ""
                            col_header = structure[0][col_idx].strip() if row_idx > 0 else ""
                            
                            if row_header or col_header:
                                query_text = ""
                                if row_header and col_header:
                                    query_text = f"{row_header}的{col_header}是什么？"
                                elif row_header:
                                    query_text = f"{row_header}是什么？"
                                elif col_header:
                                    query_text = f"{col_header}是什么？"
                                    
                                if query_text:
                                    query = QueryInfo(
                                        query=query_text,
                                        answer_position=(row_idx, col_idx)
                                    )
                                    queries.append(query)
            
            return queries
            
        except Exception as e:
            print(f"提取查询时发生错误: {str(e)}")
            return []
        
    def _generate_basic_queries(self, table_info: Dict[str, Any]) -> List[QueryInfo]:
        """使用规则生成基本查询"""
        queries = []
        structure = table_info.get('table_structure', [])
        
        if not structure or len(structure) < 2:
            return []
            
        # 获取表头（如果有）
        headers = [cell.get('text', '').strip() for cell in structure[0]]
        
        # 遍历每个空单元格
        for row_idx in range(1, len(structure)):
            row = structure[row_idx]
            for col_idx in range(len(row)):
                cell = row[col_idx]
                if not cell.get('text', '').strip():
                    # 获取字段名（通常在第一列）
                    field_name = ""
                    if col_idx > 0 and row_idx < len(structure):
                        field_name = structure[row_idx][0].get('text', '').strip()
                    
                    # 获取列标题
                    col_header = headers[col_idx] if col_idx < len(headers) else ""
                    
                    # 构建查询文本
                    query_text = ""
                    if field_name and col_header:
                        query_text = f"{field_name}的{col_header}"
                    elif field_name:
                        query_text = field_name.rstrip('：:')
                    elif col_header:
                        query_text = col_header
                        
                    if query_text:
                        # 清理查询文本
                        query_text = self._clean_query_text(query_text)
                        if query_text:  # 如果清理后不为空
                            queries.append(QueryInfo(
                                text=query_text,
                                row=row_idx,
                                col=col_idx,
                                answer_positions=[{
                                    'row': row_idx,
                                    'col': col_idx,
                                    'context': self._get_cell_context(structure, row_idx, col_idx)
                                }],
                                is_multi_row=False,
                                confidence=0.8
                            ))
                            
        return queries
        
    def _is_valid_position(self, structure: List[List[Dict]], row: int, col: int) -> bool:
        """检查位置是否有效"""
        return (0 <= row < len(structure) and 
                0 <= col < len(structure[row]) and 
                not structure[row][col].get('text', '').strip())
                
    def _clean_query_text(self, text: str) -> str:
        """清理查询文本，去除不必要的标题等"""
        if not text:
            return ""
            
        # 移除查询前缀
        text = text.replace('查询：', '')
        text = text.replace('查询:', '')
        
        # 处理换行符，将\n替换为实际的换行符
        text = text.replace('\\n', '\n')
        
        # 移除"工商注册基本信息的一、管理人基本情况"等完整路径
        text = re.sub(r'[^、]*的[一二三四五六七八九十]、[^、]+?的', '', text)
        
        # 移除"一、管理人基本情况"等标题
        text = re.sub(r'[一二三四五六七八九十]、[^、]+?(?=的|$)', '', text)
        
        # 移除开头的"工商注册基本信息的"等前缀
        text = re.sub(r'^[^、]+?的', '', text)
        
        # 移除多余的标点
        text = text.strip('、，。：')
        
        # 如果以"一、"等开头，直接移除
        text = re.sub(r'^[一二三四五六七八九十]、', '', text)
        
        # 移除纯数字标题
        text = re.sub(r'^\d+\.?\s*', '', text)
        
        # 移除多余的"的"
        text = re.sub(r'的+', '的', text)
        text = text.strip('的')
        
        # 移除括号中的内容（除非是注释）
        if not text.startswith('（注：') and not text.startswith('(注:'):
            text = re.sub(r'\（[^）]*\）', '', text)
            text = re.sub(r'\([^)]*\)', '', text)
        
        # 移除特殊字符
        text = text.replace('/', '')
        text = text.replace('\\', '')
        text = text.replace('|', '')
        
        # 移除空格
        text = text.strip()
        
        return text
        
    def _get_cell_context(self, structure: List[List[Dict]], row: int, col: int) -> List[str]:
        """获取单元格的上下文"""
        context = []
        
        # 获取上方单元格
        if row > 0:
            context.append(structure[row-1][col].get('text', '').strip())
            
        # 获取左侧单元格
        if col > 0:
            context.append(structure[row][col-1].get('text', '').strip())
            
        # 获取右侧单元格
        if col < len(structure[row]) - 1:
            context.append(structure[row][col+1].get('text', '').strip())
            
        # 获取下方单元格
        if row < len(structure) - 1:
            context.append(structure[row+1][col].get('text', '').strip())
            
        # 过滤掉空字符串
        return [c for c in context if c]

    def _get_note_text(self, structure: List[List[Dict]], row: int, col: int) -> str:
        """获取单元格相关的注释文本"""
        # 检查下一行是否存在且是注释
        if row + 1 < len(structure):
            next_row = structure[row + 1]
            if col < len(next_row):
                cell_text = next_row[col].get('text', '').strip()
                if cell_text.startswith('(注:'):
                    return cell_text
                    
        # 检查当前行的下一列是否是注释
        if col + 1 < len(structure[row]):
            cell_text = structure[row][col + 1].get('text', '').strip()
            if cell_text.startswith('(注:'):
                return cell_text
                
        return ""

    def _analyze_table_by_rules(self, structure: List[List[Dict[str, Any]]]) -> TableAnalysisResult:
        """使用规则分析表格类型"""
        # 首先检查是否是表单类型（两列表格）
        if len(structure[0]) == 2:  # 检查是否是两列
            first_col_titles = True
            for row in structure:
                title = row[0].get('text', '').strip()
                if not title or not title.endswith(QUERY_CONFIG['form_field_suffix']):
                    first_col_titles = False
                    break
            
            if first_col_titles:
                return TableAnalysisResult(TableType.FORM, 0.9)
        
        # 获取表格特征
        features = self._detect_table_features(structure)
        
        # 计算各种类型的分数
        form_score = self._calc_form_score(structure, features)
        matrix_score = self._calc_matrix_score(structure, features)
        complex_score = self._calc_complex_score(structure, features)
        
        # 选择得分最高的类型
        scores = {
            TableType.FORM: form_score,
            TableType.MATRIX: matrix_score,
            TableType.COMPLEX: complex_score
        }
        
        table_type = max(scores.items(), key=lambda x: x[1])[0]
        confidence = scores[table_type]
        
        return TableAnalysisResult(table_type, confidence)

    def _detect_table_features(self, structure: List[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """检测表格特征
        
        Args:
            structure: 表格结构
            
        Returns:
            Dict[str, Any]: 表格特征
        """
        if not structure:
            return {}
            
        rows = len(structure)
        cols = len(structure[0])
        
        # 计算空单元格比例
        empty_cells = sum(1 for row in structure for cell in row if not cell['text'])
        empty_ratio = empty_cells / (rows * cols) if rows * cols > 0 else 0
        
        # 检测是否有合并单元格
        merged_cells = sum(1 for row in structure for cell in row if cell['merged'])
        merged_ratio = merged_cells / (rows * cols) if rows * cols > 0 else 0
        
        # 检测数据类型
        numeric_cells = 0
        text_cells = 0
        for row in structure:
            for cell in row:
                cell_text = cell['text']
                if cell_text:
                    if self._is_numeric(cell_text):
                        numeric_cells += 1
                    else:
                        text_cells += 1
                        
        total_cells = rows * cols
        numeric_ratio = numeric_cells / total_cells if total_cells > 0 else 0
        text_ratio = text_cells / total_cells if total_cells > 0 else 0
        
        # 检测行列标题
        has_row_headers = all(bool(row[0]['text']) for row in structure[1:]) if cols > 0 else False
        has_col_headers = all(bool(cell['text']) for cell in structure[0]) if rows > 0 else False
        
        return {
            'rows': rows,
            'cols': cols,
            'empty_ratio': empty_ratio,
            'merged_ratio': merged_ratio,
            'numeric_ratio': numeric_ratio,
            'text_ratio': text_ratio,
            'has_row_headers': has_row_headers,
            'has_col_headers': has_col_headers
        }
        
    def _calc_form_score(self, structure: List[List[Dict[str, Any]]], features: Dict[str, Any]) -> float:
        """计算表单类型的分数"""
        if not structure or len(structure) < 2:
            return 0.0
            
        # 检查是否是两列表格
        if len(structure[0]) == 2:
            # 检查第一列是否都是标题
            first_col_titles = True
            for row in structure:
                if not row[0].get('text', '').strip().endswith('：'):
                    first_col_titles = False
                    break
            
            if first_col_titles:
                return 0.9  # 如果第一列都是标题，给出很高的分数
            
        # 原有的评分逻辑
        score = 0.0
        
        # 检查行标题特征
        if features.get('has_row_titles', False):
            score += 0.3
            
        # 检查数据单元格特征
        if features.get('data_cells_ratio', 0) > 0.5:
            score += 0.2
            
        # 检查表格规整性
        if features.get('is_regular', False):
            score += 0.2
            
        # 检查空单元格比例
        empty_ratio = features.get('empty_cells_ratio', 0)
        if empty_ratio < 0.3:
            score += 0.2
            
        return min(score, 1.0)
        
    def _calc_matrix_score(self, structure: List[List[Dict[str, Any]]], features: Dict[str, Any]) -> float:
        """计算矩阵类型的分数"""
        if not structure or len(structure) < 2:
            return 0.0
            
        # 如果是两列表格，不太可能是矩阵
        if len(structure[0]) == 2:
            return 0.1
            
        score = 0.0
        
        # 检查行标题和列标题
        if features.get('has_row_headers', False) and features.get('has_col_headers', False):
            score += 0.4
            
        # 检查数据单元格特征
        if features.get('numeric_ratio', 0) > 0.3:
            score += 0.3
            
        # 检查表格规整性
        if features.get('is_regular', False):
            score += 0.3
            
        return min(score, 1.0)
        
    def _calc_complex_score(self, structure: List[List[Dict[str, Any]]], features: Dict[str, Any]) -> float:
        """计算复杂表格的分数"""
        if not structure or len(structure) < 3:
            return 0.0
            
        score = 0.0
        
        # 检查合并单元格
        if features.get('merged_ratio', 0) > 0.2:
            score += 0.4
            
        # 检查不规整性
        if not features.get('is_regular', True):
            score += 0.3
            
        # 检查空单元格比例
        empty_ratio = features.get('empty_cells_ratio', 0)
        if empty_ratio > 0.3:
            score += 0.3
            
        return min(score, 1.0)

    def _analyze_table_structure(self, structure: List[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """分析表格结构
        
        Args:
            structure: 表格结构
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        if not structure:
            return {}
            
        # 获取表格维度
        rows = len(structure)
        cols = len(structure[0])
        
        # 分析行类型
        row_types = []
        for row in structure:
            # 计算行中的空单元格比例
            empty_cells = sum(1 for cell in row if not cell['text'])
            empty_ratio = empty_cells / cols if cols > 0 else 0
            
            # 计算行中的数值单元格比例
            numeric_cells = sum(1 for cell in row if cell['text'] and self._is_numeric(cell['text']))
            numeric_ratio = numeric_cells / cols if cols > 0 else 0
            
            # 判断行类型
            if empty_ratio > 0.8:
                row_types.append('separator')
            elif numeric_ratio > 0.5:
                row_types.append('data')
            elif row[0]['text'] and all(not cell['text'] for cell in row[1:]):
                row_types.append('header')
            else:
                row_types.append('data')
                
        # 分析列类型
        col_types = []
        for col_idx in range(cols):
            # 计算列中的空单元格比例
            empty_cells = sum(1 for row in structure if not row[col_idx]['text'])
            empty_ratio = empty_cells / rows if rows > 0 else 0
            
            # 计算列中的数值单元格比例
            numeric_cells = sum(1 for row in structure if row[col_idx]['text'] and self._is_numeric(row[col_idx]['text']))
            numeric_ratio = numeric_cells / rows if rows > 0 else 0
            
            # 判断列类型
            if empty_ratio > 0.8:
                col_types.append('separator')
            elif numeric_ratio > 0.5:
                col_types.append('data')
            elif all(row[col_idx]['text'] for row in structure[:1]):
                col_types.append('header')
            else:
                col_types.append('data')
                
        # 检查是否有分隔行
        has_separators = any(t == 'separator' for t in row_types)
        
        # 检查是否有嵌套结构
        has_nested_structure = False
        data_sections = 0
        in_data_section = False
        for row_type in row_types:
            if row_type == 'data':
                if not in_data_section:
                    data_sections += 1
                    in_data_section = True
            else:
                in_data_section = False
        has_nested_structure = data_sections > 1
        
        # 检查是否是规则的表格结构
        is_regular = (
            row_types.count('header') <= 2 and  # 最多两个表头
            row_types.count('separator') <= 2 and  # 最多两个分隔行
            all(t != 'separator' for t in col_types)  # 没有分隔列
        )
        
        return {
            'rows': rows,
            'cols': cols,
            'row_types': row_types,
            'col_types': col_types,
            'has_separators': has_separators,
            'has_nested_structure': has_nested_structure,
            'is_regular': is_regular
        }
        
    def _detect_table_sections(self, structure: List[List[Dict]]) -> List[Dict]:
        """检测表格中的分区和标题"""
        sections = []
        current_section = None
        
        for row_idx, row in enumerate(structure):
            if not row:
                continue
                
            # 检查是否是标题行
            is_title = False
            cell_text = row[0].get('text', '').strip()
            
            # 通过特征判断是否是标题
            if cell_text.endswith('：') or cell_text.endswith(':'):
                if len(row) == 1 or all(not cell.get('text', '').strip() for cell in row[1:]):
                    is_title = True
            elif '基本信息' in cell_text or '注册' in cell_text:
                is_title = True
            
            if is_title:
                # 如果找到新标题，结束前一个分区
                if current_section:
                    current_section['end'] = row_idx
                    sections.append(current_section)
                
                # 开始新分区
                current_section = {
                    'title': cell_text.rstrip('：:'),
                    'start': row_idx + 1,
                    'end': None,
                    'type': 'one_dimensional'  # 默认为一维表格
                }
            
        # 处理最后一个分区
        if current_section:
            current_section['end'] = len(structure)
            sections.append(current_section)
        
        # 如果没有找到任何分区，创建一个默认分区
        if not sections and structure:
            sections.append({
                'title': '',
                'start': 0,
                'end': len(structure),
                'type': 'one_dimensional'
            })
        
        return sections

    def _generate_queries_with_llm(self, structure: List[List[Dict]], analysis: TableAnalysisResult) -> List[QueryInfo]:
        """使用LLM生成查询"""
        try:
            # 构建提示
            prompt = "请为以下表格中的空白单元格生成查询：\n\n"
            
            # 添加表格内容
            for row in structure:
                row_text = [f"'{cell.get('text', '').strip()}'" for cell in row]
                prompt += " | ".join(row_text) + "\n"
                
            prompt += "\n对于每个空白单元格，请：\n"
            prompt += "1. 确定应该查询的内容\n"
            prompt += "2. 生成自然语言查询\n"
            prompt += "3. 考虑上下文信息\n"
            
            # 调用LLM
            try:
                response = self.llm_handler.generate_queries(prompt)
            except Exception as e:
                print(f"LLM调用失败: {str(e)}")
                return []
                
            if not response:
                return []
                
            # 解析LLM响应，生成查询
            queries = []
            for query_info in response.get('queries', []):
                row = query_info.get('row', 0)
                col = query_info.get('col', 0)
                text = query_info.get('text', '')
                confidence = query_info.get('confidence', 0.7)  # 默认置信度
                
                if text and self._is_valid_position(structure, row, col):
                    queries.append(QueryInfo(
                        text=text,
                        row=row,
                        col=col,
                        answer_positions=[{
                            'row': row,
                            'col': col,
                            'context': self._get_cell_context(structure, row, col)
                        }],
                        is_multi_row=False,
                        confidence=confidence
                    ))
                    
            return queries
            
        except Exception as e:
            print(f"LLM查询生成失败: {str(e)}")
            return []
            
    def _generate_queries_for_form(self, table_data: List[List[Dict]], structure_info: Dict) -> List[QueryInfo]:
        """为表单类型表格生成查询"""
        queries = []
        
        # 对于两列的表单
        if len(table_data[0]) == 2:
            for row_idx, row in enumerate(table_data):
                # 获取标题文本
                title = row[0].get('text', '').strip()
                if not title or not title.endswith('：'):
                    continue
                
                # 生成查询
                query = QueryInfo(
                    text=title.rstrip('：'),  # 移除末尾的冒号
                    row=row_idx,
                    col=1,  # 查询应该在第二列
                    note=None
                )
                queries.append(query)
                
        return queries
        
    def _generate_queries_for_matrix(self, table_data: List[List[Dict]], structure_info: Dict) -> List[QueryInfo]:
        """为矩阵表格生成查询
        
        Args:
            table_data: 表格数据
            structure_info: 表格结构信息
            
        Returns:
            List[QueryInfo]: 查询列表
        """
        queries = []
        
        # 获取表头行
        header_row = table_data[0] if table_data else []
        
        # 遍历每个单元格
        for row_idx in range(1, len(table_data)):
            row = table_data[row_idx]
            row_header = row[0].get('text', '').strip() if row else ''
            
            if not row_header:  # 跳过没有行标题的行
                continue
                
            for col_idx in range(1, len(row)):
                if col_idx >= len(header_row):  # 跳过超出列标题范围的单元格
                    continue
                    
                col_header = header_row[col_idx].get('text', '').strip()
                if not col_header:  # 跳过没有列标题的列
                    continue
                    
                cell_value = row[col_idx].get('text', '').strip()
                if not cell_value:  # 只为空单元格生成查询
                    query = QueryInfo(
                        text=f"{row_header}的{col_header}",
                        answer_positions=[{'row': row_idx, 'col': col_idx}],
                        row=row_idx,
                        col=col_idx
                    )
                    query.confidence = 0.8
                    queries.append(query)
        
        return queries

    def _generate_additional_queries(self, table_data: List[List[Dict]], structure_info: Dict) -> List[QueryInfo]:
        """生成额外的复杂查询
        
        Args:
            table_data: 表格数据
            structure_info: 表格结构信息
            
        Returns:
            List[QueryInfo]: 查询列表
        """
        queries = []
        
        # 检查是否是时间序列表格
        has_time_col = False
        if table_data and table_data[0]:
            first_col = table_data[0][0].get('text', '').lower()
            has_time_col = any(word in first_col for word in ['时间', '年度', '日期', '期间'])
        
        if has_time_col:
            # 处理时间序列数据
            for row_idx in range(1, len(table_data)):
                time_value = table_data[row_idx][0].get('text', '').strip()
                if not time_value:
                    continue
                    
                for col_idx in range(1, len(table_data[row_idx])):
                    if not table_data[row_idx][col_idx].get('text', '').strip():
                        col_title = table_data[0][col_idx].get('text', '').strip()
                        if col_title:
                            query_text = f"{time_value}的{col_title}"
                            query = QueryInfo(
                                text=query_text,
                                answer_positions=[{'row': row_idx, 'col': col_idx}],
                                row=row_idx,
                                col=col_idx
                            )
                            query.confidence = 0.85
                            queries.append(query)
        
        return queries

    def _is_header_like(self, text: str) -> bool:
        """判断文本是否像表头"""
        header_keywords = ['类别', '项目', '序号', '编号', '名称', '序号', '类型', '日期', '时间']
        return any(keyword in text for keyword in header_keywords)

    def _generate_query_text(self, row_title: str, col_title: str) -> str:
        """生成查询文本"""
        # 特殊处理时间相关的列
        time_keywords = ['时间', '日期', '期限', '年份']
        if any(keyword in col_title for keyword in time_keywords):
            # 如果行标题已经包含"时间"等字样，直接使用行标题
            if any(keyword in row_title for keyword in time_keywords):
                return row_title
            return f"{row_title}时间"
            
        # 特殊处理编号相关的列
        if '编号' in col_title and '编号' not in row_title:
            return f"{row_title}编号"
            
        # 默认格式
        return f"{row_title}的{col_title}" 

    def _is_separator(self, text: str) -> bool:
        """判断文本是否是分隔符"""
        if not text:
            return False
        # 检查是否全是分隔符号
        separators = set('-_=*#')
        return all(char in separators for char in text) 

    def detect_table_type(self, table: CT_Tbl) -> Tuple[Optional[TableType], float]:
        """检测表格类型
        
        Args:
            table: docx表格对象
            
        Returns:
            Tuple[TableType, float]: 表格类型和置信度
        """
        try:
            # 获取表格结构
            structure = self._get_table_structure(table)
            if not structure:
                return TableType.UNKNOWN, 0.0
                
            # 分析表格
            analysis = self._analyze_table_by_rules(structure)
            
            # 如果规则分析的置信度不够高，尝试使用LLM
            if analysis.confidence < self.confidence_threshold:
                llm_analysis = self._analyze_table_with_llm(structure)
                if llm_analysis and llm_analysis.confidence > analysis.confidence:
                    analysis = llm_analysis
                    
            return analysis.table_type, analysis.confidence
            
        except Exception as e:
            logger.debug(f"检测表格类型失败: {str(e)}")
            return TableType.UNKNOWN, 0.0

    def generate_query(self, text: str, row: int, col: int, note: str = None) -> str:
        """生成查询
        
        Args:
            text: 单元格文本
            row: 行号
            col: 列号
            note: 注释文本
            
        Returns:
            str: 生成的查询
        """
        if not text:
            return None
            
        # 如果有注释，将其作为补充信息添加到查询中
        if note:
            text = f"{text}（{note}）"
            
        return text 

    def _is_numeric(self, text: str) -> bool:
        """检查文本是否为数值
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 是否为数值
        """
        try:
            float(text.replace(',', ''))
            return True
        except ValueError:
            return False 

    def _get_table_structure(self, table: CT_Tbl) -> List[List[Dict]]:
        """获取表格结构"""
        try:
            from .utils.docx_utils import extract_text
            structure = []
            
            for row in table.tr_lst:
                row_data = []
                for cell in row.tc_lst:
                    cell_text = extract_text(cell).strip()
                    row_data.append({
                        'text': cell_text,
                        'is_empty': not bool(cell_text)
                    })
                structure.append(row_data)
            
            return structure
            
        except Exception as e:
            print(f"获取表格结构失败: {str(e)}")
            return [] 