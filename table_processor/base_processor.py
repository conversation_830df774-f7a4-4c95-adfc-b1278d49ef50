from typing import Dict, List, Any
from docx import Document
import logging

# 配置日志
logger = logging.getLogger(__name__)

class BaseProcessor:
    """基础处理器类"""
    
    def process_document(self, doc: Document) -> Dict[str, Any]:
        """处理文档
        
        Args:
            doc: docx文档对象
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        raise NotImplementedError("子类必须实现此方法") 