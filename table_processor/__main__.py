import sys
import argparse
from .main import process_document_with_queries

def run():
    try:
        parser = argparse.ArgumentParser(description='处理文档中的表格')
        parser.add_argument('input', type=str, help='输入文件路径')
        parser.add_argument('output', type=str, nargs='?', help='输出文件路径（可选，默认在输入文件同目录下创建）')
        parser.add_argument('--mode', type=str, choices=['basic', 'balanced', 'complete', 'BASIC', 'BALANCED', 'COMPLETE'], 
                          default='balanced', help='处理模式')
        
        args = parser.parse_args()
        
        result = process_document_with_queries(
            input_path=args.input,
            output_path=args.output,
            mode=args.mode
        )
        print(result)
        
        if not result['success']:
            sys.exit(1)
            
    except Exception:
        print({
            'success': False,
            'file_path': None
        })
        sys.exit(1)

if __name__ == '__main__':
    run() 