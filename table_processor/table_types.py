from enum import Enum
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from docx.oxml.table import CT_Tbl
import logging

logger = logging.getLogger(__name__)

class AnalysisMode(Enum):
    """分析模式"""
    BASIC = "basic"  # 基础模式，只处理简单表格
    BALANCED = "balanced"  # 平衡模式，处理大部分表格
    COMPLETE = "complete"  # 完整模式，处理所有表格

    @staticmethod
    def get_confidence_threshold(mode: 'AnalysisMode') -> float:
        """获取不同模式下的置信度阈值"""
        thresholds = {
            AnalysisMode.BASIC: 0.8,     # 基础模式要求很高的置信度
            AnalysisMode.BALANCED: 0.6,   # 平衡模式使用适中的置信度
            AnalysisMode.COMPLETE: 0.5    # 完整模式使用较低的置信度
        }
        return thresholds.get(mode, 0.7)

class TableType(Enum):
    """表格类型枚举"""
    UNKNOWN = "unknown"
    ONE_DIMENSIONAL = "one_dimensional"  # 一维表格，如基本信息表
    TWO_DIMENSIONAL = "two_dimensional"  # 二维表格，如城市-团队-人数表
    COMPLEX = "complex"  # 复杂表格，需要特殊处理
    FORM = 1     # 一维表格(键值对形式)
    MATRIX = 2   # 二维表格(矩阵形式) 
    LIST = "LIST"
    FINANCIAL = "FINANCIAL"  # 财务报表
    ORGANIZATION = "ORGANIZATION"  # 组织架构
    PRODUCT = "PRODUCT"  # 产品信息
    TIME_SERIES = "TIME_SERIES"  # 时间序列
    ALTERNATE_ROWS = "ALTERNATE_ROWS"  # 交替行
    CITY_TEAM = "CITY_TEAM"

    @staticmethod
    def get_supported_types(mode: AnalysisMode) -> List['TableType']:
        """获取不同模式下支持的表格类型"""
        if mode == AnalysisMode.BASIC:
            return [TableType.FORM, TableType.MATRIX]  # 只支持基础的一维和二维表格
        elif mode == AnalysisMode.BALANCED:
            return [TableType.FORM, TableType.MATRIX, TableType.LIST, TableType.FINANCIAL]
        else:
            return list(TableType)  # 支持所有类型

class AnswerPosition(Enum):
    """答案位置"""
    UNKNOWN = "UNKNOWN"
    NEXT_COLUMN = "NEXT_COLUMN"  # 下一列
    NEXT_ROW = "NEXT_ROW"  # 下一行
    MULTI_COLUMN = "MULTI_COLUMN"
    MULTI_ROW = "MULTI_ROW"
    MATRIX_CELL = "MATRIX_CELL"  # 矩阵单元格
    MULTI_CELLS = "MULTI_CELLS"  # 多个单元格

@dataclass
class CellPosition:
    """单元格位置"""
    row: int
    col: int
    context: List[str] = field(default_factory=list)

@dataclass
class QueryInfo:
    """查询信息"""
    text: str  # 查询文本
    row: int  # 查询所在行
    col: int  # 查询所在列
    answer_positions: List[Dict[str, int]] = field(default_factory=list)  # 答案位置列表
    is_multi_row: bool = False  # 是否需要多行答案
    confidence: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据

    def __init__(self, text: str, answer_positions: List[Dict[str, int]], row: int = 0, col: int = 0):
        self.text = text
        self.answer_positions = answer_positions
        self.row = row
        self.col = col

    def is_valid(self, mode: AnalysisMode) -> bool:
        """检查在指定模式下查询是否有效"""
        threshold = AnalysisMode.get_confidence_threshold(mode)
        return self.confidence >= threshold

@dataclass
class TableAnalysisResult:
    """表格分析结果"""
    table_type: TableType = TableType.UNKNOWN
    confidence: float = 0.5
    answer_position: AnswerPosition = AnswerPosition.UNKNOWN
    metadata: Dict[str, Any] = field(default_factory=lambda: {
        'subject': '',  # 表格主题
        'has_row_header': False,  # 是否有行标题
        'has_column_header': False,  # 是否有列标题
        'empty_cells_meaning': '',  # 空单元格的含义
        'cell_relations': '',  # 单元格关系描述
        'query_pattern': '',  # 查询模式
        'is_multi_year': False,  # 是否包含多个年份
        'is_multi_row': False,  # 是否需要多行答案
        'needs_row_col_combination': False,  # 是否需要行列组合
        'has_hierarchy': False,  # 是否有层级结构
        'time_series_info': None,  # 时间序列信息
        'value_type': None  # 值类型（数字、文本、日期等）
    })
    queries: List[QueryInfo] = field(default_factory=list)
    title: str = ""
    sections: List[Dict[str, Any]] = field(default_factory=list)

    def merge(self, other: 'TableAnalysisResult') -> 'TableAnalysisResult':
        """合并两个分析结果"""
        # 选择置信度较高的类型
        if other.confidence > self.confidence:
            self.table_type = other.table_type
            self.confidence = other.confidence
            self.answer_position = other.answer_position
            
        # 合并元数据
        for key, value in other.metadata.items():
            if isinstance(value, bool):
                self.metadata[key] = self.metadata.get(key, False) or value
            elif isinstance(value, (str, int, float)) and value:
                self.metadata[key] = value
            elif isinstance(value, dict):
                self.metadata[key] = {**self.metadata.get(key, {}), **value}
            elif isinstance(value, list):
                self.metadata[key] = list(set(self.metadata.get(key, []) + value))
                
        return self 

    def filter_queries(self, mode: AnalysisMode) -> List[QueryInfo]:
        """根据分析模式过滤查询"""
        threshold = AnalysisMode.get_confidence_threshold(mode)
        return [q for q in self.queries if q.confidence >= threshold]

    def is_supported_type(self, mode: AnalysisMode) -> bool:
        """检查表格类型是否在指定模式下支持"""
        return self.table_type in TableType.get_supported_types(mode) 

class TableStructure:
    """表格结构信息"""
    def __init__(self):
        self.merged_cells: List[Dict[str, Any]] = []  # 合并单元格信息
        self.section_rows: List[int] = []  # 分隔行索引
        self.headers: List[int] = []  # 标题行索引
        self.hierarchy: List[Dict[str, Any]] = []  # 层次结构信息

class SubTableInfo:
    """子表格信息"""
    def __init__(self):
        self.type: TableType = TableType.UNKNOWN
        self.rows: tuple = (0, 0)  # 起始行和结束行
        self.confidence: float = 0.0
        self.data: List[List[Dict[str, Any]]] = []  # 表格数据

class ComplexTableResult:
    """复杂表格分析结果"""
    def __init__(self):
        self.structure: TableStructure = TableStructure()
        self.subtables: List[SubTableInfo] = []
        self.success: bool = False
        self.error: Optional[str] = None 

def detect_table_type(table: CT_Tbl) -> TableType:
    """检测表格类型
    
    Args:
        table: docx表格对象
    
    Returns:
        表格类型
    """
    try:
        rows = len(table.tr_lst)
        cols = len(table.tr_lst[0].tc_lst) if rows > 0 else 0
        logger.info(f"检测表格类型 - 行数: {rows}, 列数: {cols}")
        
        # 空表格
        if rows == 0 or cols == 0:
            logger.info("检测到空表格")
            return TableType.UNKNOWN
            
        # 检查是否是一维表格
        if cols == 2:
            # 检查第一列是否都是字段名
            is_one_dimensional = True
            for row in table.tr_lst:
                if len(row.tc_lst) > 0:
                    cell = row.tc_lst[0]
                    cell_text = ""
                    for p in cell.p_lst:
                        cell_text += p.text if p.text else ""
                    cell_text = cell_text.strip()
                    # 如果第一列单元格为空，或者内容过长，可能不是字段名
                    if not cell_text or len(cell_text) > 50:
                        is_one_dimensional = False
                        break
            if is_one_dimensional:
                logger.info("检测到一维表格")
                return TableType.ONE_DIMENSIONAL
                
        # 检查是否是二维表格
        if rows >= 2 and cols >= 2:
            # 检查第一行和第一列是否都是表头
            first_row = table.tr_lst[0]
            has_row_headers = True
            has_col_headers = True
            
            # 检查第一行（列标题）
            for cell in first_row.tc_lst[1:]:  # 跳过第一个单元格
                cell_text = ""
                for p in cell.p_lst:
                    cell_text += p.text if p.text else ""
                cell_text = cell_text.strip()
                logger.info(f"检查列标题: {cell_text}")
                if not cell_text:
                    has_col_headers = False
                    break
                    
            # 检查第一列（行标题）
            for row in table.tr_lst[1:]:  # 跳过第一行
                if len(row.tc_lst) > 0:
                    cell = row.tc_lst[0]
                    cell_text = ""
                    for p in cell.p_lst:
                        cell_text += p.text if p.text else ""
                    cell_text = cell_text.strip()
                    logger.info(f"检查行标题: {cell_text}")
                    if not cell_text:
                        has_row_headers = False
                        break
                    
            if has_row_headers and has_col_headers:
                # 检查第一行和第一列的文本长度是否合适（表头通常较短）
                max_header_length = 20
                
                def get_cell_text(cell):
                    text = ""
                    for p in cell.p_lst:
                        text += p.text if p.text else ""
                    return text.strip()
                    
                if all(len(get_cell_text(cell)) <= max_header_length for cell in first_row.tc_lst) and \
                   all(len(get_cell_text(row.tc_lst[0])) <= max_header_length for row in table.tr_lst[1:] if len(row.tc_lst) > 0):
                    logger.info("检测到二维表格")
                    return TableType.TWO_DIMENSIONAL
                    
        # 其他情况视为复杂表格
        logger.info("检测到复杂表格")
        return TableType.COMPLEX
        
    except Exception as e:
        logger.error(f"检测表格类型失败: {str(e)}", exc_info=True)
        return TableType.UNKNOWN 