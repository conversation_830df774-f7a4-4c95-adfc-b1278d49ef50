import logging
import os
import re
import json
from typing import Dict, Any, Optional, List, Union
from docx import Document
from docx.oxml.text.paragraph import CT_P
from docx.oxml.table import CT_Tbl
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.table import Table
from docx.text.paragraph import Paragraph
from .table_types import TableType, AnalysisMode, TableAnalysisResult, QueryInfo, detect_table_type
from .hybrid_detector import HybridTableDetector
from .two_column_processor import TwoColumnProcessor
from .utils.docx_utils import write_cell_content, extract_text
from .models import TableProcessMode
from .paragraph_processor import ParagraphProcessor
from .config import QUERY_CONFIG
from .complex_table_processor import ComplexTableProcessor

# 确保日志目录存在
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'table_processing.log')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, mode='a', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TableProcessor:
    """表格处理器"""
    
    def __init__(self, mode: AnalysisMode = AnalysisMode.BALANCED):
        """初始化表格处理器
        
        Args:
            mode: 处理模式
        """
        self.detector = HybridTableDetector(mode=mode)
        self.paragraph_processor = ParagraphProcessor()
        self.mode = mode
        self.two_column_processor = TwoColumnProcessor()
        logger.info("初始化表格处理器")
        logger.info(f"处理模式: {mode.name}")
        
    def process_table(self, table: CT_Tbl) -> Dict[str, Any]:
        """处理表格
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict: 包含查询信息的字典
        """
        try:
            # 获取表格的行数
            rows = table.tr_lst
            if not rows:
                return {'queries': [], 'marked_queries': []}
                
            row_count = len(rows)
            
            # 如果表格行数大于25，使用两列处理器
            if row_count > 25:
                return self.two_column_processor.process_table(table)
            
            # 否则使用原来的处理方法
            return self.process_normal_table(table)
        except Exception as e:
            logger.error(f"处理表格失败: {str(e)}", exc_info=True)
            return {'queries': [], 'marked_queries': []}
        
    def process_normal_table(self, table: CT_Tbl) -> Dict[str, Any]:
        """处理普通表格（原有的处理方法）
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict: 包含查询信息的字典
        """
        try:
            # 获取表格结构
            rows = table.tr_lst
            if not rows:
                print("表格为空")
                return {'queries': [], 'marked_queries': []}

            print(f"\n表格行数: {len(rows)}")
            print(f"表格列数: {len(rows[0].tc_lst)}")

            # 检查是否是一维表格（两列表格）
            if len(rows[0].tc_lst) == 2:
                print("检测到一维表格，使用一维表格处理器")
                return self.process_1d_table(table)
            
            # 检查是否是二维表格（行列标题都存在）
            if self._is_2d_table(table):
                print("检测到二维表格，使用二维表格处理器")
                return self.process_matrix_table(table)
            
            # 其他情况按照多行表格处理
            print("使用复杂表格处理器")
            return self.process_complex_table(table)
                
        except Exception as e:
            print(f"处理普通表格失败: {str(e)}")
            return {'queries': [], 'marked_queries': []}
        
    def process_document(self, input_path: str, output_path: str = None, mode: TableProcessMode = TableProcessMode.BALANCED) -> Dict[str, Any]:
        """处理文档中的表格和段落
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径，默认为None（在输入文件同目录下创建）
            mode: 处理模式
            
        Returns:
            Dict: 包含处理结果的字典，格式为：
                {
                    'success': bool,
                    'file_path': str
                }
        """
        try:
            # 加载文档
            if not os.path.isfile(input_path):
                return {
                    'success': False,
                    'file_path': None
                }
            
            doc = Document(input_path)
            
            # 如果没有指定输出路径，在输入文件同目录下创建
            if not output_path:
                input_dir = os.path.dirname(os.path.abspath(input_path))
                input_name = os.path.basename(input_path)
                output_name = input_name.replace('.docx', '_processed.docx')
                output_path = os.path.join(input_dir, output_name)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
                
            # 初始化统计信息
            total_queries = 0
            total_marked_queries = 0
            
            # 首先处理段落
            paragraph_result = self.paragraph_processor.process_document(doc)
            if isinstance(paragraph_result, dict):
                total_queries += paragraph_result.get('total_queries', 0)
                total_marked_queries += paragraph_result.get('marked_queries', 0)
            
            # 处理每个表格
            for table in doc.tables:
                try:
                    # 如果表格行数大于25，直接使用two_column_processor
                    rows = len(table.rows)
                    if rows > 25:
                        processor = TwoColumnProcessor()
                        result = processor.process_table(table._tbl)
                    else:
                        # 检测表格类型
                        table_type = detect_table_type(table._tbl)
                        
                        # 根据表格类型选择处理器
                        if table_type == TableType.ONE_DIMENSIONAL:
                            processor = TwoColumnProcessor()
                            result = processor.process_table(table._tbl)
                        elif table_type == TableType.TWO_DIMENSIONAL:
                            processor = ComplexTableProcessor()
                            result = processor.process_table(table._tbl)
                        else:
                            processor = ComplexTableProcessor()
                            result = processor.process_table(table._tbl)
                        
                    # 更新统计信息
                    if isinstance(result, dict):
                        total_queries += result.get('total_queries', 0)
                        total_marked_queries += result.get('total_marked_queries', 0)
                        
                except Exception:
                    continue
            
            # 标记所有查询
            if hasattr(self.paragraph_processor, 'mark_queries'):
                doc = self.paragraph_processor.mark_queries(doc, paragraph_result.get('queries', []))
                
            # 保存文档
            doc.save(output_path)
            
            return {
                'success': True,
                'file_path': output_path
            }
            
        except Exception:
            return {
                'success': False,
                'file_path': None
            }
        
    def _should_process_table(self, analysis: TableAnalysisResult) -> bool:
        """根据模式决定是否处理表格"""
        if analysis.table_type == TableType.FORM:
            return True
        elif analysis.table_type == TableType.MATRIX:
            return True
        else:  # COMPLETE
            return True
            
    def _mark_queries(self, table: Table, queries: List[QueryInfo]) -> int:
        """标记查询位置
        
        Args:
            table: 表格对象
            queries: 查询列表
            
        Returns:
            int: 成功标记的查询数
        """
        marked_count = 0
        for query in queries:
            try:
                # 验证位置
                if not self._validate_position(table, query.row, query.col):
                    continue
                    
                # 获取单元格
                cell = table.rows[query.row].cells[query.col]
                
                # 如果单元格为空，插入查询
                if not cell.text.strip():
                    cell.text = f"{{查询：{query.text}}}"
                    marked_count += 1
                    
            except Exception as e:
                logger.error(f"标记查询失败: {str(e)}")
                continue
                
        return marked_count
        
    def _validate_position(self, table: Table, row: int, col: int) -> bool:
        """验证表格位置是否有效"""
        try:
            if row < 0 or col < 0:
                return False
            if row >= len(table.rows):
                return False
            if col >= len(table.rows[0].cells):
                return False
            return True
        except Exception:
            return False
        
    def get_table_info(self, table: Table) -> Dict[str, Any]:
        """获取表格信息
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict[str, Any]: 表格信息
        """
        try:
            # 获取表格维度
            row_count = len(table.rows)
            col_count = len(table.rows[0].cells) if row_count > 0 else 0
            
            # 提取表格结构
            structure = []
            empty_cells = []
            
            for row_idx, row in enumerate(table.rows):
                row_cells = []
                for col_idx, cell in enumerate(row.cells):
                    text = cell.text.strip()
                    cell_info = {
                        'text': text,
                        'row': row_idx,
                        'col': col_idx,
                        'merged': False  # 简化合并单元格检测
                    }
                    row_cells.append(cell_info)
                    
                    # 记录空单元格
                    if not text:
                        empty_cells.append((row_idx, col_idx))
                        
                structure.append(row_cells)
                
            logger.debug(f"处理表格: {row_count}行 x {col_count}列")
            logger.debug(f"找到 {len(empty_cells)} 个空单元格")
            
            return {
                'table_structure': structure,
                'row_count': row_count,
                'col_count': col_count,
                'empty_cells': empty_cells
            }
            
        except Exception as e:
            logger.error(f"获取表格信息失败: {str(e)}", exc_info=True)
            return {
                'table_structure': [],
                'row_count': 0,
                'col_count': 0,
                'empty_cells': []
            }
        
    def _get_cell_style(self, cell: Any) -> Dict[str, Any]:
        """获取单元格样式"""
        style = {}
        try:
            # 提取字体样式
            if cell._tc.get_or_add_tcPr().xpath('./w:shd'):
                style['background'] = cell._tc.get_or_add_tcPr().xpath('./w:shd')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}fill')
                
            # 提取边框样式
            if cell._tc.get_or_add_tcPr().xpath('./w:tcBorders'):
                borders = cell._tc.get_or_add_tcPr().xpath('./w:tcBorders')[0]
                style['borders'] = {
                    'top': borders.xpath('./w:top')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:top') else None,
                    'bottom': borders.xpath('./w:bottom')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:bottom') else None,
                    'left': borders.xpath('./w:left')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:left') else None,
                    'right': borders.xpath('./w:right')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:right') else None
                }
                
            # 提取文本样式
            if cell.paragraphs and cell.paragraphs[0].runs:
                run = cell.paragraphs[0].runs[0]
                style['font'] = {
                    'bold': run.bold,
                    'italic': run.italic,
                    'color': run.font.color.rgb if run.font.color else None
                }
        except Exception:
            pass
            
        return style
        
    def _get_visual_features(self, table: Any) -> List[Dict[str, Any]]:
        """获取表格的视觉特征"""
        features = []
        
        try:
            # 检查表格边框
            if table._tbl.xpath('.//w:tblBorders'):
                borders = table._tbl.xpath('.//w:tblBorders')[0]
                features.append({
                    'type': 'table_borders',
                    'value': {
                        'top': borders.xpath('./w:top')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:top') else None,
                        'bottom': borders.xpath('./w:bottom')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val') if borders.xpath('./w:bottom') else None
                    }
                })
                
            # 检查表格网格
            if table._tbl.xpath('.//w:tblGrid'):
                features.append({
                    'type': 'table_grid',
                    'value': True
                })
                
            # 检查表格样式
            if table._tbl.xpath('.//w:tblStyle'):
                style = table._tbl.xpath('.//w:tblStyle')[0].get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
                features.append({
                    'type': 'table_style',
                    'value': style
                })
        except Exception:
            pass
            
        return features

    def _validate_table_position(self, table: Table, row: int, col: int) -> bool:
        """验证表格位置是否有效"""
        try:
            if row < 0 or col < 0:
                return False
            
            if row >= len(table.rows) or col >= len(table.rows[0].cells):
                return False
            
            return True
        
        except Exception as e:
            logger.error(f"验证表格位置失败: {str(e)}")
            return False

    def _process_table(self, table):
        """处理单个表格"""
        try:
            rows = len(table.rows)
            cols = len(table.rows[0].cells) if rows > 0 else 0
            
            # 首先检查是否是两列表格（表单型）
            if cols == 2:
                # 检查是否所有第一列都是以冒号结尾的标题
                is_form_table = True
                for row in table.rows:
                    if len(row.cells) != 2:
                        is_form_table = False
                        break
                    title = row.cells[0].text.strip()
                    # 支持中文冒号和英文冒号
                    has_colon = title.endswith('：') or title.endswith(':')
                    if not title or not has_colon:
                        is_form_table = False
                        break
                
                if is_form_table:
                    return self._process_form_table(table)
            
            # 处理多维表格（3列或以上）
            if cols >= 3:
                return self._process_matrix_table(table)
            
            # 如果都不是，才使用复杂表格处理器
            processor = ComplexTableProcessor()
            result = processor.process_complex_table(table, self.mode)
            
            if result:
                return {
                    'queries': result.get('queries', []),
                    'marked_queries': result.get('marked_queries', [])
                }
                
            return None
            
        except Exception as e:
            logger.error(f"处理表格失败: {str(e)}", exc_info=True)
            return None

    def _process_form_table(self, table):
        """处理表单型表格（两列）"""
        try:
            queries = []
            marked_queries = []
            
            # 遍历每一行
            for row_idx, row in enumerate(table.rows):
                title = row.cells[0].text.strip()
                # 支持中文冒号和英文冒号
                if title and (title.endswith('：') or title.endswith(':')):
                    # 移除冒号
                    clean_title = title.rstrip('：').rstrip(':')
                    query = {
                        'text': clean_title,
                        'row': row_idx,
                        'col': 1,
                        'note': None
                    }
                    queries.append(query)
                    
                    # 直接写入查询到单元格
                    row.cells[1].text = f"{{查询：{clean_title}}}"
                    marked_queries.append(query)
            
            return {
                'queries': queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            logger.error(f"处理表单表格失败: {str(e)}", exc_info=True)
            return None

    def _process_matrix_table(self, table):
        """处理矩阵型表格（多列）"""
        try:
            queries = []
            marked_queries = []
            
            rows = len(table.rows)
            cols = len(table.rows[0].cells) if rows > 0 else 0
            
            # 获取第一行作为列标题
            col_headers = []
            if rows > 0:
                for col_idx in range(cols):
                    header = table.rows[0].cells[col_idx].text.strip()
                    col_headers.append(header)
            
            # 获取第一列作为行标题（跳过第一个单元格）
            row_headers = []
            for row_idx in range(1, rows):
                header = table.rows[row_idx].cells[0].text.strip()
                row_headers.append(header)
            
            # 为空单元格生成查询
            for row_idx in range(1, rows):
                for col_idx in range(cols):  # 从第0列开始，包括第一列
                    cell = table.rows[row_idx].cells[col_idx]
                    if not cell.text.strip():  # 只为空单元格生成查询
                        if col_idx == 0:
                            # 第一列为空，使用列标题作为查询
                            if col_headers and col_headers[0]:
                                query_text = col_headers[0]
                            else:
                                query_text = "第一列内容"
                        else:
                            # 其他列为空，组合行标题和列标题
                            row_header = row_headers[row_idx - 1] if row_idx - 1 < len(row_headers) else ""
                            col_header = col_headers[col_idx] if col_idx < len(col_headers) else ""
                            
                            if row_header and col_header:
                                query_text = f"{row_header}的{col_header}"
                            elif col_header:
                                query_text = col_header
                            elif row_header:
                                query_text = f"{row_header}的信息"
                            else:
                                continue
                        
                        query = {
                            'text': query_text,
                            'row': row_idx,
                            'col': col_idx,
                            'note': None
                        }
                        queries.append(query)
                        
                        # 写入查询
                        cell.text = f"{{查询：{query_text}}}"
                        marked_queries.append(query)
            
            return {
                'queries': queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            logger.error(f"处理矩阵表格失败: {str(e)}", exc_info=True)
            return None
        
    def _is_2d_table(self, table) -> bool:
        """判断是否是二维表格（第一行和第一列都是问题）
        
        Args:
            table: docx表格对象
            
        Returns:
            bool: 是否是二维表格
        """
        try:
            if len(table.rows) < 2 or len(table.rows[0].cells) < 2:
                return False
            
            # 检查第一行是否都是问题
            header_row = table.rows[0]
            header_cells = [cell.text.strip() for cell in header_row.cells]
            if not all(header_cells[1:]):  # 跳过第一个单元格，检查其他单元格是否都有内容
                return False
            
            # 检查第一列是否都是问题
            first_col = [row.cells[0].text.strip() for row in table.rows]
            if not all(first_col[1:]):  # 跳过第一个单元格，检查其他单元格是否都有内容
                return False
            
            return True
            
        except Exception:
            return False
        
    def _process_paragraph(self, paragraph):
        """处理单个段落
        
        Args:
            paragraph: docx段落对象
        """
        try:
            # 使用段落处理器处理段落
            result = self.paragraph_processor.process_paragraph(paragraph)
            
            if result:
                # 不在这里打印信息，段落处理器已经打印了
                # 也不在这里修改段落文本，段落处理器已经修改了
                return result
                
        except Exception as e:
            logger.error(f"处理段落失败: {str(e)}", exc_info=True)
            return None

    def process_1d_table(self, table: CT_Tbl) -> Dict[str, Any]:
        """处理一维表格（两列表格）
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict: 包含查询信息的字典
        """
        queries = []
        marked_queries = []
        
        try:
            print("\n开始处理一维表格:")
            for row_idx, row in enumerate(table.tr_lst):
                # 跳过空行
                if len(row.tc_lst) < 2:
                    print(f"跳过空行 {row_idx}")
                    continue
                    
                # 获取左列文本（查询字段）
                left_cell = row.tc_lst[0]
                query_text = extract_text(left_cell).strip()
                print(f"\n处理第 {row_idx + 1} 行:")
                print(f"左列文本: {query_text}")
                
                # 跳过空的查询字段
                if not query_text:
                    print("左列为空，跳过")
                    continue
                    
                # 清理查询文本
                query_text = self._clean_query_text(query_text)
                if not query_text:
                    print("清理后的查询文本为空，跳过")
                    continue
                    
                # 生成查询
                query = {
                    'text': query_text,
                    'type': 'field',
                    'row': row_idx,
                    'col': 1
                }
                queries.append(query)
                print(f"生成查询: {query}")
                
                # 在右列单元格写入查询
                right_cell = row.tc_lst[1]
                if self._write_query_to_cell(right_cell, query_text):
                    marked_queries.append(query)
                    print("成功写入查询")
                else:
                    print("写入查询失败")
                    
            print(f"\n一维表格处理完成，生成查询 {len(queries)} 个，标记查询 {len(marked_queries)} 个")
            return {
                'queries': queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            print(f"处理一维表格失败: {str(e)}")
            return {'queries': [], 'marked_queries': []}

    def process_matrix_table(self, table: CT_Tbl) -> Dict[str, Any]:
        """处理二维表格（行列标题交叉）
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict: 包含查询信息的字典
        """
        queries = []
        marked_queries = []
        
        try:
            rows = table.tr_lst
            if not rows or len(rows[0].tc_lst) < 2:
                return {'queries': [], 'marked_queries': []}
                
            # 获取列标题
            col_titles = []
            first_row = rows[0]
            for col_idx in range(1, len(first_row.tc_lst)):
                col_title = first_row.tc_lst[col_idx].text.strip()
                if col_title:
                    col_titles.append((col_idx, col_title))
                    
            # 处理每一行
            for row_idx in range(1, len(rows)):
                row = rows[row_idx]
                if len(row.tc_lst) < 2:
                    continue
                    
                # 获取行标题
                row_title = row.tc_lst[0].text.strip()
                if not row_title:
                    continue
                    
                # 清理行标题
                row_title = self._clean_query_text(row_title)
                if not row_title:
                    continue
                    
                # 为每个列标题生成查询
                for col_idx, col_title in col_titles:
                    if col_idx >= len(row.tc_lst):
                        continue
                        
                    # 生成组合查询文本
                    query_text = f"{row_title}的{col_title}"
                    query = {
                        'text': query_text,
                        'type': 'matrix',
                        'row': row_idx,
                        'col': col_idx
                    }
                    queries.append(query)
                    
                    # 在单元格写入查询
                    cell = row.tc_lst[col_idx]
                    if self._write_query_to_cell(cell, query_text):
                        marked_queries.append(query)
                        
            return {
                'queries': queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            logger.error(f"处理二维表格失败: {str(e)}", exc_info=True)
            return {'queries': [], 'marked_queries': []}

    def _write_query_to_cell(self, cell: Any, query_text: str) -> bool:
        """在单元格中写入查询
        
        Args:
            cell: 单元格对象
            query_text: 查询文本
            
        Returns:
            bool: 是否成功写入
        """
        try:
            # 如果单元格已经有内容，不覆盖
            current_text = extract_text(cell).strip()
            if current_text:
                print(f"单元格已有内容: {current_text}")
                return False
                
            # 写入查询
            write_cell_content(cell, "", query_text)
            print(f"成功写入查询: {query_text}")
            return True
            
        except Exception as e:
            print(f"写入单元格失败: {str(e)}")
            return False

    def _clean_query_text(self, text: str) -> str:
        """清理查询文本
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        print(f"\n清理查询文本:")
        print(f"原始文本: {text}")
        
        # 移除冒号及其后面的内容
        text = re.split('[：:]', text)[0]
        print(f"移除冒号后: {text}")
        
        # 移除括号及其内容
        text = re.sub(r'[（(][^）)]*[）)]', '', text)
        print(f"移除括号后: {text}")
        
        # 移除特殊字符
        text = re.sub(r'[*#@/\\]', '', text)
        print(f"移除特殊字符后: {text}")
        
        # 移除多余的空格
        text = ' '.join(text.split())
        text = text.strip()
        print(f"最终文本: {text}")
        
        return text

    def process_complex_table(self, table: CT_Tbl) -> Dict[str, Any]:
        """处理复杂表格（多行多列）
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict: 包含查询信息的字典
        """
        try:
            print("\n开始处理复杂表格:")
            
            # 获取表格的行数和列数
            rows = table.tr_lst
            if not rows:
                print("表格为空，跳过处理")
                return {'queries': [], 'marked_queries': []}
                
            first_row = rows[0]
            if not first_row.tc_lst:
                print("表格第一行为空，跳过处理")
                return {'queries': [], 'marked_queries': []}
                
            print(f"表格行数: {len(rows)}")
            print(f"表格列数: {len(first_row.tc_lst)}")
            
            # 获取合并单元格信息
            from .utils.docx_utils import get_merged_cells
            merged_cells = get_merged_cells(table)
            
            queries = []
            marked_queries = []
            seen_queries = set()  # 用于去重
            
            # 标题关键词
            title_keywords = [
                "基本信息", "联系方式", "附件", "尽职调查报告",
                "一、", "二、", "三、", "四、", "五、", "六、", "七、", "八、", "九、", "十、",
                "管理人基本情况", "第一列内容"
            ]
            
            # 检查文本是否包含标题的函数
            def contains_title(text):
                # 如果文本为空，返回False
                if not text:
                    return False
                    
                # 检查是否包含标题关键词
                if any(keyword in text for keyword in title_keywords):
                    return True
                    
                # 检查是否匹配标题模式
                title_patterns = [
                    r"[一二三四五六七八九十]、",              # 匹配中文数字标题
                    r"\d+[.、]",                            # 匹配阿拉伯数字标题
                    r"基本情况$",                           # 匹配"基本情况"结尾
                    r"基本信息$",                           # 匹配"基本信息"结尾
                    r"联系方式$",                           # 匹配"联系方式"结尾
                    r"管理人基本情况$",                      # 匹配"管理人基本情况"结尾
                ]
                return any(re.search(pattern, text) for pattern in title_patterns)
            
            # 清理查询文本的函数
            def clean_query_text(text):
                # 如果文本为空，直接返回空字符串
                if not text:
                    return ""
                    
                # 移除标点符号
                text = text.replace("：", "").replace(":", "")
                
                # 移除括号及其内容
                text = re.sub(r"（[^）]*）", "", text)
                text = re.sub(r"\([^)]*\)", "", text)
                
                # 移除多余的空格和换行
                text = re.sub(r"\s+", " ", text)
                
                # 移除多余的"的"字
                text = re.sub(r"的+", "的", text)
                
                # 清理空白字符
                text = text.strip()
                
                # 如果文本以"的"开头或结尾，去掉它
                text = text.strip("的")
                
                # 移除标题相关的后缀
                text = re.sub(r"的[一二三四五六七八九十]、.*?$", "", text)
                text = re.sub(r"的.*?基本情况$", "", text)
                text = re.sub(r"的.*?基本信息$", "", text)
                text = re.sub(r"的.*?联系方式$", "", text)
                text = re.sub(r"的一、管理人基本情况$", "", text)
                text = re.sub(r"一、管理人基本情况$", "", text)
                text = re.sub(r"管理人基本情况$", "", text)
                text = re.sub(r"基本情况$", "", text)
                text = re.sub(r"基本信息$", "", text)
                text = re.sub(r"联系方式$", "", text)
                
                return text
            
            # 检查是否包含禁止的标题
            def contains_forbidden_title(text):
                forbidden_titles = [
                    "一、管理人基本情况",
                    "管理人基本情况",
                    "基本情况",
                    "基本信息",
                    "联系方式"
                ]
                return any(title in text for title in forbidden_titles)
            
            # 确定查询类型的函数
            def determine_query_type(text):
                # 人员相关
                if any(keyword in text for keyword in ['姓名', '人员', '负责人', '代表', '法人']):
                    return 'person'
                # 日期相关
                elif any(keyword in text for keyword in ['日期', '时间', '成立']):
                    return 'date'
                # 金额相关
                elif any(keyword in text for keyword in ['金额', '资本', '规模', '注册资本', '实缴资本']):
                    return 'amount'
                # 比例相关
                elif any(keyword in text for keyword in ['比例', '率']):
                    return 'percentage'
                # 地址相关
                elif any(keyword in text for keyword in ['地址', '住所', '办公']):
                    return 'address'
                # 联系方式相关
                elif any(keyword in text for keyword in ['电话', '传真', '联系']):
                    return 'contact'
                # 代码相关
                elif any(keyword in text for keyword in ['代码', '编号', '信用代码']):
                    return 'code'
                # 网址相关
                elif any(keyword in text for keyword in ['网址', 'url', '网站']):
                    return 'url'
                # 范围相关
                elif any(keyword in text for keyword in ['范围', '经营范围']):
                    return 'scope'
                # 类型相关
                elif any(keyword in text for keyword in ['类型', '性质']):
                    return 'type'
                # 牌照相关
                elif any(keyword in text for keyword in ['牌照', '资质', '备案', '许可']):
                    return 'license'
                # 默认类型
                else:
                    return 'form_field'
            
            # 识别标题列
            title_columns = set()
            for col_idx in range(len(first_row.tc_lst)):
                title_count = 0
                total_count = 0
                
                for row_idx, row in enumerate(rows):
                    if col_idx >= len(row.tc_lst):
                        continue
                        
                    cell = row.tc_lst[col_idx]
                    cell_text = extract_text(cell).strip()
                    
                    if cell_text:
                        total_count += 1
                        if contains_title(cell_text):
                            title_count += 1
                
                # 如果超过30%的非空单元格包含标题，则认为这是标题列
                if total_count > 0 and title_count / total_count > 0.3:
                    title_columns.add(col_idx)
                    print(f"列 {col_idx} 被识别为标题列")
            
            # 遍历每一行
            for row_idx, row in enumerate(rows):
                # 跳过标题行
                if row_idx == 0:
                    continue
                    
                # 获取实际列数（考虑合并单元格）
                actual_cols = 0
                col_idx = 0
                while col_idx < len(row.tc_lst):
                    if (row_idx, col_idx) in merged_cells:
                        _, colspan = merged_cells[(row_idx, col_idx)]
                        actual_cols += 1
                        col_idx += colspan
                    else:
                        actual_cols += 1
                        col_idx += 1
                
                # 如果实际列数为2，处理为查询
                if actual_cols == 2:
                    # 获取第一列的文本（作为查询）
                    first_cell = row.tc_lst[0]
                    original_text = extract_text(first_cell).strip()
                    
                    # 跳过空单元格
                    if not original_text:
                        continue
                        
                    # 跳过标题列中的单元格
                    if 0 in title_columns:
                        continue
                        
                    # 跳过包含标题的单元格
                    if contains_title(original_text):
                        continue
                        
                    # 清理查询文本
                    query_text = clean_query_text(original_text)
                    
                    # 跳过清理后为空的查询
                    if not query_text:
                        continue
                        
                    # 跳过包含标题的查询
                    if contains_title(query_text) or contains_forbidden_title(query_text):
                        continue
                        
                    # 跳过重复的查询
                    if query_text in seen_queries:
                        continue
                    seen_queries.add(query_text)
                    
                    # 确定查询类型
                    query_type = determine_query_type(query_text)
                    
                    # 生成查询
                    query = {
                        'text': query_text,
                        'type': query_type,
                        'level': 0,
                        'title_type': '',
                        'annotation': ''
                    }
                    
                    queries.append(query)
                    
                    # 在第二列写入查询
                    try:
                        second_cell = row.tc_lst[1]
                        write_cell_content(
                            second_cell, 
                            f"{{查询：{query_text}}}", 
                            query_text,
                            {'row_idx': row_idx, 'col_idx': 1}
                        )
                        marked_queries.append(query)
                    except Exception as e:
                        print(f"写入查询失败: {str(e)}")
            
            return {
                'queries': queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            print(f"处理复杂表格失败: {str(e)}")
            logger.error(f"处理复杂表格失败: {str(e)}", exc_info=True)
            return {'queries': [], 'marked_queries': []} 

def write_cell_content(cell, content: str, query: Optional[str] = None, position_info: Optional[Dict] = None) -> None:
    """写入单元格内容
    
    Args:
        cell: docx单元格对象 (可以是 _Cell 或 CT_Tc)
        content: 要写入的内容
        query: 查询内容（如果有）
        position_info: 位置信息字典，包含 row_idx 和 col_idx
    """
    if cell is None:
        return
        
    try:
        # 清空单元格内容
        for child in cell.inner_content_elements:
            cell.remove(child)
            
        # 创建新段落
        new_p = OxmlElement('w:p')
        cell.append(new_p)
        
        # 创建运行对象
        new_r = OxmlElement('w:r')
        new_p.append(new_r)
        
        # 创建文本对象
        new_text = OxmlElement('w:t')
        new_text.text = content.strip() if content else ""
        new_r.append(new_text)
        
        # 如果有查询内容，添加新段落
        if query:
            # 清理查询文本
            def clean_query_text(text):
                # 移除标题相关的后缀
                text = re.sub(r"的[一二三四五六七八九十]、.*?$", "", text)
                text = re.sub(r"的.*?基本情况$", "", text)
                text = re.sub(r"的.*?基本信息$", "", text)
                text = re.sub(r"的.*?联系方式$", "", text)
                text = re.sub(r"的一、管理人基本情况$", "", text)
                text = re.sub(r"一、管理人基本情况$", "", text)
                text = re.sub(r"管理人基本情况$", "", text)
                text = re.sub(r"基本情况$", "", text)
                text = re.sub(r"基本信息$", "", text)
                text = re.sub(r"联系方式$", "", text)
                
                # 移除括号及其内容
                text = re.sub(r"（[^）]*）", "", text)
                text = re.sub(r"\([^)]*\)", "", text)
                
                # 移除多余的空格和换行
                text = re.sub(r"\s+", " ", text)
                
                # 移除多余的"的"字
                text = re.sub(r"的+", "的", text)
                
                # 清理空白字符
                text = text.strip()
                
                # 如果文本以"的"开头或结尾，去掉它
                text = text.strip("的")
                
                return text
                
            # 检查是否包含禁止的标题
            def contains_forbidden_title(text):
                forbidden_titles = [
                    "一、管理人基本情况",
                    "管理人基本情况",
                    "基本情况",
                    "基本信息",
                    "联系方式"
                ]
                return any(title in text for title in forbidden_titles)
                
            # 清理查询文本
            cleaned_query = clean_query_text(query)
            
            # 如果清理后的文本为空或包含禁止的标题，跳过写入
            if not cleaned_query or contains_forbidden_title(cleaned_query):
                return
                
            # 格式化查询文本
            formatted_query = f"{{查询：{cleaned_query}}}"
            
            # 创建新段落用于查询
            query_p = OxmlElement('w:p')
            cell.append(query_p)
            
            # 创建运行对象
            query_r = OxmlElement('w:r')
            query_p.append(query_r)
            
            # 设置查询文本的格式
            rPr = OxmlElement('w:rPr')
            
            # 设置斜体
            i = OxmlElement('w:i')
            i.set(qn('w:val'), 'true')
            rPr.append(i)
            
            # 设置蓝色
            color = OxmlElement('w:color')
            color.set(qn('w:val'), '0000FF')
            rPr.append(color)
            
            query_r.append(rPr)
            
            # 创建文本对象
            query_text = OxmlElement('w:t')
            query_text.text = formatted_query
            query_r.append(query_text)
            
    except Exception as e:
        logger.error(f"写入单元格失败: {str(e)}", exc_info=True) 