import logging
from docx import Document
from docx.text.paragraph import Paragraph
from docx.table import Table
from typing import List, Dict, Any, Optional, Union, Tuple
import re
from .base_processor import BaseProcessor
from docx.shared import RGBColor
from docx.oxml.text.paragraph import CT_P
from docx.oxml.table import CT_Tbl, CT_Row, CT_Tc
from docx.text.run import Run
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.table import _Cell

logger = logging.getLogger(__name__)

class ParagraphProcessor(BaseProcessor):
    """段落处理器"""
    
    def __init__(self):
        # 编译标题模式 - 更精确的匹配
        self.title_pattern = re.compile(r'^\d+(?:\.\d+)*\.?\s+')
        
        # 编译注释模式
        self.note_pattern = re.compile(r'^\s*[（(][注:：].+[）)]$')
    
        # 记录已处理的标题，避免重复
        self.processed_titles = set()
        
        # 记录最后一个生成的查询，用于合并注释
        self.last_query = None
        
        # 记录最后一个查询的位置
        self.last_query_position = None
        
    def process_document(self, doc: Document) -> Dict[str, Any]:
        """处理文档中的段落
        
        Args:
            doc: docx文档对象
            
        Returns:
            Dict[str, Any]: 段落信息列表
        """
        queries = []
        current_section = None
        last_title_query = None
        
        # 遍历所有段落和表格
        elements = list(doc.paragraphs) + list(doc.tables)
        
        for i, element in enumerate(elements):
            if isinstance(element, Table):
                # 将表格添加到当前段落的内容中
                if current_section:
                    current_section['content'].append(element)
                continue
                
            if not isinstance(element, Paragraph):
                continue
                
            text = element.text.strip()
            if not text:
                continue
                
            # 检查是否是标题
            is_title, level, title_type = self._is_title(text)
            if is_title:
                # 检查是否应该跳过该标题
                if self._should_skip_title(text):
                    logger.info(f"跳过标题: {text}")
                    continue
                    
                # 检查标题是否已处理过
                if text in self.processed_titles:
                    logger.info(f"标题已处理过，跳过: {text}")
                    continue
                    
                # 检查下一个元素是否是表格
                next_element = self._get_next_element(element)
                if next_element is not None and isinstance(next_element, CT_Tbl):
                    logger.info(f"标题后紧跟表格，跳过: {text}")
                    continue
                    
                self.processed_titles.add(text)
                
                # 如果标题本身就是注释，跳过
                if text.startswith('（注：') or text.startswith('(注:'):
                    logger.info(f"跳过注释标题: {text}")
                    continue
                    
                # 创建新段落
                current_section = {
                    'title': text,
                    'type': title_type,
                    'level': level,
                    'content': [],
                    'annotation': None
                }
                
                # 生成查询
                query = {
                    'text': text,
                    'type': 'title',
                    'position': {'paragraph': element}
                }
                last_title_query = query
                queries.append(query)
                
            # 检查是否是注释
            elif self._is_note(text):
                note_text = self._clean_note_text(text)
                if current_section and last_title_query:
                    current_section['annotation'] = note_text
                    current_section['content'].append(text)
                    # 修改上一个标题的查询，添加注释
                    last_title_query['text'] = f"{current_section['title']}（注：{note_text}）"
                    last_title_query['type'] = 'title_with_note'
                    
            elif current_section:
                current_section['content'].append(text)
                
        return {
            'total_queries': len(queries),
            'marked_queries': len([q for q in queries if q.get('position')]),
            'queries': queries
        }
        
    def _get_title_level(self, text: str) -> int:
        """获取标题层级"""
        # 根据标题格式判断层级
        if text.startswith(('一', '二', '三', '四', '五', '六', '七', '八', '九', '十')):
            return 1
        elif text[0].isdigit():
            dots = text.count('.')
            return dots + 1
        return 1
        
    def _get_document_elements(self, doc: Document) -> List[Union[str, Table]]:
        """获取文档中的所有元素（段落文本和表格）"""
        elements = []
        for element in doc._body._body:
            if element.tag.endswith('p'):
                text = element.text.strip()
                if text:
                    elements.append(text)
            elif element.tag.endswith('tbl'):
                elements.append('table')
        return elements
        
    def apply_queries(self, doc: Document, sections: List[Dict[str, Any]]) -> None:
        """将查询应用到文档中
        
        Args:
            doc: docx文档对象
            sections: 段落信息列表
        """
        current_para_idx = 0
        
        for section in sections:
            for content in section.get('content', []):
                if content['type'] == 'note':
                    # 找到原始段落
                    while current_para_idx < len(doc.paragraphs):
                        if doc.paragraphs[current_para_idx].text.strip() == content['text']:
                            # 在原始段落后添加查询段落
                            new_para = doc.add_paragraph()
                            new_para.insert_paragraph_before(content['query'])
                            current_para_idx += 2  # 跳过原始段落和新添加的查询段落
                            break
                        current_para_idx += 1
                elif content['type'] == 'text':
                    current_para_idx += 1
                elif content['type'] == 'table':
                    # 跳过表格
                    while current_para_idx < len(doc.paragraphs) and not doc.paragraphs[current_para_idx].text.strip():
                        current_para_idx += 1 

    def _is_note(self, text: str) -> bool:
        """检查文本是否是注释"""
        return bool(self.note_pattern.match(text))
        
    def _is_major_title(self, text: str) -> bool:
        """检查是否是大标题（一、二、三、等）
        
        Args:
            text: 标题文本
            
        Returns:
            bool: 是否是大标题
        """
        # 中文数字
        chinese_numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
        
        # 去除空格
        text = text.strip()
        
        # 检查是否以中文数字开头且后面跟着顿号
        for num in chinese_numbers:
            if text.startswith(f"{num}、"):
                return True
                
        return False

    def _extract_annotation(self, text: str) -> Optional[str]:
        """提取标题中的注释
        
        Args:
            text: 标题文本
            
        Returns:
            Optional[str]: 提取的注释文本
        """
        # 查找注释
        match = re.search(r'（注[：:](.*?)）', text)
        if match:
            return match.group(1).strip()
        return None

    def _has_title_characteristics(self, text: str) -> bool:
        """检查文本是否具有标题特征
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 是否具有标题特征
        """
        # 如果文本太长，可能不是标题
        if len(text) > 100:
            return False
            
        # 检查是否以冒号结尾
        if text.endswith('：') or text.endswith(':'):
            return True
            
        # 检查是否包含关键词
        title_keywords = [
            '情况', '分析', '说明', '概述', '总结', '介绍', '提示', 
            '简介', '基本信息', '基本情况', '基本状况', '主要内容',
            '结构', '架构', '体系', '制度', '机制', '流程'
        ]
        if any(keyword in text for keyword in title_keywords):
            return True
            
        # 检查是否是问句
        if text.endswith('？') or text.endswith('?'):
            return True
            
        # 检查是否有特殊标点
        special_chars = ['、', '：', ':', '．']
        if any(char in text for char in special_chars):
            return True
            
        # 检查是否是短语（不包含标点符号）
        if len(text) <= 20 and not any(char in '。，,;；!！' for char in text):
            return True
            
        return False

    def _is_title(self, text: str) -> Tuple[bool, int, str]:
        """检查文本是否是标题，并返回标题信息
        
        Args:
            text: 要检查的文本
            
        Returns:
            Tuple[bool, int, str]: (是否是标题, 标题层级, 标题类型)
        """
        text = text.strip()
        
        # 检查中文数字标题（优先检查）
        if re.match(r'^[一二三四五六七八九十]+[、\.]', text):
            return True, 1, "中文数字标题"
        
        # 检查数字层级标题
        if self.title_pattern.match(text):
            # 获取标题编号部分（第一个空格之前的部分）
            number_part = text.split()[0]
            # 移除末尾的点号（如果有）
            if number_part.endswith('.'):
                number_part = number_part[:-1]
            # 计算层级：根据剩余点号的数量确定层级
            level = number_part.count('.') + 1
            return True, level, "数字层级标题"
            
        # 检查罗马数字标题
        if re.match(r'^[IVX]+\.\s', text):
            return True, 1, "罗马数字标题"
            
        # 检查英文字母标题
        if re.match(r'^[A-Z]\.\s', text):
            return True, 1, "英文字母标题"
            
        # 检查圆点标题
        if text.startswith('•') or text.startswith('◦'):
            return True, 1, "圆点标题"
            
        # 检查短横线标题
        if text.startswith('-') or text.startswith('—'):
            return True, 1, "短横线标题"
            
        # 检查启发式标题
        if self._has_title_characteristics(text):
            # 检查是否包含注释
            if '（注：' in text or '(注:' in text:
                main_text = text.split('（注：')[0].split('(注:')[0].strip()
                if self._has_title_characteristics(main_text):
                    return True, 1, "启发式标题"
            else:
                return True, 1, "启发式标题"
            
        return False, 0, ""

    def _clean_note_text(self, text: str) -> str:
        """清理注释文本，移除注释标记
        
        Args:
            text: 要清理的文本
            
        Returns:
            str: 清理后的文本
        """
        # 移除各种注释标记
        patterns = [
            (r'^\s*[（(][注备说][：:]\s*', ''),  # 开头的注释标记
            (r'\s*[）)]\s*$', ''),  # 结尾的括号
            (r'^\s*【注[：:]\s*', ''),  # 方括号开头
            (r'\s*】\s*$', ''),  # 方括号结尾
            (r'^\s*［注[：:]\s*', ''),  # 全角方括号开头
            (r'\s*］\s*$', ''),  # 全角方括号结尾
            (r'^\s*备注[：:]\s*', ''),  # 备注开头
            (r'^\s*说明[：:]\s*', '')  # 说明开头
        ]
        
        result = text
        for pattern, replacement in patterns:
            result = re.sub(pattern, replacement, result)
            
        return result.strip()
        
    def process_paragraph(self, paragraph) -> Optional[Dict[str, Any]]:
        """处理段落，识别标题和注释并生成查询
        
        Args:
            paragraph: docx段落对象
            
        Returns:
            Optional[Dict[str, Any]]: 处理结果
        """
        try:
            text = paragraph.text.strip()
            if not text:  # 跳过空段落
                return None
            
            print(f"\n处理段落文本: {text}")
            print(f"段落长度: {len(text)}")
            
            # 检查是否是标题
            is_title, level, title_type = self._is_title(text)
            print(f"标题检查结果: is_title={is_title}, level={level}, type={title_type}")
            
            if is_title:
                print(f"识别到标题: {text}")
                
                # 检查是否应该跳过该标题
                if self._should_skip_title(text):
                    print(f"跳过标题: {text}")
                    return None
                    
                # 检查标题是否已处理过
                if text in self.processed_titles:
                    print(f"标题已处理过，跳过: {text}")
                    return None
                    
                self.processed_titles.add(text)
                
                # 如果标题本身就是注释，直接返回None
                if text.startswith('（注：'):
                    print(f"跳过注释标题: {text}")
                    return None
                    
                # 提取标题中的注释（如果有）
                annotation = self._extract_annotation(text)
                print(f"标题中的注释: {annotation}")
                
                # 生成查询文本
                query_text = text
                if annotation:
                    # 将注释合并到标题中
                    query_text = f"{text}（注：{annotation}）"
                    
                return {
                    'text': query_text,
                    'type': 'title',
                    'level': level,
                    'title_type': title_type,
                    'annotation': annotation,
                    'position': {
                        'paragraph': paragraph
                    }
                }
                
            return None
            
        except Exception as e:
            logger.error(f"处理段落失败: {str(e)}", exc_info=True)
            return None

    def _get_next_element(self, paragraph):
        """获取段落后的下一个元素
        
        Args:
            paragraph: 当前段落对象
            
        Returns:
            下一个元素对象，如果没有则返回None
        """
        try:
            parent = paragraph._element.getparent()
            if parent is None:
                return None
            
            # 获取当前段落在父元素中的索引
            index = parent.index(paragraph._element)
            
            # 获取下一个元素
            if index + 1 < len(parent):
                return parent[index + 1]
            
        except Exception as e:
            logger.error(f"获取下一个元素失败: {str(e)}", exc_info=True)
        
        return None

    def _is_table(self, element) -> bool:
        """判断元素是否是表格
        
        Args:
            element: 要判断的元素
            
        Returns:
            bool: 是否是表格
        """
        from docx.oxml.table import CT_Tbl
        return isinstance(element, CT_Tbl)

    def _should_skip_title(self, text: str) -> bool:
        """判断是否应该跳过该标题
        
        Args:
            text: 标题文本
            
        Returns:
            bool: 是否应该跳过
        """
        # 跳过文档开头的标题
        if text in ['附件6：', '尽职调查报告', '(适用私募证券投资基金公司）']:
            return True
        
        # 跳过大标题（一、二、三等）
        if self._is_major_title(text):
            return True
            
        # 跳过表格上方的标题
        if text in ['基本情况介绍', '投资管理介绍', '财务状况', '风险管理及内控制度', 
                   '产品管理规模及业绩', '法律意见书', '其他事项', '尽调结论（非管理人填写部分）']:
            return True

        # 跳过说明性文本
        if text.startswith('（请根据实际') or text.startswith('(请根据实际'):
            return True
        if '请根据实际情况填写' in text:
            return True
        if '各细项无对应情况可不填相应细项' in text:
            return True
        if text.endswith('尽调底稿模板'):
            return True
            
        return False

    def mark_queries(self, doc: Document, queries: List[Dict]) -> Document:
        """标记文档中的查询
        
        Args:
            doc: docx文档对象
            queries: 查询列表
            
        Returns:
            Document: 标记后的文档对象
        """
        try:
            for query in queries:
                if query.get('position') and query['position'].get('paragraph'):
                    # 获取原始段落
                    original_para = query['position']['paragraph']
                    
                    # 在原始段落后添加查询段落
                    query_text = f"{{查询：{query['text']}}}"
                    new_para = doc.add_paragraph()
                    
                    # 移动新段落到原始段落后面
                    original_para._p.addnext(new_para._p)
                    
                    # 添加查询文本
                    run = new_para.add_run(query_text)
                    run.italic = True
                    run.font.color.rgb = RGBColor(0, 0, 255)  # 蓝色
                    
                    logger.info(f"标记查询: {query_text}")
            
            return doc
            
        except Exception as e:
            logger.error(f"标记查询失败: {str(e)}", exc_info=True)
            return doc 