from typing import Literal, Optional
import os

# LLM模型配置
ModelName = Literal[
    "DeepSeek-R1", "DeepSeek-V3", "Qwen3-235B-A22B", "Qwen2_5-VL-32B-Instruct"
]

# 默认配置
DEFAULT_MODEL: ModelName = "DeepSeek-R1"
DEFAULT_BASE_URL: str = os.getenv("LLM_API_URL", "http://llm.yanfuinvest.com/v1")
DEFAULT_API_KEY: str = os.getenv("LLM_API_KEY", "sk-ISyVIYc3933iApsiLaz-HQ")

# LLM提示词配置
TABLE_ANALYSIS_PROMPT = """你是一个专业的表格分析助手。请分析以下表格的结构和内容：

{table_content}

请特别注意以下几点：
1. 识别需要填写答案的空单元格位置
2. 理解表头和行列标题之间的关系，生成合适的查询
3. 如果单元格不为空，则不能作为答案位置
4. 对于需要多行填写的表格，根据第一行提取信息，按照1,2,3等顺序往下填写
5. 表格可能包含以下特征：
   - 末尾带"i"的内容表示需要查询
   - 空白单元格表示需要填写答案
   - 表头和行列标题的组合可能构成查询
   - 多行填写时需要保持顺序

请用JSON格式回答，包含以下字段：
{
    "table_type": "表格类型",
    "confidence": 0.95,  // 置信度
    "answer_position": "答案位置类型",
    "queries": [  // 查询列表
        {
            "text": "查询文本",
            "row": 0,
            "col": 0,
            "answer_positions": [  // 可能有多个答案位置
                {"row": 1, "col": 1, "context": ["相关上下文"]}
            ],
            "is_multi_row": false,  // 是否需要多行填写
            "order": 1  // 多行填写时的顺序
        }
    ],
    "metadata": {
        "has_header": true,
        "needs_row_col_combination": true,  // 是否需要组合行列标题
        "empty_cells_are_answers": true  // 空单元格是否为答案位置
    }
}"""

QUERY_ENHANCEMENT_PROMPT = """请根据以下上下文增强查询：

原始查询：{query}

上下文信息：
{context}

请特别注意：
1. 如果查询涉及表格的行列标题组合，确保包含两者
2. 如果是多行填写的情况，标注顺序信息
3. 如果有特殊标记，保留其语义

请生成一个更完整的查询，确保：
1. 包含必要的上下文信息
2. 保持查询的简洁性
3. 明确指出具体的信息需求

请直接返回增强后的查询文本，不需要其他解释。"""

"""表格处理器配置"""

# 表格大小限制
TABLE_SIZE_LIMITS = {
    'max_rows': 50,  # 最大行数
    'max_cols': 10,  # 最大列数
    'max_cells': 200,  # 最大单元格数
    'form_max_rows': 100  # 表单类型表格的最大行数
}

# 表格类型检测配置
TABLE_TYPE_CONFIG = {
    'form_confidence_threshold': 0.4,  # 表单类型的置信度阈值
    'matrix_confidence_threshold': 0.6,  # 矩阵类型的置信度阈值
    'complex_confidence_threshold': 0.7,  # 复杂类型的置信度阈值
}

# 查询生成配置
QUERY_CONFIG = {
    'form_field_suffix': '：',  # 表单字段的后缀
    'query_template': '{{查询：{text}}}',  # 查询模板
    'skip_empty_cells': True,  # 是否跳过空单元格
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# 处理模式配置
PROCESS_MODE_CONFIG = {
    'default_mode': 'BALANCED',
    'supported_modes': ['BASIC', 'BALANCED', 'COMPLETE']
} 