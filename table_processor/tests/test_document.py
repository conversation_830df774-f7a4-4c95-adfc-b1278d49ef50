from docx import Document
from table_processor.processor import TableProcessor
from table_processor.table_types import AnalysisMode, QueryInfo
import os
import sys
from typing import Tuple, List, Dict
import json
from datetime import datetime

def validate_table_position(table, row: int, col: int) -> bool:
    """验证表格位置是否有效"""
    try:
        if row < 0 or col < 0:
            return False
        if row >= len(table.rows):
            return False
        if col >= len(table.rows[0].cells):
            return False
        return True
    except Exception:
        return False

def process_table(table, table_idx: int, processor: TableProcessor, mode: AnalysisMode) -> Tuple[int, int, List[Dict]]:
    """处理单个表格，返回查询数、成功标记数和查询列表"""
    try:
        print(f"\n处理表格 {table_idx + 1} (模式: {mode.name})")
        
        # 设置分析模式
        processor.detector.mode = mode
        
        # 获取表格信息
        table_info = processor._get_table_info(table)
        
        # 分析表格结构
        structure_info = processor.detector._analyze_table_structure(table_info['table_structure'])
        
        # 分析表格
        analysis = processor.detector.analyze_table(table_info)
        print(f"表格类型: {analysis.table_type}")
        print(f"置信度: {analysis.confidence}")
        
        # 提取查询
        queries = processor.detector.extract_queries(table_info, analysis)
        
        # 如果查询数量不足，尝试生成更多查询（仅在COMPLETE模式下）
        if mode == AnalysisMode.COMPLETE and len(queries) < 5 and len(table_info['table_structure']) > 1:
            try:
                # 遍历每个空单元格
                for empty_cell in structure_info['empty_cells']:
                    row_idx = empty_cell['row']
                    col_idx = empty_cell['col']
                    
                    # 跳过表头
                    if row_idx == 0:
                        continue
                        
                    # 获取字段名（通常在第一列）
                    field_name = ""
                    if col_idx > 0 and row_idx < len(table_info['table_structure']):
                        field_name = table_info['table_structure'][row_idx][0].get('text', '').strip()
                    
                    # 获取列标题（如果有）
                    col_header = ""
                    if row_idx > 0 and col_idx < len(table_info['table_structure'][0]):
                        col_header = table_info['table_structure'][0][col_idx].get('text', '').strip()
                    
                    # 构建查询文本
                    query_text = ""
                    if field_name and col_header:
                        query_text = f"{field_name}的{col_header}"
                    elif field_name:
                        query_text = field_name.rstrip('：:')
                    elif col_header:
                        query_text = col_header
                        
                    if query_text:
                        # 清理查询文本
                        query_text = processor.detector._clean_query_text(query_text)
                        if query_text:  # 如果清理后不为空
                            queries.append(QueryInfo(
                                text=query_text,
                                row=row_idx,
                                col=col_idx,
                                answer_positions=[{
                                    'row': row_idx,
                                    'col': col_idx,
                                    'context': processor.detector._get_cell_context(
                                        table_info['table_structure'], 
                                        row_idx, 
                                        col_idx
                                    )
                                }],
                                is_multi_row=False,
                                confidence=0.7
                            ))
            except Exception as e:
                print(f"生成额外查询失败: {str(e)}")
        
        print(f"找到 {len(queries)} 个查询")
        
        # 统计成功标记的查询数
        marked_count = 0
        query_records = []
        
        # 在表格中标记查询和答案位置
        for query_idx, query in enumerate(queries):
            try:
                print(f"\n查询 {query_idx + 1}:")
                print(f"文本: {query.text}")
                print(f"位置: 行={query.row}, 列={query.col}")
                print(f"置信度: {query.confidence}")
                
                query_record = {
                    'text': query.text,
                    'row': query.row,
                    'col': query.col,
                    'confidence': query.confidence,
                    'table_idx': table_idx
                }
                query_records.append(query_record)
                
                # 在答案位置插入标记
                for pos in query.answer_positions:
                    if validate_table_position(table, pos['row'], pos['col']):
                        row = table.rows[pos['row']]
                        cell = row.cells[pos['col']]
                        
                        # 如果单元格为空，插入查询
                        if not cell.text.strip():
                            cell.text = f"{{查询：{query.text}}}"
                            marked_count += 1
                    else:
                        print(f"警告: 无效的答案位置 - 行={pos['row']}, 列={pos['col']}")
                        
            except Exception as e:
                print(f"处理查询失败: {str(e)}")
                continue
                
        return len(queries), marked_count, query_records
        
    except Exception as e:
        print(f"处理表格失败: {str(e)}")
        return 0, 0, []

def process_document(input_path: str, mode: AnalysisMode) -> Tuple[str, List[Dict]]:
    """处理文档并标记查询位置，返回输出路径和查询记录"""
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_path):
            print(f"错误: 输入文件不存在 - {input_path}")
            return "", []
            
        # 创建处理器
        processor = TableProcessor()
        
        # 读取原始文档
        doc = Document(input_path)
        
        # 统计信息
        total_tables = len(doc.tables)
        total_queries = 0
        total_marked = 0
        all_queries = []
        
        print(f"开始处理文档，共 {total_tables} 个表格")
        
        # 处理每个表格
        for table_idx, table in enumerate(doc.tables):
            queries, marked, query_records = process_table(table, table_idx, processor, mode)
            total_queries += queries
            total_marked += marked
            all_queries.extend(query_records)
            
        # 生成输出文件名
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        output_dir = os.path.join(base_dir, "data", "尽调报告", "output")
        os.makedirs(output_dir, exist_ok=True)  # 确保输出目录存在
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(
            output_dir,
            f"output_tables_{mode.name.lower()}_{timestamp}.docx"
        )
        
        # 保存修改后的文档
        doc.save(output_path)
        
        # 输出统计信息
        print(f"\n处理完成 ({mode.name}):")
        print(f"- 总表格数: {total_tables}")
        print(f"- 总查询数: {total_queries}")
        print(f"- 成功标记: {total_marked}")
        print(f"\n已保存标记结果到: {output_path}")
        
        return output_path, all_queries
        
    except Exception as e:
        print(f"处理文档失败: {str(e)}")
        return "", []

def compare_modes(input_doc: str):
    """比较不同模式的处理结果"""
    results = {}
    
    # 处理每种模式
    for mode in AnalysisMode:
        output_path, queries = process_document(input_doc, mode)
        if output_path:
            results[mode.name] = {
                'output_path': output_path,
                'queries': queries
            }
            
    # 保存比较结果
    comparison_path = os.path.join(
        os.path.dirname(input_doc),
        'mode_comparison.json'
    )
    
    with open(comparison_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
        
    print(f"\n比较结果已保存到: {comparison_path}")

if __name__ == "__main__":
    # 设置文件路径
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    input_doc = os.path.join(base_dir, "table_processor", "data", "尽调报告", "尽职调查报告.docx")
    
    # 比较不同模式
    compare_modes(input_doc) 