from docx import Document
from ..utils.docx_utils import write_cell_content
import os

def test_write_cell():
    """测试单元格写入功能"""
    # 创建测试文档
    doc = Document()
    
    # 添加一个测试表格
    table = doc.add_table(rows=2, cols=2)
    
    # 测试场景1：只写入内容
    cell1 = table.cell(0, 0)
    write_cell_content(cell1, "测试内容1", None)
    
    # 测试场景2：只写入查询
    cell2 = table.cell(0, 1)
    write_cell_content(cell2, "", "测试查询2")
    
    # 测试场景3：同时写入内容和查询
    cell3 = table.cell(1, 0)
    write_cell_content(cell3, "测试内容3", "测试查询3")
    
    # 测试场景4：写入多行内容和查询
    cell4 = table.cell(1, 1)
    write_cell_content(cell4, "第一行\n第二行", "测试查询4")
    
    # 保存文档
    output_dir = os.path.join(os.path.dirname(__file__), "data")
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, "test_write_cell.docx")
    doc.save(output_path)
    print(f"\n测试文档已保存到: {output_path}")
    
    # 验证结果
    doc = Document(output_path)
    table = doc.tables[0]
    
    # 打印每个单元格的内容进行验证
    for i in range(2):
        for j in range(2):
            cell = table.cell(i, j)
            print(f"\n单元格 ({i}, {j}) 内容:")
            for para in cell.paragraphs:
                print(f"段落: {para.text}")

if __name__ == "__main__":
    test_write_cell() 