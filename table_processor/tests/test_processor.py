import unittest
from docx import Document
from .processor import TableProcessor
from .table_types import TableType, AnswerPosition

class TestTableProcessor(unittest.TestCase):
    def setUp(self):
        self.processor = TableProcessor()
        
    def test_basic_form_table(self):
        """测试基础表单类型"""
        doc = Document()
        table = doc.add_table(rows=3, cols=2)
        
        # 设置表格内容
        cells = table.rows[0].cells
        cells[0].text = "公司名称："
        cells[1].text = ""
        
        cells = table.rows[1].cells
        cells[0].text = "注册资本："
        cells[1].text = ""
        
        cells = table.rows[2].cells
        cells[0].text = "成立日期："
        cells[1].text = ""
        
        # 处理表格
        results = self.processor.process_document(doc)
        self.assertEqual(len(results), 1)
        
        # 验证分析结果
        analysis = results[0]['analysis']
        self.assertEqual(analysis.table_type, TableType.FORM)
        self.assertEqual(analysis.answer_position, AnswerPosition.NEXT_COLUMN)
        self.assertGreater(analysis.confidence, 0.8)
        
        # 验证查询
        queries = results[0]['queries']
        self.assertEqual(len(queries), 3)
        self.assertEqual(queries[0].text, "公司名称")
        self.assertEqual(queries[0].answer_positions[0], {'row': 0, 'col': 1})
        
    def test_alternate_rows_table(self):
        """测试隔行问答类型"""
        doc = Document()
        table = doc.add_table(rows=4, cols=1)
        
        # 设置表格内容
        table.rows[0].cells[0].text = "主要产品和服务："
        table.rows[1].cells[0].text = ""
        table.rows[2].cells[0].text = "主要客户群体："
        table.rows[3].cells[0].text = ""
        
        # 处理表格
        results = self.processor.process_document(doc)
        self.assertEqual(len(results), 1)
        
        # 验证分析结果
        analysis = results[0]['analysis']
        self.assertEqual(analysis.table_type, TableType.ALTERNATE_ROWS)
        self.assertEqual(analysis.answer_position, AnswerPosition.NEXT_ROW)
        
        # 验证查询
        queries = results[0]['queries']
        self.assertEqual(len(queries), 2)
        self.assertEqual(queries[0].text, "主要产品和服务")
        self.assertEqual(queries[0].answer_positions[0], {'row': 1, 'col': 0})
        
    def test_organization_table(self):
        """测试组织架构表"""
        doc = Document()
        table = doc.add_table(rows=3, cols=4)
        
        # 设置表头
        header_cells = table.rows[0].cells
        header_cells[0].text = "部门名称"
        header_cells[1].text = "部门人数"
        header_cells[2].text = "部门主要职能"
        header_cells[3].text = "主要负责人"
        
        # 设置数据行
        data_cells = table.rows[1].cells
        data_cells[0].text = "研发部"
        
        # 处理表格
        results = self.processor.process_document(doc)
        self.assertEqual(len(results), 1)
        
        # 验证分析结果
        analysis = results[0]['analysis']
        self.assertEqual(analysis.table_type, TableType.ORGANIZATION)
        self.assertEqual(analysis.answer_position, AnswerPosition.MATRIX_CELL)
        
        # 验证查询
        queries = results[0]['queries']
        self.assertTrue(any(q.text.startswith("研发部") for q in queries))
        
    def test_financial_table(self):
        """测试财务表格"""
        doc = Document()
        table = doc.add_table(rows=3, cols=4)
        
        # 设置表头（年份）
        header_cells = table.rows[0].cells
        header_cells[0].text = "项目"
        header_cells[1].text = "2023年"
        header_cells[2].text = "2022年"
        header_cells[3].text = "2021年"
        
        # 设置数据行
        row_cells = table.rows[1].cells
        row_cells[0].text = "营业收入"
        
        # 处理表格
        results = self.processor.process_document(doc)
        self.assertEqual(len(results), 1)
        
        # 验证分析结果
        analysis = results[0]['analysis']
        self.assertEqual(analysis.table_type, TableType.FINANCIAL)
        self.assertTrue(analysis.metadata['is_multi_year'])
        
        # 验证查询
        queries = results[0]['queries']
        self.assertTrue(any('营业收入' in q.text for q in queries))
        self.assertTrue(any('2023年' in q.text for q in queries))
        
    def test_city_team_table(self):
        """测试城市团队表"""
        doc = Document()
        table = doc.add_table(rows=3, cols=3)
        
        # 设置表头
        header_cells = table.rows[0].cells
        header_cells[0].text = "办公城市"
        header_cells[1].text = "所涉团队"
        header_cells[2].text = "人员数量"
        
        # 设置数据行
        row_cells = table.rows[1].cells
        row_cells[0].text = "北京"
        
        # 处理表格
        results = self.processor.process_document(doc)
        self.assertEqual(len(results), 1)
        
        # 验证分析结果
        analysis = results[0]['analysis']
        self.assertEqual(analysis.table_type, TableType.CITY_TEAM)
        
        # 验证查询
        queries = results[0]['queries']
        self.assertTrue(any(q.text.startswith("北京") for q in queries))
        
if __name__ == '__main__':
    unittest.main() 