from docx import Document
from docx.shared import Inches
import os
import sys
import logging

# 添加父目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_doc():
    """创建测试文档"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('测试文档', 0)
    
    # 添加二维表格
    table = doc.add_table(rows=4, cols=3)
    table.style = 'Table Grid'
    
    # 设置表头
    headers = ['办公城市', '所涉团队', '人员数量']
    for i, header in enumerate(headers):
        table.cell(0, i).text = header
        
    # 设置数据
    data = [
        ['北京', '投资团队', '10'],
        ['上海', '研究团队', '8'],
        ['深圳', '运营团队', '12']
    ]
    
    for i, row in enumerate(data):
        for j, value in enumerate(row):
            table.cell(i + 1, j).text = value
            
    # 保存文档
    output_dir = os.path.dirname(__file__)
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, 'test_two_dimensional.docx')
    doc.save(output_path)
    logger.info(f"测试文档已保存: {output_path}")
    return output_path

if __name__ == '__main__':
    # 创建测试文档
    test_doc_path = create_test_doc()
    
    # 导入处理器
    from table_processor.processor import TableProcessor
    from table_processor.table_types import AnalysisMode
    
    # 处理文档
    processor = TableProcessor(mode=AnalysisMode.BASIC)
    result = processor.process_document(
        input_path=test_doc_path,
        output_path=test_doc_path.replace('.docx', '_processed.docx')
    )
    
    logger.info(f"处理结果: {result}") 