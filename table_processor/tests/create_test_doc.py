from docx import Document
from docx.shared import Inches
import os

def create_test_document():
    """创建测试文档"""
    # 创建文档
    doc = Document()
    
    # 添加标题
    doc.add_heading('一、基本情况介绍', level=1)
    
    # 添加子标题
    doc.add_heading('1.1 简介及历史沿革', level=2)
    
    # 添加注释段落
    doc.add_paragraph('（注：请简单介绍公司的发展历程）')
    
    # 添加基本信息表格
    doc.add_heading('1.2 基本信息', level=2)
    table = doc.add_table(rows=14, cols=2)
    
    # 填充基本信息表格
    info_fields = [
        ('统一社会信用代码：', ''),
        ('注册号：', ''),
        ('商事主体名称：', ''),
        ('住所：', ''),
        ('法定代表人：', ''),
        ('注册资本（万元）：', ''),
        ('实缴资本(万元):', ''),
        ('经济性质：', ''),
        ('成立日期：', ''),
        ('营业期限：', ''),
        ('核准日期：', ''),
        ('实际控制人：', ''),
        ('联系人及职务：', ''),
        ('联系电话：', '')
    ]
    
    for i, (field, value) in enumerate(info_fields):
        table.cell(i, 0).text = field
        table.cell(i, 1).text = value
    
    # 添加一个一维表格
    doc.add_paragraph("一维表格测试")
    table1 = doc.add_table(rows=3, cols=2)
    table1.rows[0].cells[0].text = "字段1："
    table1.rows[0].cells[1].text = ""
    table1.rows[1].cells[0].text = "字段2："
    table1.rows[1].cells[1].text = ""
    table1.rows[2].cells[0].text = "字段3："
    table1.rows[2].cells[1].text = ""
    
    # 添加一个段落
    doc.add_paragraph("这是一个测试段落（注：这里需要生成查询）")
    
    # 添加一个二维表格
    doc.add_paragraph("二维表格测试")
    table2 = doc.add_table(rows=3, cols=3)
    table2.rows[0].cells[0].text = "项目"
    table2.rows[0].cells[1].text = "2023年"
    table2.rows[0].cells[2].text = "2024年"
    table2.rows[1].cells[0].text = "收入"
    table2.rows[1].cells[1].text = ""
    table2.rows[1].cells[2].text = ""
    table2.rows[2].cells[0].text = "支出"
    table2.rows[2].cells[1].text = ""
    table2.rows[2].cells[2].text = ""
    
    # 确保目录存在
    os.makedirs('test_docs', exist_ok=True)
    
    # 保存文档
    doc.save('test_docs/test.docx')
    print(f"测试文档已保存到: {os.path.abspath('test_docs/test.docx')}")

if __name__ == '__main__':
    create_test_document() 