import unittest
from docx import Document
from table_processor.paragraph_processor import ParagraphProcessor
import os

class TestParagraphProcessor(unittest.TestCase):
    def setUp(self):
        self.processor = ParagraphProcessor()
        self.test_doc_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "table_processor", "data", "尽调报告", "尽职调查报告.docx"
        )
        
    def test_document_processing(self):
        """测试文档处理"""
        # 确保文件存在
        self.assertTrue(os.path.exists(self.test_doc_path), f"测试文件不存在: {self.test_doc_path}")
        
        # 读取文档
        doc = Document(self.test_doc_path)
        
        # 处理文档
        print("\n开始处理段落问答...")
        result = self.processor.process_document(doc)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn('queries', result)
        self.assertGreater(len(result['queries']), 0)
        
        # 输出结果
        print(f"\n找到 {len(result['queries'])} 个查询:")
        
        for idx, query in enumerate(result['queries'], 1):
            print(f"\n查询 {idx}:")
            print(f"文本: {query['text']}")
            print(f"类型: {query['type']}")
            if query.get('position'):
                print("位置: 已标记")
            
        print(f"\n总计: {result['total_queries']} 个查询, {result['marked_queries']} 个已标记查询")
        
        # 标记文档
        marked_doc = self.processor.mark_queries(doc, result['queries'])
        
        # 保存标记后的文档
        output_path = os.path.join(
            os.path.dirname(self.test_doc_path),
            "尽职调查报告(私募证券投资基金公司)_qa_marked.docx"
        )
        marked_doc.save(output_path)
        print(f"\n已保存标记结果到: {output_path}")
        
    def test_title_detection(self):
        """测试标题检测"""
        test_cases = [
            # 数字层级标题
            ("1.1 投资理念", True, 2, "数字层级标题"),
            ("2.3.1 投资流程", True, 3, "数字层级标题"),
            ("1. 基本信息", True, 1, "数字层级标题"),
            
            # 中文数字标题
            ("一、公司简介", True, 1, "中文数字标题"),
            ("二、经营情况", True, 1, "中文数字标题"),
            
            # 英文字母标题
            ("A. 基本情况", True, 1, "英文字母标题"),
            ("B. 经营分析", True, 1, "英文字母标题"),
            
            # 罗马数字标题
            ("I. 概述", True, 1, "罗马数字标题"),
            ("II. 分析", True, 1, "罗马数字标题"),
            
            # 圆点标题
            ("• 重要提示", True, 1, "圆点标题"),
            ("◦ 补充说明", True, 1, "圆点标题"),
            
            # 短横线标题
            ("- 注意事项", True, 1, "短横线标题"),
            ("— 特别说明", True, 1, "短横线标题"),
            
            # 启发式标题
            ("风险提示：", True, 1, "启发式标题"),
            ("公司基本情况介绍", True, 1, "启发式标题"),
            ("是否存在重大风险？", True, 1, "启发式标题"),
            
            # 非标题
            ("普通文本", False, 0, ""),
            ("这是一段很长的文本，超过了100个字符。" * 3, False, 0, ""),
            ("这是一个普通句子。", False, 0, "")
        ]
        
        for text, expected_is_title, expected_level, expected_type in test_cases:
            is_title, level, title_type = self.processor._is_title(text)
            self.assertEqual(is_title, expected_is_title, f"标题检测失败: {text}")
            if expected_is_title:
                self.assertEqual(level, expected_level, f"层级检测失败: {text}")
                self.assertEqual(title_type, expected_type, f"类型检测失败: {text}")
            
    def test_annotation_extraction(self):
        """测试注释提取"""
        test_cases = [
            # 中文括号
            ("2.1 投资理念（注：请简要说明）", "请简要说明"),
            # 英文括号
            ("3.2 风控措施(注：包括具体制度)", "包括具体制度"),
            # 方括号
            ("4.1 组织架构【注：需要详细说明】", "需要详细说明"),
            # 全角方括号
            ("5.1 人员情况［注：包括学历分布］", "包括学历分布"),
            # 无注释
            ("普通文本", None),
            ("1.1 标题", None)
        ]
        
        for text, expected in test_cases:
            annotation = self.processor._extract_annotation(text)
            self.assertEqual(annotation, expected, f"注释提取失败: {text}")
            
    def test_title_characteristics(self):
        """测试标题特征检测"""
        positive_cases = [
            "风险提示：以下内容",
            "公司基本情况介绍",
            "是否存在重大风险？",
            "运营数据分析",
            "团队人员情况说明",
            "投资策略概述",
            "风控制度总结"
        ]
        
        negative_cases = [
            "这是一个普通的句子。",
            "这段文字超过了100个字符。" * 5,
            "普通文本",
            "这是一段描述性的文字，最后有句号。"
        ]
        
        for text in positive_cases:
            self.assertTrue(
                self.processor._has_title_characteristics(text),
                f"应该识别为标题: {text}"
            )
            
        for text in negative_cases:
            self.assertFalse(
                self.processor._has_title_characteristics(text),
                f"不应该识别为标题: {text}"
            )

if __name__ == '__main__':
    unittest.main() 