from docx import Document

# 创建测试文档
doc = Document()

# 添加一个一维表格
table1 = doc.add_table(rows=3, cols=2)
table1.rows[0].cells[0].text = "字段1："
table1.rows[0].cells[1].text = ""
table1.rows[1].cells[0].text = "字段2："
table1.rows[1].cells[1].text = ""
table1.rows[2].cells[0].text = "字段3："
table1.rows[2].cells[1].text = ""

# 添加一个段落
doc.add_paragraph("这是一个测试段落（注：这里需要生成查询）")

# 添加一个二维表格
table2 = doc.add_table(rows=3, cols=3)
table2.rows[0].cells[0].text = "项目"
table2.rows[0].cells[1].text = "2023年"
table2.rows[0].cells[2].text = "2024年"
table2.rows[1].cells[0].text = "收入"
table2.rows[1].cells[1].text = ""
table2.rows[1].cells[2].text = ""
table2.rows[2].cells[0].text = "支出"
table2.rows[2].cells[1].text = ""
table2.rows[2].cells[2].text = ""

# 保存文档
doc.save('test_sample.docx') 