import sys
import os
import unittest
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from docx import Document
from docx.text.paragraph import Paragraph
from docx.table import Table
from table_processor.paragraph_processor import ParagraphProcessor
from table_processor.processor import TableProcessor
from table_processor.table_types import AnalysisMode
from table_processor.main import process_document_with_queries
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestParagraphExtraction(unittest.TestCase):
    """测试段落提取功能"""
    
    def setUp(self):
        self.base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        self.test_doc_path = os.path.join(
            self.base_dir,
            "table_processor",
            "data",
            "尽调报告",
            "尽职调查报告.docx"
        )
        
    def test_document_exists(self):
        """测试文档是否存在"""
        self.assertTrue(os.path.exists(self.test_doc_path), f"测试文件不存在: {self.test_doc_path}")
        
    def test_document_structure(self):
        """测试文档结构分析"""
        doc = Document(self.test_doc_path)
        self.assertGreater(len(doc.paragraphs), 0, "文档应该包含段落")
        self.assertGreater(len(doc.tables), 0, "文档应该包含表格")
        
    def test_paragraph_processing(self):
        """测试段落处理"""
        processor = ParagraphProcessor()
        doc = Document(self.test_doc_path)
        
        # 只处理前5个段落进行测试
        test_paragraphs = doc.paragraphs[:5]
        doc._body._body.clear()  # 清空文档
        for para in test_paragraphs:
            doc._body._body.append(para._element)  # 添加测试段落
            
        sections = processor.process_document(doc)
        
        self.assertIsNotNone(sections, "应该返回段落列表")
        self.assertGreater(len(sections), 0, "应该至少有一个段落")
        
        # 检查段落结构
        for section in sections:
            self.assertIn('title', section, "段落应该有标题")
            self.assertIn('type', section, "段落应该有类型")
            self.assertIn('level', section, "段落应该有层级")
            
    def test_query_generation(self):
        """测试查询生成"""
        # 只测试 COMPLETE 模式
        mode = AnalysisMode.COMPLETE
        logger.info(f"使用 {mode.name} 模式测试查询生成")
        
        # 创建一个小型测试文档
        doc = Document(self.test_doc_path)
        test_doc = Document()
        
        # 只复制前3个表格进行测试
        for i, table in enumerate(doc.tables[:3]):
            logger.info(f"处理表格 {i+1}")
            test_doc.add_table(rows=len(table.rows), cols=len(table.rows[0].cells))
            for row_idx, row in enumerate(table.rows):
                for col_idx, cell in enumerate(row.cells):
                    test_doc.tables[i].rows[row_idx].cells[col_idx].text = cell.text
                    
        # 保存测试文档
        test_doc_path = os.path.join(
            self.base_dir,
            "table_processor",
            "data",
            "尽调报告",
            "test_sample.docx"
        )
        os.makedirs(os.path.dirname(test_doc_path), exist_ok=True)
        test_doc.save(test_doc_path)
        
        # 处理测试文档
        result = process_document_with_queries(test_doc_path, mode)
        
        self.assertTrue(result['success'], f"处理应该成功 ({mode.name})")
        self.assertTrue(os.path.exists(result['file_path']), f"输出文件应该存在 ({mode.name})")
        
        # 检查统计信息
        stats = result['stats']
        self.assertGreater(stats['total_queries'], 0, f"应该生成至少一个查询 ({mode.name})")
        self.assertGreater(stats['marked_queries'], 0, f"应该至少标记一个查询 ({mode.name})")
        
        # 输出详细的查询信息
        logger.info(f"\n处理完成 ({mode.name}):")
        logger.info(f"- 总查询数: {stats['total_queries']}")
        logger.info(f"- 标记查询数: {stats['marked_queries']}")
        logger.info(f"- 段落查询数: {stats['paragraph_queries']}")
        logger.info(f"- 表格查询数: {stats['table_queries']}")
        logger.info(f"\n输出文件: {result['file_path']}")
        
    def test_output_format(self):
        """测试输出格式"""
        # 使用小型测试文档
        test_doc_path = os.path.join(
            self.base_dir,
            "table_processor",
            "data",
            "尽调报告",
            "test_sample.docx"
        )
        
        if not os.path.exists(test_doc_path):
            self.skipTest("需要先运行 test_query_generation 生成测试文档")
            
        result = process_document_with_queries(test_doc_path, AnalysisMode.COMPLETE)
        
        # 检查输出文档
        doc = Document(result['file_path'])
        
        # 检查查询标记格式
        query_count = 0
        for para in doc.paragraphs:
            text = para.text
            if "{查询：" in text and "}" in text:
                query_count += 1
                logger.info(f"找到查询: {text}")
                
        self.assertGreater(query_count, 0, "输出文档应该包含查询标记")
        self.assertEqual(query_count, result['stats']['marked_queries'], "标记数量应该匹配")

if __name__ == '__main__':
    unittest.main() 