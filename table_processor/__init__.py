"""
Table processor package for processing complex Chinese documents.
"""

from .processor import TableProcessor
from .hybrid_detector import HybridTableDetector
from .table_types import TableType, AnalysisMode, TableAnalysisResult, QueryInfo
from .utils import docx_utils
from .models import TableProcessMode
from .main import process_document_with_queries

__all__ = [
    'TableProcessor',
    'HybridTableDetector',
    'TableType',
    'AnalysisMode',
    'TableAnalysisResult',
    'QueryInfo',
    'docx_utils',
    'TableProcessMode',
    'process_document_with_queries'
]
