import argparse
import logging
import os
import sys
import io
import warnings
import traceback
from .advanced_table_processor import TableProcessor
from .text_query_extractor import TextQueryExtractor
from .models import TableProcessMode
from .table_types import AnalysisMode

def process_document_with_queries(input_path: str, output_path: str = None, mode: str = 'balanced'):
    """处理文档并返回查询结果
    
    Args:
        input_path: 输入文件路径
        output_path: 输出文件路径（可选）
        mode: 处理模式 ('basic', 'balanced', 'complete')
        
    Returns:
        Dict: 包含处理结果的字典
    """
    # 验证输入文件是否存在
    if not os.path.isfile(input_path):
        return {
            'success': False,
            'file_path': None
        }
    
    # 忽略警告
    warnings.filterwarnings('ignore')
    
    # 禁用 jieba 的日志输出
    jieba_logger = logging.getLogger('jieba')
    jieba_logger.setLevel(logging.ERROR)
    
    try:
        # 如果没有指定输出路径，在输入文件同目录下创建
        if not output_path:
            input_dir = os.path.dirname(os.path.abspath(input_path))
            input_name = os.path.basename(input_path)
            output_name = input_name.replace('.docx', '_processed.docx')
            output_path = os.path.join(input_dir, output_name)
        
        # 确保输出目录存在
        output_dir = os.path.dirname(os.path.abspath(output_path))
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建临时文件路径用于中间处理结果
        temp_output = output_path.replace('.docx', '_temp.docx')
        
        # 第一步：使用 TableProcessor 处理表格
        table_processor = TableProcessor(input_path, temp_output)
        table_processor.process()
        
        # 第二步：使用 TextQueryExtractor 处理文本
        text_processor = TextQueryExtractor()
        text_processor.process_document(temp_output, output_path)
        
        # 删除临时文件
        if os.path.exists(temp_output):
            try:
                os.remove(temp_output)
            except Exception:
                pass
            
        return {
            'success': True,
            'file_path': output_path
        }
        
    except Exception as e:
        print(f"处理文档时发生错误: {str(e)}")
        traceback.print_exc()
        return {
            'success': False,
            'file_path': None
        }

def main():
    parser = argparse.ArgumentParser(description='处理文档中的表格和文本')
    parser.add_argument('input', type=str, help='输入文件路径')
    parser.add_argument('output', type=str, nargs='?', help='输出文件路径（可选，默认在输入文件同目录下创建）')
    parser.add_argument('--mode', type=str, choices=['basic', 'balanced', 'complete', 'BASIC', 'BALANCED', 'COMPLETE'], 
                      default='balanced', help='处理模式')
    
    args = parser.parse_args()
    
    try:
        # 处理文档
        result = process_document_with_queries(
            input_path=args.input,
            output_path=args.output,
            mode=args.mode
        )
        
        # 输出结果
        print(result)
        
        if not result['success']:
            sys.exit(1)
            
    except Exception:
        print({
            'success': False,
            'file_path': None
        })
        sys.exit(1)

if __name__ == '__main__':
    main() 