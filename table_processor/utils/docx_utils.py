import docx
import pandas as pd
import logging
from docx.oxml.text.paragraph import CT_P
from docx.oxml.table import CT_Tbl, CT_Row, CT_Tc
from docx.text.run import Run
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.table import _Cell
from docx.text.paragraph import Paragraph
from docx.shared import RGBColor
from typing import Optional, List, Dict, Any, Union
import re

logger = logging.getLogger(__name__)


def convert_to_json(index, element):
    """
    将word文件转换为json，并保留位置信息。
    args:
        index: 当前元素的索引
        element: 当前元素
    """
    json_data = {}
    json_data["index"] = index
    if isinstance(element, docx.document.Document):
        json_data["type"] = "Document"
        json_data["content"] = [convert_to_json(index, element) for index, element in enumerate(element.element.body.inner_content_elements)]
    elif isinstance(element, CT_P):
        json_data["type"] = "Paragraph"
        json_data["content"] = element.text
    elif isinstance(element, CT_Tbl):
        json_data["type"] = "Table"
        json_data["content"] = [convert_to_json(tri, tr) for tri, tr in enumerate(element.tr_lst)]
    elif isinstance(element, CT_Row):
        json_data["type"] = "Row"
        json_data["content"] = [convert_to_json(tci, tc) for tci, tc in enumerate(element.tc_lst)]
    elif isinstance(element, CT_Tc):
        json_data["type"] = "Cell"
        json_data["content"] = [convert_to_json(index, element) for index, element in enumerate(element.inner_content_elements)]
    else:
        json_data["type"] = "Unknown element"
        json_data["content"] = "Unknown element"
    return json_data


def convert_to_json_with_xpath(element, xpath="."):
    """
    将word文件转换为json，并保留位置信息。
    args:
        element: 当前元素
        xpath: 节点的xpath
    """
    json_data = {}
    json_data['xpath'] = xpath
    if isinstance(element, docx.document.Document):
        json_data["type"] = "Document"
        json_data["content"] = [convert_to_json_with_xpath(e, f"{xpath}.[{index}]") for index, e in enumerate(element.element.body.inner_content_elements)]
    elif isinstance(element, CT_P):
        json_data["type"] = "Paragraph"
        json_data["content"] = element.text
    elif isinstance(element, CT_Tbl):
        json_data["type"] = "Table"
        json_data["content"] = [convert_to_json_with_xpath(tr, f"{xpath}.[{tri}]") for tri, tr in enumerate(element.tr_lst)]
    elif isinstance(element, CT_Row):
        json_data["type"] = "Row"
        json_data["content"] = [convert_to_json_with_xpath(tc, f"{xpath}.[{tci}]") for tci, tc in enumerate(element.tc_lst)]
    elif isinstance(element, CT_Tc):
        json_data["type"] = "Cell"
        json_data["content"] = [convert_to_json_with_xpath(e, f"{xpath}.[{index}]") for index, e in enumerate(element.inner_content_elements)]
    else:
        json_data["type"] = "Unknown element"
        json_data["content"] = "Unknown element"
    return json_data


def get_element_by_xpath(element, xpath):
    """
    根据xpath获取对应element
    """
    if xpath == ".":
        return element
    xpath = xpath.lstrip(" ").lstrip(".")
    paths = xpath.split('.')

    path = paths[0]
    assert path.startswith('[') and path.endswith(']'), f"{path} 不合法"
    index = int(path[1:-1])
    if isinstance(element, docx.document.Document):
        child_element = element.element.body.inner_content_elements[index]
    elif isinstance(element, CT_Tbl):
        child_element = element.tr_lst[index]
    elif isinstance(element, CT_Row):
        child_element = element.tc_lst[index]
    elif isinstance(element, CT_Tc):
        child_element = element.inner_content_elements[index]
    else:
        raise Exception("Not supported element")

    child_xpath = xpath.lstrip(path)
    if len(child_xpath) == 0:
        return child_element
    else:
        return get_element_by_xpath(child_element, child_xpath)


def get_merged_cells(table):
    """
    获取表格中的合并单元格信息
    args:
        table: 目标表格 (CT_Tbl)
    returns:
        merged_cells: 合并单元格信息的字典，格式为 {(row, col): (rowspan, colspan)}
    """
    merged_cells = {}
    for row_idx, row in enumerate(table.tr_lst):
        for col_idx, cell in enumerate(row.tc_lst):
            # 获取垂直合并信息
            v_merge = cell.tcPr.find(qn('w:vMerge'))
            h_merge = cell.tcPr.find(qn('w:gridSpan'))
            
            if v_merge is not None or h_merge is not None:
                rowspan = 1
                colspan = 1
                
                # 处理垂直合并
                if v_merge is not None:
                    if v_merge.val == 'restart':
                        # 找到合并的结束位置
                        for i in range(row_idx + 1, len(table.tr_lst)):
                            next_cell = table.tr_lst[i].tc_lst[col_idx]
                            if next_cell.tcPr.find(qn('w:vMerge')) is None:
                                break
                            rowspan += 1
                
                # 处理水平合并
                if h_merge is not None:
                    colspan = int(h_merge.val)
                
                if rowspan > 1 or colspan > 1:
                    merged_cells[(row_idx, col_idx)] = (rowspan, colspan)
    
    return merged_cells


def extract_text(element, final_text=True, handle_merged_cells=True):
    """
    提取元素中的文本信息，段落间使用\n拼接，table转换为markdown
    args:
        element: 目标元素
        final_text: 是否返回最终的文本
        handle_merged_cells: 是否处理合并单元格
    """
    if isinstance(element, CT_P):
        text = element.text
    elif isinstance(element, docx.document.Document):
        texts = [extract_text(element) for element in element.element.body.inner_content_elements]
        if final_text:
            text = '\n'.join(texts)
        else:
            text = texts
    elif isinstance(element, CT_Tbl):
        if handle_merged_cells:
            merged_cells = get_merged_cells(element)
            texts = []
            for row_idx, row in enumerate(element.tr_lst):
                row_texts = []
                for col_idx, cell in enumerate(row.tc_lst):
                    # 检查是否是合并单元格的起始位置
                    if (row_idx, col_idx) in merged_cells:
                        rowspan, colspan = merged_cells[(row_idx, col_idx)]
                        cell_text = extract_text(cell)
                        # 将合并单元格的内容只放在起始位置
                        row_texts.append(cell_text)
                        # 其他位置填充空字符串
                        row_texts.extend([''] * (colspan - 1))
                    else:
                        # 检查是否在其他合并单元格的范围内
                        skip = False
                        for (m_row, m_col), (rowspan, colspan) in merged_cells.items():
                            if (m_row <= row_idx < m_row + rowspan and 
                                m_col <= col_idx < m_col + colspan):
                                skip = True
                                break
                        if not skip:
                            row_texts.append(extract_text(cell))
                texts.append(row_texts)
        else:
            texts = [extract_text(tr, final_text=False) for tr in element.tr_lst]
        
        df = pd.DataFrame(texts)
        text = df.to_markdown()
    elif isinstance(element, CT_Row):
        texts = [extract_text(tc) for tc in element.tc_lst]
        if final_text:
            text = '\t'.join(texts)
        else:
            text = [extract_text(tc) for tc in element.tc_lst]
    elif isinstance(element, CT_Tc):
        text = '\n'.join([extract_text(tc_element) for tc_element in element.inner_content_elements])
    else:
        text = "Unknown element"
    return text


def get_table_elements(document):
    """
    获取document中的所有table元素
    """
    return [element for element in document.element.body.inner_content_elements if isinstance(element, CT_Tbl)]


def write_cell_content(cell, content: str, query: Optional[str] = None, position_info: Optional[Dict] = None) -> None:
    """写入单元格内容
    
    Args:
        cell: docx单元格对象 (可以是 _Cell 或 CT_Tc)
        content: 要写入的内容
        query: 查询内容（如果有）
        position_info: 位置信息字典，包含 row_idx 和 col_idx
    """
    if cell is None:
        return
        
    try:
        # 清空单元格内容
        for child in cell.inner_content_elements:
            cell.remove(child)
            
        # 创建新段落
        new_p = OxmlElement('w:p')
        cell.append(new_p)
        
        # 创建运行对象
        new_r = OxmlElement('w:r')
        new_p.append(new_r)
        
        # 创建文本对象
        new_text = OxmlElement('w:t')
        new_text.text = content.strip() if content else ""
        new_r.append(new_text)
        
        # 如果有查询内容，添加新段落
        if query:
            # 清理查询文本
            def clean_query_text(text):
                # 移除标题相关的后缀
                text = re.sub(r"的[一二三四五六七八九十]、.*?$", "", text)
                text = re.sub(r"的.*?基本情况$", "", text)
                text = re.sub(r"的.*?基本信息$", "", text)
                text = re.sub(r"的.*?联系方式$", "", text)
                text = re.sub(r"的一、管理人基本情况$", "", text)
                text = re.sub(r"一、管理人基本情况$", "", text)
                text = re.sub(r"管理人基本情况$", "", text)
                text = re.sub(r"基本情况$", "", text)
                text = re.sub(r"基本信息$", "", text)
                text = re.sub(r"联系方式$", "", text)
                
                # 移除括号及其内容
                text = re.sub(r"（[^）]*）", "", text)
                text = re.sub(r"\([^)]*\)", "", text)
                
                # 移除多余的空格和换行
                text = re.sub(r"\s+", " ", text)
                
                # 移除多余的"的"字
                text = re.sub(r"的+", "的", text)
                
                # 清理空白字符
                text = text.strip()
                
                # 如果文本以"的"开头或结尾，去掉它
                text = text.strip("的")
                
                return text
                
            # 检查是否包含禁止的标题
            def contains_forbidden_title(text):
                forbidden_titles = [
                    "一、管理人基本情况",
                    "管理人基本情况",
                    "基本情况",
                    "基本信息",
                    "联系方式"
                ]
                return any(title in text for title in forbidden_titles)
                
            # 清理查询文本
            cleaned_query = clean_query_text(query)
            
            # 如果清理后的文本为空或包含禁止的标题，跳过写入
            if not cleaned_query or contains_forbidden_title(cleaned_query):
                return
                
            # 格式化查询文本
            formatted_query = f"{{查询：{cleaned_query}}}"
            
            # 创建新段落用于查询
            query_p = OxmlElement('w:p')
            cell.append(query_p)
            
            # 创建运行对象
            query_r = OxmlElement('w:r')
            query_p.append(query_r)
            
            # 设置查询文本的格式
            rPr = OxmlElement('w:rPr')
            
            # 设置斜体
            i = OxmlElement('w:i')
            i.set(qn('w:val'), 'true')
            rPr.append(i)
            
            # 设置蓝色
            color = OxmlElement('w:color')
            color.set(qn('w:val'), '0000FF')
            rPr.append(color)
            
            query_r.append(rPr)
            
            # 创建文本对象
            query_text = OxmlElement('w:t')
            query_text.text = formatted_query
            query_r.append(query_text)
            
    except Exception as e:
        logger.error(f"写入单元格失败: {str(e)}", exc_info=True)


def write_table_cell(table, row_idx, col_idx, content, query=None):
    """
    写入表格单元格内容
    args:
        table: 目标表格 (CT_Tbl)
        row_idx: 行索引
        col_idx: 列索引
        content: 写入的内容
        query: 查询内容（如果有）
    """
    try:
        cell = table.tr_lst[row_idx].tc_lst[col_idx]
        # 获取合并单元格信息
        merged_cells = get_merged_cells(table)
        merged_info = None
        
        # 检查是否是合并单元格
        if (row_idx, col_idx) in merged_cells:
            merged_info = merged_cells[(row_idx, col_idx)]
            
        position_info = {
            'row_idx': row_idx,
            'col_idx': col_idx,
            'merged_info': merged_info
        }
        
        return write_cell_content(cell, content, query, position_info)
    except IndexError:
        return False 