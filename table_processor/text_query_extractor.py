from docx import Document
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re

class TextQueryExtractor:
    def __init__(self):
        self.query_tag = "{查询："
        # 使用XML命名空间
        self.nsmap = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
        self.list_count = 0
        self.heading_count = 0 
        self.query_count = 0
        
    def process_document(self, input_path, output_path):
        """处理整个Word文档"""
        doc = Document(input_path)
        print(f"===== 开始文本标记处理 =====")
        print(f"文档段落总数: {len(doc.paragraphs)}")
        print(f"文档表格数量: {len(doc.tables)}")
        
        self.add_queries_to_doc(doc)
        
        print(f"===== 文本标记完成 =====")
        print(f"处理的编号段落数: {self.list_count}")
        print(f"处理的标题数: {self.heading_count}")
        print(f"添加的查询标记数: {self.query_count}")
        doc.save(output_path)
    
    def add_queries_to_doc(self, doc):
        """为文档添加查询标记"""
        paragraphs = list(doc.paragraphs)
        i = 0
        while i < len(paragraphs):
            para = paragraphs[i]
            
            # 跳过已有查询标记的段落
            if self.has_query_tag(para.text):
                i += 1
                continue
            
            # 跳过表格标题
            if self.is_table_title(para):
                i += 1
                continue
                
            # 首先检查是否是主标题
            if self.is_section_title(para):
                i += 1
                continue
                
            # 检查是否是次级标题（如1.3.1等）
            if self.is_numbered_heading(para):
                next_para = paragraphs[i+1] if i+1 < len(paragraphs) else None
                next_next_para = paragraphs[i+2] if i+2 < len(paragraphs) else None
                
                # 检查是否有注释行
                has_note = next_para and self.is_note_paragraph(next_para)
                
                # 检查是否后面紧跟表格
                next_is_table = self.is_followed_by_table(doc, i + (1 if has_note else 0))
                
                # 如果后面紧跟表格，跳过生成查询
                if next_is_table:
                    i += 1
                    continue
                
                # 获取标题文本（移除编号）
                query = self.extract_query_from_heading(para.text)
                if not query:
                    i += 1
                    continue
                    
                # 如果有注释行且后面是空行，在空行处添加查询
                if has_note and next_next_para and self.is_empty_paragraph(next_next_para):
                    next_next_para.text = f"{' ' * self.get_indent_level(para)}{self.query_tag}{query}]}}"
                    self.query_count += 1
                    i += 3  # 跳过注释行和空行
                # 如果标题后直接是空行，在空行处添加查询
                elif not has_note and next_para and self.is_empty_paragraph(next_para):
                    next_para.text = f"{' ' * self.get_indent_level(para)}{self.query_tag}{query}]}}"
                    self.query_count += 1
                    i += 2  # 跳过空行
                else:
                    i += 1
                continue
                
            # 处理普通编号段落（如名称：、办公地址：等）
            if self.is_list_item(para):
                self.list_count += 1
                next_para = paragraphs[i+1] if i+1 < len(paragraphs) else None
                
                # 检查是否后面紧跟表格
                next_is_table = self.is_followed_by_table(doc, i)
                if next_is_table:
                    i += 1
                    continue
                
                # 提取冒号后的内容作为查询
                query = self.extract_query_from_list(para.text)
                if query:
                    # 总是在下一行添加查询
                    if next_para:
                        if self.is_empty_paragraph(next_para):
                            next_para.text = f"{' ' * self.get_indent_level(para)}{self.query_tag}{query}]}}"
                        else:
                            # 插入新段落
                            new_para = doc.add_paragraph()
                            new_para.text = f"{' ' * self.get_indent_level(para)}{self.query_tag}{query}]}}"
                            # 将新段落插入到当前段落之后
                            para._p.addnext(new_para._p)
                            paragraphs = list(doc.paragraphs)  # 刷新段落列表
                    else:
                        new_para = doc.add_paragraph()
                        new_para.text = f"{' ' * self.get_indent_level(para)}{self.query_tag}{query}]}}"
                        paragraphs = list(doc.paragraphs)
                    self.query_count += 1
                i += 1
            else:
                i += 1
                
    def is_table_title(self, paragraph):
        """判断是否是表格标题"""
        text = paragraph.text.strip()
        # 检查是否以表格编号开始（如1.3.1）且包含"表"字
        return bool(re.match(r'^\d+\.\d+\.\d+\s*[表图]', text))
            
    def is_list_item(self, paragraph):
        """判断是否是编号段落（如名称：、办公地址：等）"""
        text = paragraph.text.strip()
        
        # 检查是否以冒号结尾的短语
        if re.match(r'^[^：:]+[：:]', text):
            return True
            
        # 检查是否是数字编号开头的段落（如：2、近一年...）
        if re.match(r'^[1-9][、\.](?!\d)', text):
            return True
            
        # 检查段落的XML结构
        try:
            pPr = paragraph._element.pPr
            if pPr is not None and pPr.numPr is not None:
                return True
        except AttributeError:
            pass
            
        return False
            
    def is_section_title(self, paragraph):
        """判断是否是主标题（如：一、公司概况）"""
        text = paragraph.text.strip()
        return bool(re.match(r'^[一二三四五六七八九十][、\.]', text))
        
    def is_numbered_heading(self, paragraph):
        """判断是否是次级标题（如：1.3.2 股东简介）"""
        text = paragraph.text.strip()
        # 匹配形如1.3.2的编号格式
        return bool(re.match(r'^\d+(\.\d+)+\s', text))
        
    def extract_query_from_list(self, text):
        """从编号文本提取查询问题"""
        # 移除编号
        text = text.strip()
        
        # 移除数字编号（如：2、3、4、等）
        text = re.sub(r'^[1-9][、\.](?!\d)\s*', '', text)
        
        # 处理冒号后的内容
        if "：" in text:
            parts = text.split("：")
            query = parts[0].strip()  # 使用冒号前的内容作为查询
            return query if query else None
        elif ":" in text:
            parts = text.split(":")
            query = parts[0].strip()  # 使用冒号前的内容作为查询
            return query if query else None
            
        # 如果没有冒号，直接使用整个文本
        return text.strip() if text else None

    def extract_query_from_heading(self, text):
        """从标题文本提取查询问题"""
        # 移除编号
        text = re.sub(r'^\d+(\.\d+)+\s*', '', text.strip())
        # 移除（查询：xxx）这样的标记
        text = re.sub(r'\（查询[:：].*?\）', '', text)
        return text.strip() if text else None

    def is_empty_paragraph(self, paragraph):
        """判断是否是空段落"""
        return not paragraph.text.strip()
    
    def is_note_paragraph(self, paragraph):
        """判断是否是注释段落"""
        text = paragraph.text.strip()
        return text.startswith('(注：') or text.startswith('（注：')
        
    def has_query_tag(self, text):
        """检查文本是否已经包含查询标记"""
        return self.query_tag in text
        
    def get_indent_level(self, paragraph):
        """获取段落缩进级别"""
        return len(paragraph.text) - len(paragraph.text.lstrip())
        
    def is_followed_by_table(self, doc, para_index):
        """判断段落后面是否紧跟表格"""
        try:
            # 获取段落在文档中的位置
            para_element = doc.paragraphs[para_index]._element
            # 获取下一个元素
            next_element = para_element.getnext()
            # 检查下一个元素是否是表格
            return next_element is not None and next_element.tag.endswith('tbl')
        except (IndexError, AttributeError):
            return False
        
if __name__ == "__main__":
    extractor = TextQueryExtractor()
    input_file = "尽职调查.docx"
    output_file = "尽职调查_查询标记后.docx"
    extractor.process_document(input_file, output_file)
