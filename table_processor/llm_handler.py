from typing import List, Dict, Any, Optional
import logging
import json
import requests
import openai
import os
import re
import tempfile
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_log, after_log
from .table_types import TableType, AnswerPosition, TableAnalysisResult, QueryInfo
from .constants import (
    QUERY_ENHANCEMENT_PROMPT,
    QUERY_GENERATION_PROMPT,
    STRUCTURE_ANALYSIS_PROMPT,
    SUBTABLE_DETECTION_PROMPT
)
import hashlib
import pickle
from pathlib import Path
from .config import TABLE_SIZE_LIMITS

logger = logging.getLogger(__name__)

class LLMHandler:
    """处理与LLM的交互"""
    
    def __init__(self, 
                 model_name: str = "Qwen3-235B-A22B",
                 base_url: Optional[str] = "http://llm.yanfuinvest.com/v1",
                 api_key: Optional[str] = "sk-ISyVIYc3933iApsiLaz-HQ"):
        """初始化LLM处理器
        
        Args:
            model_name: 模型名称
            base_url: API基础URL
            api_key: API密钥
        """
        self.model_name = model_name
        self.base_url = base_url or os.getenv("LLM_API_URL", "http://llm.yanfuinvest.com/v1")
        self.api_key = api_key or os.getenv("LLM_API_KEY", "sk-ISyVIYc3933iApsiLaz-HQ")
        
        # 配置API客户端
        openai.api_base = self.base_url
        openai.api_key = self.api_key
        
        self.timeout = 60  # 增加超时时间到60秒
        
        # 初始化缓存目录（使用系统临时目录）
        temp_dir = tempfile.gettempdir()
        self.cache_dir = Path(temp_dir) / "table_processor_cache" / "llm_responses"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"初始化LLM处理器: {self.base_url}")
        logger.info(f"缓存目录: {self.cache_dir}")
        
    def _get_cache_key(self, messages: List[Dict], temperature: float) -> str:
        """生成缓存键
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            
        Returns:
            str: 缓存键
        """
        # 将消息和温度参数序列化为字符串
        data = json.dumps({
            'messages': messages,
            'temperature': temperature
        }, sort_keys=True)
        
        # 计算哈希值
        return hashlib.sha256(data.encode()).hexdigest()
        
    def _get_from_cache(self, cache_key: str) -> Optional[Dict]:
        """从缓存中获取响应
        
        Args:
            cache_key: 缓存键
            
        Returns:
            Optional[Dict]: 缓存的响应，如果不存在则返回None
        """
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.warning(f"读取缓存失败: {str(e)}")
                return None
        return None
        
    def _save_to_cache(self, cache_key: str, response: Dict):
        """保存响应到缓存
        
        Args:
            cache_key: 缓存键
            response: 响应数据
        """
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(response, f)
        except Exception as e:
            logger.warning(f"保存缓存失败: {str(e)}")
            
    def analyze_table(self, table_info: Dict[str, Any]) -> TableAnalysisResult:
        """使用LLM分析表格"""
        try:
            # 构建表格内容字符串
            table_content = self._format_table_content(table_info)
            
            # 构建提示词
            prompt = TABLE_ANALYSIS_PROMPT.format(table_content=table_content)
            
            # 调用LLM
            response = self._call_llm([
                {"role": "system", "content": "你是一个专业的表格分析助手，擅长分析表格结构和内容。"},
                {"role": "user", "content": prompt}
            ])
            
            # 解析响应
            return self._parse_table_analysis(response)
            
        except Exception as e:
            print(f"LLM分析失败: {str(e)}")
            return TableAnalysisResult()
            
    def enhance_query(self, query: str, context: Dict[str, Any]) -> str:
        """增强查询，使用LLM模型改进查询质量"""
        try:
            # 构建提示
            prompt = f"""请根据上下文改进以下查询，使其更加准确和完整：

查询：{query}

上下文：
{json.dumps(context, ensure_ascii=False, indent=2)}

要求：
1. 保持查询的核心含义不变
2. 添加必要的限定词和补充说明
3. 使查询更加明确和具体
4. 返回改进后的查询文本
"""
            
            # 调用LLM模型
            response = self._call_llm(prompt)
            if response and isinstance(response, str):
                return response.strip()
            return query
            
        except Exception as e:
            print(f"查询增强失败: {str(e)}")
            return query
            
    def _generate_question_from_title(self, title: str, annotation: str = "", supplementary: str = "") -> str:
        """根据标题生成问题"""
        try:
            # 构建提示
            prompt = f"""请将以下标题转换为自然的问题形式：

标题：{title}

注释：{annotation if annotation else '无'}
补充信息：{supplementary if supplementary else '无'}

要求：
1. 生成自然、流畅的问题
2. 保持原始含义
3. 如果有注释和补充信息，需要合理整合到问题中
4. 返回一个完整的问题句子
5. 如果是投资相关的标题，需要询问具体的投资方法、策略或机制
6. 如果是考核相关的标题，需要询问具体的考核标准和激励方式
7. 如果是市场地位相关的标题，需要询问具体的市场表现和获奖情况
"""
            
            # 调用LLM模型
            response = self._call_llm(prompt)
            if response and isinstance(response, str):
                return response.strip()
            
            # 如果LLM调用失败，使用基本的转换逻辑
            question = title.replace("介绍", "是怎样的")
            if not question.endswith(('？', '?')):
                question += '？'
                
            if annotation:
                question = question.rstrip('?？') + f"，{annotation}？"
                
            if supplementary:
                question = question.rstrip('?？') + f"，需要说明{supplementary}？"
                
            return question
            
        except Exception as e:
            print(f"问题生成失败: {str(e)}")
            return title
            
    @retry(
        stop=stop_after_attempt(3),  # 最多重试3次
        wait=wait_exponential(multiplier=1, min=4, max=10),  # 指数退避
        retry=retry_if_exception_type(requests.exceptions.RequestException),
        before=before_log(logger, logging.DEBUG),
        after=after_log(logger, logging.DEBUG)
    )
    def _call_llm(self, messages: List[Dict], temperature: float = 0.7) -> Dict:
        """调用LLM服务"""
        # 生成缓存键
        cache_key = self._get_cache_key(messages, temperature)
        
        # 尝试从缓存获取
        cached_response = self._get_from_cache(cache_key)
        if cached_response:
            logger.debug("使用缓存的响应")
            return cached_response
            
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                },
                json={
                    "model": "Qwen3-235B-A22B",
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": 2000,
                    "response_format": {
                        "type": "json_object"
                    }
                },
                timeout=120  # 增加超时时间到120秒
            )
            
            if response.status_code != 200:
                raise requests.exceptions.RequestException(
                    f"LLM服务返回错误: {response.status_code} - {response.text}"
                )
            
            result = response.json()
            
            # 保存到缓存
            self._save_to_cache(cache_key, result)
            
            return result
            
        except requests.exceptions.Timeout:
            raise requests.exceptions.RequestException("LLM服务请求超时")
        except requests.exceptions.RequestException as e:
            raise e
        except Exception as e:
            raise requests.exceptions.RequestException(f"调用LLM服务失败: {str(e)}")
            
    def _format_table_content(self, table_info: Dict[str, Any]) -> str:
        """格式化表格内容"""
        structure = table_info.get('table_structure', [])
        if not structure:
            return ""
            
        lines = []
        for row_idx, row in enumerate(structure):
            cells = [f"'{cell['text'].strip()}'" for cell in row]
            lines.append(f"行 {row_idx}: {' | '.join(cells)}")
            
        return "\n".join(lines)
        
    def _parse_table_analysis(self, response: str) -> TableAnalysisResult:
        """解析表格分析结果"""
        try:
            # 尝试直接解析JSON
            try:
                data = json.loads(response)
            except:
                # 如果直接解析失败，尝试提取JSON部分
                start = response.find('{')
                end = response.rfind('}') + 1
                if start == -1 or end == 0:
                    raise ValueError("响应中没有找到JSON")
                    
                json_str = response[start:end]
                data = json.loads(json_str)
            
            # 创建结果对象
            result = TableAnalysisResult()
            
            # 设置基本属性
            if 'table_type' in data:
                try:
                    result.table_type = TableType[data['table_type'].upper()]
                except:
                    result.table_type = TableType.UNKNOWN
                    
            result.confidence = float(data.get('confidence', 0.0))
            
            if 'answer_position' in data:
                try:
                    result.answer_position = AnswerPosition[data['answer_position'].upper()]
                except:
                    result.answer_position = AnswerPosition.UNKNOWN
            
            # 设置元数据
            if 'metadata' in data:
                result.metadata.update(data['metadata'])
                
            # 处理查询列表
            if 'queries' in data:
                queries = []
                for q in data['queries']:
                    query = QueryInfo(
                        text=q['text'],
                        row=q.get('row', 0),
                        col=q.get('col', 0),
                        answer_positions=q.get('answer_positions', []),
                        is_multi_row=q.get('is_multi_row', False),
                        needs_context=q.get('needs_context', False),
                        metadata=q.get('metadata', {})
                    )
                    queries.append(query)
                result.queries = queries
                
            return result
            
        except Exception as e:
            print(f"解析表格分析结果失败: {str(e)}")
            return TableAnalysisResult()

    def query(self, prompt: str) -> str:
        """向LLM发送查询并获取响应"""
        try:
            # 调用LLM API
            response = self._call_llm(prompt)
            if response:
                return response.strip()
            return ""
            
        except Exception as e:
            logger.error(f"查询失败: {str(e)}", exc_info=True)
            return ""

    def analyze_table_type(self, table_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """分析表格类型"""
        try:
            # 获取表格结构
            structure = table_info.get('table_structure', [])
            if not structure:
                return None
                
            # 分析表格特征
            row_count = len(structure)
            col_count = len(structure[0]) if row_count > 0 else 0
            
            # 检查是否为表单类型
            if self._is_form_table(structure):
                return {
                    "table_type": TableType.FORM,
                    "confidence": 0.9
                }
                
            # 检查是否为矩阵类型
            if self._is_matrix_table(structure):
                return {
                    "table_type": TableType.MATRIX,
                    "confidence": 0.8
                }
                
            # 检查是否为交替行类型
            if self._is_alternate_rows_table(structure):
                return {
                    "table_type": TableType.ALTERNATE_ROWS,
                    "confidence": 0.75
                }
                
            # 默认返回未知类型
            return {
                "table_type": TableType.UNKNOWN,
                "confidence": 0.5
            }
            
        except Exception as e:
            logger.error(f"表格类型分析失败: {str(e)}")
            return None
            
    def _is_form_table(self, structure: List[List[Dict[str, Any]]]) -> bool:
        """检查是否为表单类型"""
        if len(structure) < 2:
            return False
            
        # 检查第一列是否都是字段名
        for row in structure:
            if not row[0].get('text', '').strip().endswith('：') and \
               not row[0].get('text', '').strip().endswith(':'):
                return False
                
        return True
        
    def _is_matrix_table(self, structure: List[List[Dict[str, Any]]]) -> bool:
        """检查是否为矩阵类型"""
        if len(structure) < 2 or len(structure[0]) < 2:
            return False
            
        # 检查是否有行标题和列标题
        has_row_headers = all(row[0].get('text', '').strip() for row in structure[1:])
        has_col_headers = all(cell.get('text', '').strip() for cell in structure[0][1:])
        
        return has_row_headers and has_col_headers
        
    def _is_alternate_rows_table(self, structure: List[List[Dict[str, Any]]]) -> bool:
        """检查是否为交替行类型"""
        if len(structure) < 2:
            return False
            
        # 检查是否有表头
        header_row = structure[0]
        if not all(cell.get('text', '').strip() for cell in header_row):
            return False
            
        # 检查数据行是否有规律变化
        data_rows = structure[1:]
        if not data_rows:
            return False
            
        return True
        
    def generate_questions(self, table_info: Dict[str, Any], table_type: TableType) -> List[Dict[str, Any]]:
        """生成问题"""
        try:
            structure = table_info.get('table_structure', [])
            if not structure:
                return []
                
            questions = []
            
            if table_type == TableType.FORM:
                # 为表单类型生成问题
                for row in structure:
                    field_name = row[0].get('text', '').strip().rstrip('：:')
                    if field_name:
                        questions.append({
                            'text': field_name,
                            'type': 'field',
                            'position': {'row': structure.index(row), 'col': 0}
                        })
                        
            elif table_type == TableType.MATRIX:
                # 为矩阵类型生成问题
                for col_idx, header in enumerate(structure[0][1:], 1):
                    header_text = header.get('text', '').strip()
                    if header_text:
                        for row_idx, row in enumerate(structure[1:], 1):
                            row_header = row[0].get('text', '').strip()
                            if row_header:
                                questions.append({
                                    'text': f"{row_header}的{header_text}",
                                    'type': 'matrix',
                                    'position': {'row': row_idx, 'col': col_idx}
                                })
                                
            elif table_type == TableType.ALTERNATE_ROWS:
                # 为交替行类型生成问题
                for col_idx, header in enumerate(structure[0]):
                    header_text = header.get('text', '').strip()
                    if header_text:
                        questions.append({
                            'text': header_text,
                            'type': 'column',
                            'position': {'row': 0, 'col': col_idx}
                        })
                        
            return questions
            
        except Exception as e:
            logger.error(f"问题生成失败: {str(e)}")
            return []

    def analyze_table_structure(self, prompt: str) -> Dict[str, Any]:
        """分析表格结构
        
        Args:
            prompt: 提示文本
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的表格分析助手。请分析给定表格的结构，并返回JSON格式的结果。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            # 调用LLM
            response = self._call_llm(messages, temperature=0.2)
            
            # 解析响应
            if isinstance(response, dict):
                # 如果响应已经是字典，直接返回
                return response
            elif isinstance(response, str):
                # 如果响应是字符串，尝试解析JSON
                try:
                    return json.loads(response)
                except json.JSONDecodeError:
                    # 如果无法解析JSON，返回基本结构
                    return {
                        'type': 'unknown',
                        'title': '',
                        'sections': [],
                        'confidence': 0.5
                    }
            else:
                # 如果响应格式不正确，返回基本结构
                return {
                    'type': 'unknown',
                    'title': '',
                    'sections': [],
                    'confidence': 0.5
                }
                
        except Exception as e:
            logger.error(f"分析表格结构失败: {str(e)}", exc_info=True)
            return {
                'type': 'unknown',
                'title': '',
                'sections': [],
                'confidence': 0.5
            }
            
    def generate_queries(self, prompt: str) -> Dict[str, Any]:
        """生成查询
        
        Args:
            prompt: 提示文本
            
        Returns:
            Dict[str, Any]: 生成的查询，包含：
                - queries: 查询列表
                - confidence: 置信度
        """
        try:
            # 构建消息
            messages = [
                {
                    'role': 'system',
                    'content': '你是一个专业的表格分析助手。请为给定表格生成查询，并返回JSON格式的结果。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ]
            
            # 调用LLM
            response = self._call_llm(messages, temperature=0.5)
            
            # 解析响应
            if isinstance(response, str):
                # 如果响应是字符串，尝试解析JSON
                try:
                    response_json = json.loads(response)
                    if isinstance(response_json, dict):
                        return response_json
                except json.JSONDecodeError:
                    logger.error(f"解析LLM响应失败: {response}")
                    return {'queries': [], 'confidence': 0.0}
            elif isinstance(response, dict):
                # 如果响应是字典，直接使用
                reply = response.get('choices', [{}])[0].get('message', {}).get('content', '')
                try:
                    return json.loads(reply)
                except json.JSONDecodeError:
                    logger.error(f"解析LLM响应失败: {reply}")
                    return {'queries': [], 'confidence': 0.0}
                
            return {'queries': [], 'confidence': 0.0}
            
        except Exception as e:
            logger.error(f"生成查询失败: {str(e)}", exc_info=True)
            return {'queries': [], 'confidence': 0.0}
            
    def get_query_template(self, subject: str, attribute: str) -> Optional[str]:
        """获取查询模板"""
        try:
            prompt = f"""请为主题"{subject}"和属性"{attribute}"生成一个自然的查询模板。
要求：
1. 使用{subject}、{attribute}等占位符
2. 保持语言自然流畅
3. 考虑属性的特点（如时间、地点、数量等）

示例：
- 时间相关："{subject}在{year}年的{attribute}"
- 地点相关："{location}的{subject}{attribute}"
- 一般属性："{subject}的{attribute}"
"""
            
            return self._call_llm([
                {"role": "system", "content": "你是一个专业的查询模板生成助手。"},
                {"role": "user", "content": prompt}
            ]).strip()
            
        except Exception as e:
            print(f"模板生成失败: {str(e)}")
            return None
            
    def analyze_cell_relations(self, table_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析单元格之间的关系"""
        prompt = "请分析以下表格中单元格之间的关系：\n\n"
        
        # 添加表格内容
        structure = table_info.get('table_structure', [])
        for row in structure:
            row_text = [f"'{cell['text'].strip()}'" for cell in row]
            prompt += f"{' | '.join(row_text)}\n"
            
        prompt += "\n请分析：\n"
        prompt += "1. 行与行之间的关系\n"
        prompt += "2. 列与列之间的关系\n"
        prompt += "3. 是否存在层级关系\n"
        prompt += "4. 是否存在时间序列\n"
        prompt += "5. 数据的变化规律\n"
        
        try:
            response = self._call_llm([
                {"role": "system", "content": "你是一个专业的表格关系分析助手。"},
                {"role": "user", "content": prompt}
            ])
            
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                print(f"JSON解析失败: {response}")
                return {}
                
        except Exception as e:
            print(f"调用LLM失败: {str(e)}")
            return {}

    def _is_table_too_large(self, table_data: List[List[Dict]]) -> bool:
        """检查表格是否过大"""
        if not table_data:
            return False
        
        rows = len(table_data)
        cols = len(table_data[0]) if table_data else 0
        
        # 对于两列的表单类型表格，使用特殊限制
        if cols == 2:
            return rows > TABLE_SIZE_LIMITS['form_max_rows']
        
        return (rows > TABLE_SIZE_LIMITS['max_rows'] or 
                cols > TABLE_SIZE_LIMITS['max_cols'] or 
                rows * cols > TABLE_SIZE_LIMITS['max_cells']) 