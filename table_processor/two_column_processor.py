import re
from typing import Dict, Any, List, Optional
from docx.oxml.table import CT_Tbl
from .utils.docx_utils import get_merged_cells, extract_text, write_cell_content
import logging

logger = logging.getLogger(__name__)

class TwoColumnProcessor:
    """专门处理两列复杂表格的处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.logger = logging.getLogger(__name__)
        
    def get_actual_columns(self, row, merged_cells, row_idx) -> int:
        """获取实际列数（考虑合并单元格）
        
        Args:
            row: 表格行
            merged_cells: 合并单元格信息
            row_idx: 行索引
            
        Returns:
            int: 实际列数
        """
        actual_cols = 0
        col_idx = 0
        while col_idx < len(row.tc_lst):
            if (row_idx, col_idx) in merged_cells:
                _, colspan = merged_cells[(row_idx, col_idx)]
                actual_cols += 1
                col_idx += colspan
            else:
                actual_cols += 1
                col_idx += 1
        return actual_cols
        
    def determine_query_type(self, text: str) -> str:
        """确定查询类型
        
        Args:
            text: 查询文本
            
        Returns:
            str: 查询类型
        """
        # 人员相关
        if any(keyword in text for keyword in ['姓名', '人员', '负责人', '代表', '法人']):
            return 'person'
        # 日期相关
        elif any(keyword in text for keyword in ['日期', '时间', '成立']):
            return 'date'
        # 金额相关
        elif any(keyword in text for keyword in ['金额', '资本', '规模', '注册资本', '实缴资本']):
            return 'amount'
        # 比例相关
        elif any(keyword in text for keyword in ['比例', '率']):
            return 'percentage'
        # 地址相关
        elif any(keyword in text for keyword in ['地址', '住所', '办公']):
            return 'address'
        # 联系方式相关
        elif any(keyword in text for keyword in ['电话', '传真', '联系']):
            return 'contact'
        # 代码相关
        elif any(keyword in text for keyword in ['代码', '编号', '信用代码']):
            return 'code'
        # 网址相关
        elif any(keyword in text for keyword in ['网址', 'url', '网站']):
            return 'url'
        # 范围相关
        elif any(keyword in text for keyword in ['范围', '经营范围']):
            return 'scope'
        # 类型相关
        elif any(keyword in text for keyword in ['类型', '性质']):
            return 'type'
        # 牌照相关
        elif any(keyword in text for keyword in ['牌照', '资质', '备案', '许可']):
            return 'license'
        # 默认类型
        else:
            return 'form_field'
            
    def clean_query_text(self, text: str) -> str:
        """清理查询文本
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        # 如果文本为空，直接返回空字符串
        if not text:
            return ""
            
        # 移除标点符号
        text = text.replace("：", "").replace(":", "")
        
        # 移除括号及其内容
        text = re.sub(r"（[^）]*）", "", text)
        text = re.sub(r"\([^)]*\)", "", text)
        
        # 移除多余的空格和换行
        text = re.sub(r"\s+", " ", text)
        
        # 移除多余的"的"字
        text = re.sub(r"的+", "的", text)
        
        # 清理空白字符
        text = text.strip()
        
        # 如果文本以"的"开头或结尾，去掉它
        text = text.strip("的")
        
        return text
        
    def process_table(self, table: CT_Tbl) -> Dict[str, Any]:
        """处理两列表格
        
        Args:
            table: docx表格对象
            
        Returns:
            Dict: 包含查询信息的字典
        """
        try:
            # 获取表格的行数和列数
            rows = table.tr_lst
            if not rows:
                return {'queries': [], 'marked_queries': []}
            
            first_row = rows[0]
            if not first_row.tc_lst:
                return {'queries': [], 'marked_queries': []}
            
            # 获取合并单元格信息
            merged_cells = get_merged_cells(table)
            
            queries = []
            marked_queries = []
            seen_queries = set()  # 用于去重
            
            # 遍历每一行
            for row_idx, row in enumerate(rows):
                # 获取实际列数（考虑合并单元格）
                actual_cols = self.get_actual_columns(row, merged_cells, row_idx)
                
                # 只处理实际列数为2的行
                if actual_cols == 2:
                    # 获取第一列的文本（作为查询）
                    first_cell = row.tc_lst[0]
                    original_text = extract_text(first_cell).strip()
                    
                    # 跳过空单元格
                    if not original_text:
                        continue
                        
                    # 清理查询文本
                    query_text = self.clean_query_text(original_text)
                    
                    # 跳过清理后为空的查询
                    if not query_text:
                        continue
                        
                    # 跳过重复的查询
                    if query_text in seen_queries:
                        continue
                    seen_queries.add(query_text)
                    
                    # 确定查询类型
                    query_type = self.determine_query_type(query_text)
                    
                    # 生成查询
                    query = {
                        'text': query_text,
                        'type': query_type,
                        'level': 0,
                        'title_type': '',
                        'annotation': ''
                    }
                    
                    queries.append(query)
                    
                    # 在第二列写入查询
                    try:
                        second_cell = row.tc_lst[1]
                        # 直接写入查询，不写入原始内容
                        write_cell_content(
                            second_cell, 
                            "",  # 不写入原始内容
                            query_text,  # 只写入查询
                            {'row_idx': row_idx, 'col_idx': 1}
                        )
                        marked_queries.append(query)
                    except Exception as e:
                        logger.error(f"写入查询失败: {str(e)}", exc_info=True)
            
            return {
                'queries': queries,
                'marked_queries': marked_queries
            }
            
        except Exception as e:
            logger.error(f"处理两列表格失败: {str(e)}", exc_info=True)
            return {'queries': [], 'marked_queries': []} 