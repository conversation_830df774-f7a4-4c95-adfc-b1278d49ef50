import requests
import os
import json
import toml

def test_llm_connection():
    print("Testing LLM Connection...")
    
    # 测试模型列表获取
    url = "http://llm.yanfuinvest.com/v1/models"
    headers = {
        "Authorization": f"Bearer sk-ISyVIYc3933iApsiLaz-HQ"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Models API Status Code: {response.status_code}")
        print(f"Available Models: {response.text}")
    except Exception as e:

        
        print(f"Models API Error: {e}")

def test_knowledge_base():
    print("\nTesting Knowledge Base Access...")
    config_path = "market/qa_agent/config.toml"
    
    try:
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
            
            # 获取知识库路径
            kb_path = config['knowledge_bases']['base_yf_knowledge_base']['path']
            kb_path = os.path.normpath(os.path.join(os.path.dirname(config_path), kb_path))
            print(f"Knowledge Base Path: {kb_path}")
            
            # 检查文件是否存在
            import glob
            files = glob.glob(kb_path)
            print(f"Found Knowledge Base Files: {files}")
            
            # 尝试读取文件内容
            if files:  # 如果找到了文件
                file = files[0]  # 只读取第一个文件作为测试
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = json.load(f)
                        print(f"Successfully read file: {file}")
                        if isinstance(content, dict):
                            print(f"Content keys: {list(content.keys())}")
                        elif isinstance(content, list):
                            print(f"Content is a list with {len(content)} items")
                            if content:  # 如果列表不为空
                                print(f"First item type: {type(content[0])}")
                                if isinstance(content[0], dict):
                                    print(f"First item keys: {list(content[0].keys())}")
                except json.JSONDecodeError as e:
                    print(f"JSON decode error in file {file}: {e}")
            else:
                print("No knowledge base files found")
    except FileNotFoundError as e:
        print(f"Config file not found: {e}")
    except KeyError as e:
        print(f"Missing key in config file: {e}")
    except Exception as e:
        print(f"Knowledge Base Error: {e}")

if __name__ == "__main__":
    test_llm_connection()
    test_knowledge_base()
