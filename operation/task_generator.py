import json
from docx import Document
from docx.oxml.table import CT_Tbl
from utils.doc_extract_util import extract_text, get_element


def generate_task(doc):
    tasks = []
    for index, element in enumerate(doc.element.body.inner_content_elements):
        if isinstance(element, CT_Tbl):
            table_position = {"index": index}
            for row_index, row in enumerate(element.tr_lst):
                row_position = table_position.copy()
                row_position["child_position"] = {"index": row_index}

                cells = row.tc_lst
                cell_texts = [extract_text(cell) for cell in cells]
                row_len = len(cell_texts)
                if row_len <= 2:
                    continue
                query = cell_texts[row_len - 2]
                answer_postion = row_position.copy()
                answer_postion["child_position"]["child_position"] = {"index": row_len - 1}
                tasks.append({'query': query, "answer_position": {"child_position": answer_postion}})
    return tasks


def test_generate_task():
    doc_file = "./market_source/衍复博裕中性三号私募证券投资基金_招募说明书.docx"
    document = Document(doc_file)
    tasks = generate_task(document)
    for task in tasks:
        element = get_element(document, task["answer_position"])
        task["answer"] = extract_text(element)
        print(f"Query: {task.get("query")}")
        print(f'Answer:{extract_text(element)}')
        print("-" * 100)
    with open("tasks.json", "w") as f:
        json.dump(tasks, f, ensure_ascii=False, indent=4)
    return tasks


if __name__ == "__main__":
    # test_generate_market_task()
