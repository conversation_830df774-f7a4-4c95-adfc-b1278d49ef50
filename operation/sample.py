import sys
print(sys.path)
from docx import Document
from langchain.chat_models import init_chat_model
from utils.doc_extract_util import extract_text
from pydantic import BaseModel, Field
from langchain_core.output_parsers.pydantic import PydanticOutputParser


class ExtractInfo(BaseModel):
    found: int = Field(description="是否找到符合要求的原文内容, 找到返回1, 否则返回-1")
    source: str = Field(description="原文内容")
    target: str = Field(description="需要提取的要素")


def get_model():
    return init_chat_model(
        # model="qwen3-0.6b",
        # model="qwen3-30b-a3b",
        # model="Qwen3-235B-A22B",
        # model="DeepSeek-V3",
        model="DeepSeek-R1",
        model_provider="openai",
        base_url="http://llm.yanfuinvest.com/v1",
        # base_url="http://***************:1234/v1",
        api_key="sk-ISyVIYc3933iApsiLaz-HQ",
        temperature=0
    )


def ask_single_loop(text, target):
    parser = PydanticOutputParser(pydantic_object=ExtractInfo)
    prompt = f"""
你是一个专业的金融人员，可以准确的从各个合同中提取出需要的要素。
要素解释：
管理费条款，是指合同中约定的管理费计提方法。
注意：
1. 提取内容需要保持原文内容，不要进行任何修改
尝试从下面文本
--------------------------------------------------------
{text}
--------------------------------------------------------
提取
{target}

{parser.get_format_instructions()}
    """
    model = get_model()
    result = model.invoke(prompt).content
    result = parser.parse(result)
    return result


def run(doc_file, target):
    document = Document(doc_file)
    text_list = extract_text(document)
    overlap_size = 5000
    start = 0
    max_token = 20480
    text = ""
    while start < len(text_list):
        text = text[-overlap_size:]
        while len(text) < max_token and start < len(text_list):
            text = text + "\n" + text_list[start]
            start = start + 1
        result = ask_single_loop(text, target)

        if result.found == 1:
            return result
    return None


def main():
    doc_file = 'operation/resources/测试_中信合同.docx'
    # target_list = [
    #     "基金名称",
    #     "基金架构",
    #     "基金类型",
    #     "计划募集规模",
    #     "运作方式",
    # ]
    target_list = [
        "投资范围",
        "投资限制",
        "投资策略",
        "业绩比较基准",
        "风险收益特征",
        "基金管理人"
    ]
    for target in target_list:
        result = run(doc_file, target)
        print(target, "Not found" if result is None else result.source)


if __name__ == "__main__":
    main()
