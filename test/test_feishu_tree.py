import requests
import json
import sys
import os
import toml
from pprint import pprint

# 获取项目根目录
root_dir = os.path.dirname(os.path.abspath(__file__))
qa_agent_config = os.path.join(root_dir, "llm_for_a", "market", "qa_agent", "config.toml")

def get_feishu_token_from_config():
    """从配置文件中获取飞书 token"""
    try:
        if not os.path.exists(qa_agent_config):
            print(f"配置文件不存在: {qa_agent_config}")
            return None, None
            
        config = toml.load(qa_agent_config)
        feishu_config = config.get("knowledge_bases", {}).get("feishu", {})
        folder_token = feishu_config.get("folder_token", "")
        user_access_token = feishu_config.get("user_access_token", "")
        
        print(f"从配置文件中获取到 token:")
        print(f"- folder_token: {folder_token}")
        print(f"- user_access_token: {user_access_token[:10]}...（部分显示）")
        
        return folder_token, user_access_token
    except Exception as e:
        print(f"读取配置文件出错: {e}")
        return None, None

def test_feishu_tree_api():
    """测试飞书树状结构 API"""
    folder_token, user_access_token = get_feishu_token_from_config()
    
    if not folder_token or not user_access_token:
        print("未能从配置文件获取有效的 token，请手动设置")
        folder_token = input("请输入 folder_token: ")
        user_access_token = input("请输入 user_access_token: ")
    
    # 构建 API URL (Flask 默认端口 5000)
    url = f"http://localhost:5000/api/feishu/tree?folder_token={folder_token}&user_access_token={user_access_token}"
    
    print(f"\n开始请求树状结构 API...")
    try:
        response = requests.get(url)
        print(f"API 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("\n=== 树状结构结果 ===")
            # 处理可能的响应格式差异
            tree_data = data.get("tree", data) if isinstance(data, dict) else data
            print(f"根节点数量: {len(tree_data)}")
            
            # 输出前 3 个根节点的基本信息
            print("\n前 3 个根节点概览:")
            for i, node in enumerate(tree_data[:3]):
                print(f"[{i+1}] {node.get('name')} ({node.get('type')}) - ID: {node.get('id')}")
                if node.get('type') == 'folder':
                    children = node.get('children', [])
                    print(f"   包含 {len(children)} 个子项")
                    # 显示文件夹的前 2 个子项
                    for j, child in enumerate(children[:2]):
                        print(f"   - {child.get('name')} ({child.get('type')})")
            
            # 将完整结果保存到文件
            with open("feishu_tree_result.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n完整结果已保存至: {os.path.abspath('feishu_tree_result.json')}")
                
        else:
            print(f"API 调用失败: {response.text}")
    except Exception as e:
        print(f"请求出错: {e}")

def test_feishu_file_content_api():
    """测试获取单个文件内容的 API"""
    folder_token, user_access_token = get_feishu_token_from_config()
    
    if not folder_token or not user_access_token:
        print("未能从配置文件获取有效的 token，请手动设置")
        folder_token = input("请输入 folder_token: ")
        user_access_token = input("请输入 user_access_token: ")
    
    file_token = input("请输入要获取内容的文件 token: ")
    
    # 构建 API URL (Flask 默认端口 5000)
    url = f"http://localhost:5000/api/feishu/file_content"
    
    print(f"\n开始请求文件内容 API...")
    try:
        payload = {
            "file_token": file_token,
            "folder_token": folder_token,
            "user_access_token": user_access_token
        }
        
        response = requests.post(url, json=payload)
        print(f"API 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            content = data.get("content", "")
            print("\n=== 文件内容预览 ===")
            # 显示文件内容的前 500 个字符
            print(content[:500] + "..." if len(content) > 500 else content)
            
            # 将完整内容保存到文件
            with open(f"feishu_file_{file_token}.md", "w", encoding="utf-8") as f:
                f.write(content)
            print(f"\n完整文件内容已保存至: {os.path.abspath(f'feishu_file_{file_token}.md')}")
        else:
            print(f"API 调用失败: {response.text}")
    except Exception as e:
        print(f"请求出错: {e}")

if __name__ == "__main__":
    print("=== 飞书树状结构 API 测试工具 ===")
    print("1. 测试文档树状结构 API")
    print("2. 测试文件内容 API")
    
    choice = input("请选择要测试的功能 (1/2): ")
    
    if choice == "1":
        test_feishu_tree_api()
    elif choice == "2":
        test_feishu_file_content_api()
    else:
        print("无效的选择")
