import requests
import json
import toml
import os
import lark_oapi as lark
from lark_oapi.api.drive.v1 import ListFileRequest, ListFileResponse

# 从配置文件获取token
qa_agent_config = os.path.join(os.path.dirname(os.path.abspath(__file__)), "llm_for_a", "market", "qa_agent", "config.toml")
config = toml.load(qa_agent_config)
user_access_token = config.get("knowledge_bases", {}).get("feishu", {}).get("user_access_token", "")

print("=== 测试获取用户所有文件 ===")
print(f"使用user_access_token: {user_access_token[:10]}...")

# 第1部分: 使用API接口获取所有文件
print("\n1. 使用API接口获取所有文件...")
url = f"http://localhost:5000/api/feishu/tree?user_access_token={user_access_token}"

try:
    response = requests.get(url)
    print(f"API状态码: {response.status_code}")
    
    if response.status_code == 200:
        api_data = response.json()
        # 保存结果到文件
        with open("feishu_api_all_files.json", "w", encoding="utf-8") as f:
            json.dump(api_data, f, ensure_ascii=False, indent=2)
        
        # 输出数据概览
        if isinstance(api_data, dict) and "tree" in api_data:
            tree = api_data["tree"]
            print(f"API获取到的文件数量: {len(tree)}")
            print("前几个文件/文件夹:")
            for i, item in enumerate(tree[:5]):
                print(f"- {item.get('name')} ({item.get('type')})")
        else:
            print("API返回了未知格式的数据")
    else:
        print(f"API请求失败: {response.text}")
except Exception as e:
    print(f"API请求出错: {e}")

# 第2部分: 直接使用SDK获取所有文件（参考示例代码）
print("\n2. 直接使用SDK获取所有文件...")
try:
    # 创建client
    client = lark.Client.builder() \
        .enable_set_token(True) \
        .log_level(lark.LogLevel.INFO) \
        .build()

    # 构造请求对象
    request = ListFileRequest.builder() \
        .order_by("EditedTime") \
        .direction("DESC") \
        .build()

    # 发起请求
    option = lark.RequestOption.builder().user_access_token(user_access_token).build()
    response = client.drive.v1.file.list(request, option)

    # 处理失败返回
    if not response.success():
        print(f"SDK请求失败: {response.code}, {response.msg}")
    else:
        # 处理业务结果
        sdk_files = response.data.files
        print(f"SDK获取到的文件数量: {len(sdk_files)}")
        print("前几个文件/文件夹:")
        for i, file in enumerate(sdk_files[:5]):
            print(f"- {file.name} ({file.type})")
        
        # 保存结果到文件
        sdk_result = {
            "files": [
                {
                    "name": file.name,
                    "type": file.type,
                    "token": file.token,
                    "parent_token": file.parent_token if hasattr(file, 'parent_token') else None,
                    "created_time": str(file.created_time) if hasattr(file, 'created_time') else None,
                    "modified_time": str(file.modified_time) if hasattr(file, 'modified_time') else None
                }
                for file in sdk_files
            ]
        }
        with open("feishu_sdk_all_files.json", "w", encoding="utf-8") as f:
            json.dump(sdk_result, f, ensure_ascii=False, indent=2)
except Exception as e:
    print(f"SDK请求出错: {e}")

print("\n结果已保存到 feishu_api_all_files.json 和 feishu_sdk_all_files.json")
