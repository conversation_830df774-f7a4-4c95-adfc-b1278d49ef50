"""
测试飞书树状结构选择器

这个脚本会启动一个Flask服务器，提供飞书树状结构选择器的功能。
访问 http://localhost:5000/feishu/tree_selector 来测试。
"""
import os
import sys

# 添加项目根目录到 Python 路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, root_dir)

# 导入server_v2模块
from llm_for_a.market.qa_agent.server_v2 import app

if __name__ == "__main__":
    print("启动飞书树状结构选择器服务器...")
    print("请在浏览器中访问 http://localhost:5000/feishu/tree_selector")
    app.run(host='localhost', port=5000, debug=True)
