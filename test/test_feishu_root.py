import requests
import json
import toml
import os

# 从配置文件获取token
qa_agent_config = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "market", "qa_agent", "config.toml"))
config = toml.load(qa_agent_config)
user_access_token = config.get("knowledge_bases", {}).get("feishu", {}).get("user_access_token", "")

print("=== 测试不提供文件夹token时的行为 ===")
print(f"使用token: {user_access_token[:10]}...")

# 直接请求飞书 API 获取根文件夹内容（与 test_feishu_tree 逻辑一致）
url = "https://open.feishu.cn/open-apis/drive/v1/files"
headers = {"Authorization": f"Bearer {user_access_token}"}
params = {
    "order_by": "EditedTime",
    "direction": "DESC"
}

print("正在请求飞书 API... (无 folder_token)")
try:
    response = requests.get(url, headers=headers, params=params)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        files = data.get("data", {}).get("files", [])
        with open("feishu_root_files.json", "w", encoding="utf-8") as f:
            json.dump(files, f, ensure_ascii=False, indent=2)
        print(f"获取到的根节点数量: {len(files)}")
        print("前几个文件/文件夹:")
        for i, item in enumerate(files[:5]):
            print(f"- {item.get('name')} ({item.get('type')})")
    else:
        print(f"请求失败: {response.text}")
except Exception as e:
    print(f"请求出错: {e}")

print("\n完整结果已保存到 feishu_root_files.json")
