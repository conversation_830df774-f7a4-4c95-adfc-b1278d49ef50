import requests
import json
import toml
import os

# 从配置文件获取token
qa_agent_config = os.path.join(os.path.dirname(os.path.abspath(__file__)), "llm_for_a", "market", "qa_agent", "config.toml")
config = toml.load(qa_agent_config)
user_access_token = config.get("knowledge_bases", {}).get("feishu", {}).get("user_access_token", "")

# 从树状结构中获取的 docx 文件ID
# 可以先运行test_feishu_root.py获取文件列表，然后选择一个文件ID
file_token = "L1jGdlwcEoNdXjxYt8pcofPBnye"  # 这里使用测试文件中已有的ID，实际使用时应更换为实际文件ID

# 请求单个文件内容的API
url = "http://localhost:5000/api/feishu/file_content"

print("=== 测试不提供文件夹token获取文件内容 ===")
print(f"使用user_access_token: {user_access_token[:10]}...")
print(f"获取文件ID: {file_token}")

try:
    # 构建请求体，不包含folder_token
    payload = {
        "file_token": file_token,
        "user_access_token": user_access_token
    }
    
    response = requests.post(url, json=payload)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        content = data.get("content", "")
        
        # 保存内容到文件
        with open("feishu_file_content_no_folder.md", "w", encoding="utf-8") as f:
            f.write(content)
        
        # 显示部分内容
        print(f"文件内容长度: {len(content)} 字符")
        print("\n--- 内容前200个字符 ---")
        print(content[:200] + "..." if len(content) > 200 else content)
        print("\n内容已保存到 feishu_file_content_no_folder.md")
    else:
        print(f"请求失败: {response.text}")
except Exception as e:
    print(f"发生错误: {str(e)}")
