import requests
import json

# 直接从配置文件中获取的 token
folder_token = "FMMufxN0ylK9PJdHcvWcOSornWd"
user_access_token = "u-f8JZ40v7de_FraCC0spN3511kyJAh18rMgG04hg20d66"

# 从树状结构中获取的 docx 文件ID
file_token = "L1jGdlwcEoNdXjxYt8pcofPBnye"

# 请求单个文件内容的API
url = "http://localhost:5000/api/feishu/file_content"

print("正在请求飞书文件内容 API...")
try:
    # 构建请求体
    payload = {
        "file_token": file_token,
        "folder_token": folder_token,
        "user_access_token": user_access_token
    }
    
    response = requests.post(url, json=payload)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        content = data.get("content", "")
        
        # 保存内容到文件
        with open("feishu_file_content.md", "w", encoding="utf-8") as f:
            f.write(content)
        
        # 显示部分内容
        print(f"文件内容长度: {len(content)} 字符")
        print("\n--- 内容前200个字符 ---")
        print(content[:200] + "..." if len(content) > 200 else content)
        print("\n内容已保存到 feishu_file_content.md")
    else:
        print(f"请求失败: {response.text}")
except Exception as e:
    print(f"发生错误: {e}")
