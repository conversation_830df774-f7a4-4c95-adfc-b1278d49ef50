# test_feishu_kb_content_check.py
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
import toml
from llm_for_a.market.qa_agent.models.feishu_knowledge_base import FeishuKnowledgeBase

# 配置文件路径
# 修正为始终从项目根目录定位 config.toml
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'llm_for_a', 'market', 'qa_agent'))
config_path = os.path.join(project_root, 'config.toml')
config = toml.load(config_path)
feishu_cfg = config.get("knowledge_bases", {}).get("feishu", {})

user_access_token = feishu_cfg.get("user_access_token")
folder_token = feishu_cfg.get("folder_token")  # 可为 None

print("初始化 FeishuKnowledgeBase (只查树, 不查内容) ...")
try:
    # 只查树，不查内容
    kb_tree = FeishuKnowledgeBase(
        id="test_tree",
        folder_token=folder_token,
        user_access_token=user_access_token,
        file_tokens=None,  # 不指定 file_tokens，模拟“未选中文件”
        lazy_init=True
    )
    print(f"树状文档token列表: {kb_tree._doc_ids}")
    # 模拟用户选中前2个文档
    selected_tokens = kb_tree._doc_ids[:2] if kb_tree._doc_ids else []
    print(f"模拟用户选中的文档token: {selected_tokens}")
    # 查内容
    kb = FeishuKnowledgeBase(
        id="test",
        folder_token=folder_token,
        user_access_token=user_access_token,
        file_tokens=selected_tokens,
        lazy_init=False
    )
    print("初始化成功，开始调用 to_dict() ...")
    info = kb.to_dict()
    print("to_dict() 结果：", info)
except Exception as e:
    print("发生异常：", e)
    import traceback
    traceback.print_exc()