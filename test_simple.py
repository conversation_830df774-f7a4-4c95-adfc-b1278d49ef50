import requests
import os
import json
import toml
import traceback

def main():
    try:
        print("Starting test...")
        
        # 测试 LLM 连接
        url = "http://llm.yanfuinvest.com/v1/models"
        headers = {
            "Authorization": f"Bearer sk-ISyVIYc3933iApsiLaz-HQ"
        }
        
        print(f"Sending request to {url}")
        response = requests.get(url, headers=headers)
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")
        
    except Exception as e:
        print(f"Error occurred: {e}")
        print("Traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    main()
