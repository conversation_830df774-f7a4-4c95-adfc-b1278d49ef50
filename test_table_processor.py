import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info("Attempting to import process_document_with_queries...")
try:
    from table_processor.main import process_document_with_queries
    logger.info("Import successful")
except Exception as e:
    logger.error(f"Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

try:
    # Try to use an example document from document_processor directory
    logger.info("Looking for test documents...")
    docx_file_paths = []
    
    # Look in document_processor directory first
    document_processor_dir = os.path.join('..', 'document_processor')
    if os.path.exists(document_processor_dir):
        logger.info(f"Checking {document_processor_dir} directory...")
        files = os.listdir(document_processor_dir)
        docx_files = [f for f in files if f.endswith('.docx') and not f.startswith('~$')]
        docx_file_paths.extend([os.path.join(document_processor_dir, f) for f in docx_files])
    
    # Then check test_docs directory
    test_docs_dir = 'test_docs'
    if os.path.exists(test_docs_dir) and os.path.isdir(test_docs_dir):
        logger.info(f"Checking {test_docs_dir} directory...")
        files = os.listdir(test_docs_dir)
        docx_files = [f for f in files if f.endswith('.docx') and not f.startswith('~$')]
        docx_file_paths.extend([os.path.join(test_docs_dir, f) for f in docx_files])
    
    if docx_file_paths:
        test_file = docx_file_paths[0]
        logger.info(f"Found test file: {test_file}")
    else:
        logger.info("No test files found in standard directories, looking elsewhere...")
        # Look for any docx file we can use for testing
        for root, dirs, files in os.walk('.'):
            docx_files = [os.path.join(root, f) for f in files if f.endswith('.docx') and not f.startswith('~$')]
            if docx_files:
                test_file = docx_files[0]
                logger.info(f"Found test file: {test_file}")
                break
        else:
            logger.error("No .docx files found in workspace")
            sys.exit(1)
      logger.info(f"Testing process_document_with_queries with file: {test_file}")
    
    # Check if file exists
    if not os.path.exists(test_file):
        logger.error(f"Test file does not exist: {test_file}")
        sys.exit(1)
        
    # Create output path
    output_file = test_file.replace('.docx', '_processed.docx')
    logger.info(f"Output will be written to: {output_file}")
    
    # Actually perform the test
    logger.info("Calling process_document_with_queries...")
    result = process_document_with_queries(test_file, output_file, 'balanced')
    
    logger.info(f"Result: {result}")
    
    # Check if output file was created
    if result.get('success'):
        output_path = result.get('file_path')
        if output_path and os.path.exists(output_path):
            logger.info(f"Output file created successfully at: {output_path}")
        else:
            logger.warning(f"Output file was not created at expected path: {output_path}")
    
except Exception as e:
    import traceback
    logger.error(f"Error: {str(e)}")
    traceback.print_exc()
