#!/usr/bin/env python3
"""
Test script to verify knowledge base and LLM connection
"""
import os
import sys
import toml
import logging

# Add the project directory to Python path
sys.path.append(os.path.dirname(__file__))

from market.qa_agent.services.knowledge_base_service import KnowledgeBaseService

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_knowledge_base_loading():
    """Test loading knowledge bases from configuration"""
    try:
        # Load configuration
        config_path = os.path.join(os.path.dirname(__file__), "market", "qa_agent", "config.toml")
        config_dir = os.path.dirname(config_path)
        
        logger.info(f"Loading config from: {config_path}")
        logger.info(f"Config directory: {config_dir}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        # Initialize knowledge base service
        kb_service = KnowledgeBaseService(config, config_dir)
        
        # Test each knowledge base
        for kb_id in kb_service.knowledge_bases:
            logger.info(f"\n=== Testing Knowledge Base: {kb_id} ===")
            try:
                content = kb_service.read_knowledge_base(kb_id, with_file_name=True)
                if isinstance(content, str) and content.startswith("错误:"):
                    logger.error(f"Knowledge base {kb_id} failed: {content}")
                else:
                    logger.info(f"Knowledge base {kb_id} loaded successfully")
                    logger.info(f"Content length: {len(content) if content else 0} characters")
                    if content:
                        logger.info(f"Content preview: {content[:200]}...")
            except Exception as e:
                logger.error(f"Error testing knowledge base {kb_id}: {str(e)}")
        
        # Test multiple knowledge base reading
        logger.info("\n=== Testing Multiple Knowledge Base Reading ===")
        local_kb_ids = [kb_id for kb_id in kb_service.knowledge_bases 
                       if not isinstance(kb_service.knowledge_bases[kb_id], str)]
        
        if local_kb_ids:
            combined_content = kb_service.read_knowledge_base_multiple(local_kb_ids, with_file_name=True)
            logger.info(f"Combined content length: {len(combined_content)} characters")
            logger.info(f"Combined content preview: {combined_content[:300]}...")
        else:
            logger.warning("No successfully loaded knowledge bases found")
            
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return False

def test_llm_config():
    """Test LLM configuration"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), "market", "qa_agent", "config.toml")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        llms = config.get("llms", {})
        logger.info(f"\n=== LLM Configuration ===")
        logger.info(f"Available LLMs: {list(llms.keys())}")
        
        for llm_id, llm_config in llms.items():
            logger.info(f"LLM {llm_id}:")
            logger.info(f"  Model: {llm_config.get('model')}")
            logger.info(f"  Base URL: {llm_config.get('base_url')}")
            logger.info(f"  Max Tokens: {llm_config.get('max_tokens')}")
            
        return True
        
    except Exception as e:
        logger.error(f"LLM config test failed: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting Knowledge Base and LLM Connection Test")
    
    success = True
    
    # Test knowledge base loading
    logger.info("=" * 60)
    logger.info("TESTING KNOWLEDGE BASE LOADING")
    logger.info("=" * 60)
    if not test_knowledge_base_loading():
        success = False
    
    # Test LLM configuration
    logger.info("\n" + "=" * 60)
    logger.info("TESTING LLM CONFIGURATION")
    logger.info("=" * 60)
    if not test_llm_config():
        success = False
    
    # Final result
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("✅ ALL TESTS PASSED - System is ready!")
    else:
        logger.error("❌ SOME TESTS FAILED - Check the logs above")
    logger.info("=" * 60)
