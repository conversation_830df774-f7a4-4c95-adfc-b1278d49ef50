### 测试知识库
GET http://localhost:5002/api/knowledge_list

### 测试查询
POST http://localhost:5002/api/query_preview
Content-Type: application/json

{
    "prompt": "test_prefix{knowledges_str}test_suffix",
    "kl_ids": ["base_yf_knowledge_base"]
}

### 测试查询
POST http://localhost:5002/api/query
Content-Type: application/json

{
    "prompt": "/no_think ## 角色定义\n你是一名专业的Word文档处理助手，擅长从HTML格式的文本中提取信息并填充模板变量。\n\n## 任务说明\n用户将提供以下内容：\n- 知识库：`knowledges_str`（上下文相关背景信息）\n- HTML格式的上下文：`context`（包含需要填充的`{variable_name}`占位符）\n\n## 处理规则\n1. 仔细分析HTML内容，识别所有的`{variable_name}`格式的占位符\n2. 根据上下文语义和知识库内容，推断每个占位符应填入的值\n\n## 输出要求\n严格使用以下JSON格式响应：\n\n用户问题：\n1+1={a1}\n2+2={a2}",
    "kl_ids": ["base_yf_knowledge_base_2"],
    "llm_config": {
        "model": "Qwen3-235B-A22B",
        "base_url": "https://llm.yanfuinvest.com",
        "api_key": "sk-ISyVIYc3933iApsiLaz-HQ"
    }
}

### 测试查询
POST http://localhost:5002/api/query
Content-Type: application/json

{
  "prompt": "/no_think\n## 角色定义\n你是一名专业的Word文档处理助手，擅长从HTML格式的文本中提取信息并填充模板变量。\n\n## 任务说明\n用户将提供以下内容：\n- 知识库：`knowledges_str`（上下文相关背景信息）\n- HTML格式的上下文：`context`（包含需要填充的`{variable_name}`占位符）\n\n## 处理规则\n1. 仔细分析HTML内容，识别所有的`{variable_name}`格式的占位符\n2. 根据上下文语义和知识库内容，推断每个占位符应填入的值\n3. 所有占位符都需处理，没有明确对应值的留空字符串`\"\"`\n\n## 输出要求\n严格使用以下JSON格式响应：\n```json\n{\n\"变量名1\": \"填充内容1\",\n\"变量名2\": \"填充内容2\",\n...\n}\n```\n\n## 示例\n输入：\n```html\n<p>总部位于{headquarters}，成立于{founded_year}年</p>\n```\n知识库：公司于2005年在旧金山创立\n\n输出：\n```json\n{\n\"headquarters\": \"旧金山\",\n\"founded_year\": \"2005\"\n}\n```\n\n## 当前任务\n请处理以下内容：\n知识库：`{knowledges_str}`\n上下文：`\n\n<div class=\"WordSection1\">\n\n<p class=\"MsoNormal\"><b>&nbsp;</b></p>\n\n<p class=\"MsoNormal\" align=\"center\"><b>XXX</b><b>资产投资有限公司</b><b>尽调底稿模板</b></p>\n\n<p class=\"MsoNormal\" align=\"center\">(请根据实际客观填写，各细项无对应情况可不填相应细项)</p>\n\n<p class=\"MsoNormal\"><b>&nbsp;</b></p>\n\n<p class=\"MsoNormal\"><b>&nbsp;</b></p>\n\n<table class=\"MsoNormalTable\">\n <tbody><tr>\n  <td colspan=\"13\" valign=\"top\">\n  <p class=\"MsoNormal\" align=\"center\"><b>一、管理人基本情况</b></p>\n  </td>\n </tr>\n <tr>\n  <td colspan=\"13\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>工商注册基本信息：</b></p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">公司法定中文名称</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">上海衍复投资管理有限公司</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">中文简称</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">衍复投资</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">英文简称</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">YANFU\n  INVESTMENTS</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">注册地址或主要办公地址</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">上海市徐汇区瑞平路275号保利西岸C座7楼</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">注册资本/实缴资本</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">1000万元</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">法人代表</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">顾王琴</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">统一社会信用代码</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">91310113MA1GNMLP7F</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">成立日期</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">2019年7月25日</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">公司类型</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">有限责任公司</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">经营范围</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">投资管理</p>\n  </td>\n </tr>\n <tr>\n  <td colspan=\"13\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>公司基本联系方式：</b></p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">官方网址</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">http://www.yanfuinvestments.com/</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">联系电话及传真号码</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">021-54665022</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">紧急联系人及应急电话</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">秦梦妍，021-54665022</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">公司社交帐号 （如有）</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">{公司社交帐号 （如有）}</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"12\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n </tr>\n <tr>\n  <td colspan=\"13\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>牌照资质信息：</b></p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\"><b>类别&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n  项目</b></p>\n  </td>\n  <td colspan=\"2\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>时间</b></p>\n  </td>\n  <td colspan=\"7\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>编号</b></p>\n  </td>\n  <td colspan=\"3\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>类型</b></p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">中基协备案</p>\n  </td>\n  <td colspan=\"2\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"7\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"3\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">金融机构牌照</p>\n  </td>\n  <td colspan=\"2\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"7\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"3\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">投资顾问资质</p>\n  </td>\n  <td colspan=\"2\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>是否具有</b></p>\n  </td>\n  <td colspan=\"7\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>取得方式（自动或中基协规定</b>）</p>\n  </td>\n  <td colspan=\"3\" valign=\"top\">\n  <p class=\"MsoNormal\"><b>时间</b></p>\n  </td>\n </tr>\n <tr>\n  <td valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"2\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"7\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n  <td colspan=\"3\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  </td>\n </tr>\n <tr>\n  <td rowspan=\"4\" valign=\"top\">\n  <p class=\"MsoNormal\">&nbsp;</p>\n  <p class=\"MsoNormal\">&nbsp;</p>\n  <p class=\"MsoNormal\">其它资质牌照`\n",
  "kl_ids": ["base_yf_knowledge_base"],
  "llm_config": {
    "api_key": "sk-ISyVIYc3933iApsiLaz-HQ",
    "base_url": "https://llm.yanfuinvest.com",
    "max_tokens": 40960,
    "model": "Qwen3-30B-A3B"
  },
  "streamable": false
}

### 测试填充占位符
GET http://localhost:5002/api/word_analyze?file_path=/Users/<USER>/Documents/code/llm_for_a/market/resources/国民信托私募产品管理人尽调底稿.docx


### 测试模型列表
GET http://localhost:5002/api/llm_model_list
