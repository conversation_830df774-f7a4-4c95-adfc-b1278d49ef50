import json

from market.content_matcher import query_multi


if __name__ == "__main__":
    with open("./tests/content_match/tasks.json") as file:
        queries = json.load(file)

    result = query_multi(
        "./tests/content_match/衍复尽调内部资料库 2025年一季度末.docx",
        queries,
        debug=True,
        model_name="Qwen2_5-VL-32B-Instruct",
    )
    with open("./tests/content_match/result.json", "w+") as file:
        file.write(json.dumps(result, ensure_ascii=False, indent=4))
