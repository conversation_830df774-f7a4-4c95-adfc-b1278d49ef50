import json

if __name__ == "__main__":
    with open('./tests/content_match/result.json') as file:
        result = json.load(file)

    data = {}
    for item in result:
        match = [x for x in item["matches"] if x["match"] == True]
        text = match[0]["extracted_text"] if match else None

        data[item["query"]] = text
        

    with open('./tests/content_match/result_parsed_compact.json', 'w+') as file:
        file.write(json.dumps(data, ensure_ascii=False, indent=4))