# from docx import Document
# import json

# doc = Document(r"C:\english_name\market\src\原稿.docx")

# qa_pairs = []

# for table in doc.tables:
#     # 假设表格结构：第一列为问题，第二列为答案
#     for row in table.rows[1:]:  # 跳过表头行
#         cells = row.cells
#         if len(cells) >= 2:
#             qa_pairs.append({
#                 "query": cells[0].text.strip(),
#                 "answer": cells[1].text.strip()
#             })

# with open(r"C:\english_name\market\src\output.json", "w", encoding="utf-8") as f:
#     json.dump(qa_pairs, f, ensure_ascii=False, indent=2)

from docx import Document
from docx.document import Document as _Document
from docx.text.paragraph import Paragraph  # 添加缺失的导入
import json
import re

def process_table(table, category=None):
    """专用表格处理器（处理合并单元格重复）"""
    results = []
    seen_entries = set()  # 使用元组存储已处理的条目
    
    for row in table.rows:
        cells = [re.sub(r'\s+', ' ', cell.text.strip()) for cell in row.cells]
        if not cells or "基本情况" in cells[0]:
            continue
            
        query = cells[0]
        # 去重处理：合并单元格内容并分割后去重
        answer_parts = []
        seen_values = set()
        for value in cells[1:]:
            if not value:
                continue
            # 分割可能存在的多个值（处理合并单元格）
            for part in re.split(r'\s{2,}', value):  # 处理多个空格分割
                if part and part not in seen_values:
                    seen_values.add(part)
                    answer_parts.append(part)
        
        if not answer_parts:
            continue
            
        # 最终去重检查
        entry_key = (query, tuple(answer_parts))
        if entry_key not in seen_entries:
            seen_entries.add(entry_key)
            results.append({
                "query": query,
                "answer": "\n".join(answer_parts)
            })

    return results



doc = Document(r"C:\english_name\market\src\原稿.docx")
output = []
current_category = None

for table in doc.tables:
    # 更安全的方式获取前导段落
    prev_element = table._element.getprevious()
    
    # 仅处理段落元素且包含文本
    if prev_element is not None and prev_element.tag.endswith('p'):
        prev_para = doc.element.body[doc.element.body.index(table._element)-1]
        current_category = ''.join(
            t.text for t in Paragraph(prev_para, doc).runs if t.text
        ).strip()

    output += process_table(table, current_category)
    current_category = None


with open(r"test_sample.json", "w", encoding="utf-8") as f:
    json.dump(output, f, ensure_ascii=False, indent=2)