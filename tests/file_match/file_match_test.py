import json

from market.file_matcher import query_multi


if __name__ == "__main__":
    with open("./tests/file_match/file_list.json") as file:
        file_list = json.load(file)
        file_list = [x for x in file_list if "常用材料" in x]

    with open("./tests/file_match/tasks.json") as file:
        data = json.load(file)
        queries = [x["query"] for x in data if not not x.get("query")]

    result = query_multi(file_list, queries[:3], debug=True, model_name="Qwen2_5-VL-32B-Instruct", chunk_size=10)
    # result = query_multi(file_list, queries, debug=False, model_name="Qwen2_5-VL-32B-Instruct", chunk_size=10)
    with open("./tests/file_match/result.json", "w+") as file:
        file.write(json.dumps(result, ensure_ascii=False, indent=4))
