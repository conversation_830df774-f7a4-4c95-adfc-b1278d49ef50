import json
from docx import Document
from docx.oxml.table import CT_Tbl
from utils.doc_extract_util import extract_text, convert_to_json, get_element, convert_to_json_with_xpath, get_element_by_xpath, write_by_xpath


def generate_task(doc):
    tasks = []
    for index, element in enumerate(doc.element.body.inner_content_elements):
        if isinstance(element, CT_Tbl):
            table_position = {"index": index}
            for row_index, row in enumerate(element.tr_lst):
                row_position = table_position.copy()
                row_position["child_position"] = {"index": row_index}

                cells = row.tc_lst
                cell_texts = [extract_text(cell) for cell in cells]
                row_len = len(cell_texts)
                if row_len <= 2:
                    continue
                query = cell_texts[row_len - 2]
                answer_postion = row_position.copy()
                answer_postion["child_position"]["child_position"] = {"index": row_len - 1}
                tasks.append({'query': query, "answer_position": {"child_position": answer_postion}})
    return tasks


def test_convert_to_json():
    # doc_file = "./source/测试_中信合同.docx"
    doc_file = "./operation/resources/衍复博裕中性三号私募证券投资基金_招募说明书.docx"
    document = Document(doc_file)
    json_data = convert_to_json(0, document)
    with open("test.json", "w") as f:
        json.dump(json_data, f, ensure_ascii=False, indent=4)
    print(json_data)


def test_get_element():
    doc_file = "./operation/resources/衍复博裕中性三号私募证券投资基金_招募说明书.docx"
    document = Document(doc_file)
    position = {"child_position": {"index": 0}}
    target_element = get_element(document, position)
    print(target_element)


def test_extract_text():
    doc_file = "./operation/resources/衍复博裕中性三号私募证券投资基金_招募说明书.docx"
    document = Document(doc_file)
    position = {"child_position": {"index": 0}}
    target_element = get_element(document, position)
    print(extract_text(target_element))


def test_generate_task():
    doc_file = "./market/resources/衍复博裕中性三号私募证券投资基金_招募说明书.docx"
    document = Document(doc_file)
    tasks = generate_task(document)
    for task in tasks:
        element = get_element(document, task["answer_position"])
        task["answer"] = extract_text(element)
        print(f"Query: {task.get("query")}")
        print(f'Answer:{extract_text(element)}')
        print("-" * 100)
    with open("tasks.json", "w") as f:
        json.dump(tasks, f, ensure_ascii=False, indent=4)
    return tasks


def test_convert_to_json_with_xpath():
    doc_file = "./market/resources/国民信托私募产品管理人尽调底稿（模板）-尽调对象填写_20250415.docx"
    document = Document(doc_file)
    doc_json = convert_to_json_with_xpath(document)
    print(doc_json)
    element = get_element_by_xpath(document, '.[16].[115].[0].[8]')
    print(element.text)
    return doc_json


def test_write_by_xpath():
    doc_file = "./market/resources/国民信托私募产品管理人尽调底稿（模板）-尽调对象填写_20250415.docx"
    document = Document(doc_file)
    write_by_xpath(document, '.[16].[115].[0].[8]', "TDBTDB")
    document.save("test.docx")


if __name__ == "__main__":
    test_write_by_xpath()
    # test_generate_market_task()
