import fitz  # PyMuPDF PDF读取库
import camelot  # 表格提取库
import shutil
from difflib import Differ, SequenceMatcher  # 文本差异比较库
import cv2  # OpenCV图像处理库
from skimage.metrics import structural_similarity as ssim  # 相似度计算
from docx2pdf import convert
import os


class DocumentComparator:
    def __init__(self, doc1_path, doc2_path):
        self.doc1_path = doc1_path
        self.doc2_path = doc2_path
        self.temp_dir = "temp_comparison"
        os.makedirs(self.temp_dir, exist_ok=True)

    def convert_to_pdf(self):
        """转换Word文档为PDF（添加异常处理）"""
        try:
            convert(self.doc1_path, os.path.join(self.temp_dir, "doc1.pdf"))
            convert(self.doc2_path, os.path.join(self.temp_dir, "doc2.pdf"))
        except AttributeError as e:
            if "Quit" in str(e):
                print("检测到Word COM对象退出异常，已忽略")
            else:
                raise
        finally:
            # 强制终止可能残留的WINWORD进程
            os.system('taskkill /f /im WINWORD.EXE > nul 2>&1')

    def compare_all(self):
        """执行完整比较流程"""
        self.convert_to_pdf()

        comparison_data = {
            'text': {'similarity': 0, 'diff_original': '', 'diff_modified': ''},
            'tables': {'similarity': 0, 'diff_html': ''},
            'images': {'similarity': 0, 'diff_html': ''}
        }

        # 文本比较
        text1 = self._extract_text(self.doc1_path)
        text2 = self._extract_text(self.doc2_path)
        text_diff = self._generate_text_diff(text1, text2)
        comparison_data['text'].update({
            'similarity': SequenceMatcher(None, text1, text2).ratio(),
            'diff_original': text_diff['diff_original'],
            'diff_modified': text_diff['diff_modified']
        })

        # 表格比较
        tables1 = self._extract_tables(self.doc1_path)
        tables2 = self._extract_tables(self.doc2_path)
        comparison_data['tables'].update({
            'similarity': self._compare_tables(tables1, tables2),
            'diff_html': self._generate_table_diff(tables1, tables2)
        })

        # 图片比较
        img_similarity, img_diff = self._compare_images()
        comparison_data['images'].update({
            'similarity': img_similarity,
            'diff_html': img_diff
        })

        self._generate_report(comparison_data)
        shutil.rmtree(self.temp_dir)
        return comparison_data

    def _extract_text(self, pdf_name):
        """提取PDF文本内容"""
        doc = fitz.open(os.path.join(self.temp_dir, pdf_name))
        return "\n".join([page.get_text() for page in doc])

    def _extract_tables(self, pdf_name):
        """提取PDF中的表格"""
        return camelot.read_pdf(os.path.join(self.temp_dir, pdf_name), flavor='stream')

    def _compare_tables(self, tables1, tables2):
        """比较表格相似度"""
        if len(tables1) != len(tables2):
            return 0.0
        return sum(SequenceMatcher(None, t1.df.to_csv(), t2.df.to_csv()).ratio() 
                   for t1, t2 in zip(tables1, tables2)) / len(tables1)

    def _extract_images(self, pdf_name, output_dir):
        """提取PDF中的图片"""
        os.makedirs(output_dir, exist_ok=True)
        pdf_path = os.path.join(self.temp_dir, pdf_name)
        doc = fitz.open(pdf_path)

        for page_index in range(len(doc)):
            page = doc.load_page(page_index)
            image_list = page.get_images(full=True)

            for img_index, img in enumerate(image_list):
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                img_path = os.path.join(output_dir, f"page{page_index}_img{img_index}.png")
                pix.save(img_path)
                pix = None  # 释放内存

    def _compare_images(self):
        """改进图片对比逻辑"""
        report_img_dir = "comparison_images"
        os.makedirs(report_img_dir, exist_ok=True)

        # 提取图片到临时目录
        temp_img_dir1 = os.path.join(self.temp_dir, "images1")
        temp_img_dir2 = os.path.join(self.temp_dir, "images2")
        self._extract_images("doc1.pdf", temp_img_dir1)
        self._extract_images("doc2.pdf", temp_img_dir2)

        diff_html = []
        total_similarity = 0
        compared_pairs = 0

        # 遍历所有图片文件
        for img_file in os.listdir(temp_img_dir1):
            if img_file in os.listdir(temp_img_dir2):
                img1 = cv2.imread(os.path.join(temp_img_dir1, img_file))
                img2 = cv2.imread(os.path.join(temp_img_dir2, img_file))

                # 统一图片尺寸
                img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

                # 计算相似度
                similarity = ssim(img1, img2, channel_axis=2)
                total_similarity += similarity
                compared_pairs += 1

                # 生成差异图
                diff_img = self._highlight_image_diff(img1, img2)
                diff_path = os.path.join(report_img_dir, f"diff_{img_file}")
                cv2.imwrite(diff_path, diff_img)

                # 保存原图副本到报告目录
                orig_path = os.path.join(report_img_dir, f"orig_{img_file}")
                cv2.imwrite(orig_path, img1)

                diff_html.append(f'''
                <div class="image-pair">
                    <div class="image-box">
                        <h4>原稿图片</h4>
                        <img src="{orig_path}">
                    </div>
                    <div class="image-box">
                        <h4>差异对比</h4>
                        <img src="{diff_path}">
                    </div>
                    <div class="similarity">相似度: {similarity:.2%}</div>
                </div>
                ''')

        avg_similarity = total_similarity / compared_pairs if compared_pairs > 0 else 1.0
        return avg_similarity, "\n".join(diff_html)

    def _highlight_image_diff(self, img1, img2):
        """生成图片差异高亮"""
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 计算差异区域
        _, diff = cv2.threshold(cv2.absdiff(gray1, gray2), 30, 255, cv2.THRESH_BINARY)
        diff_color = cv2.applyColorMap(diff, cv2.COLORMAP_HOT)

        # 融合原图和差异高亮
        return cv2.addWeighted(img2, 0.7, diff_color, 0.3, 0)

    def _generate_text_diff(self, text1, text2):
        """生成两栏文本对比"""
        differ = Differ()
        diff = list(differ.compare(text1.splitlines(), text2.splitlines()))
    
        original = []
        modified = []
        for line in diff:  # 添加循环变量
            if line.startswith('- '):
                original.append(f'<del>{line[2:]}</del>')
                modified.append("&nbsp;")
            elif line.startswith('+ '):
                original.append("&nbsp;")
                modified.append(f'<ins>{line[2:]}</ins>')
            else:
                content = line[2:]
                original.append(content)
                modified.append(content)
    
        return {
            'diff_original': "<br>".join(original),
            'diff_modified': "<br>".join(modified)
        }

    def _generate_table_diff(self, tables1, tables2):
        """生成表格差异HTML"""
        html = []
        for i in range(max(len(tables1), len(tables2))):
            html.append('<div class="table-compare">')
            
            if i < len(tables1) and i < len(tables2):
                df1 = tables1[i].df
                df2 = tables2[i].df
                diff_table = self._compare_table_cells(df1, df2)
                html.append(diff_table)
            elif i < len(tables1):
                html.append('<div style="color:red">文档1特有表格:</div>')
                html.append(tables1[i].df.to_html())
            else:
                html.append('<div style="color:green">文档2特有表格:</div>')
                html.append(tables2[i].df.to_html())
            
            html.append('</div>')
        return "\n".join(html)

    def _compare_table_cells(self, df1, df2):
        """比较表格单元格并生成差异"""
        html = ['<table border="1">']
        for row in range(max(df1.shape[0], df2.shape[0])):
            html.append('<tr>')
            for col in range(max(df1.shape[1], df2.shape[1])):
                cell1 = df1.iat[row, col] if row < df1.shape[0] and col < df1.shape[1] else ""
                cell2 = df2.iat[row, col] if row < df2.shape[0] and col < df2.shape[1] else ""

                style = ""
                if cell1 != cell2:
                    style = 'style="background:linear-gradient(to right, #ffcccc 50%, #ccffcc 50%)"'
                elif row >= df1.shape[0] or col >= df1.shape[1]:
                    style = 'style="background:#ccffcc"'
                elif row >= df2.shape[0] or col >= df2.shape[1]:
                    style = 'style="background:#ffcccc"'

                html.append(f'<td {style} title="文档1: {cell1}\n文档2: {cell2}">{cell2}</td>')
            html.append('</tr>')
        html.append('</table>')
        return "\n".join(html)

    def _generate_report(self, data):
        """生成完整对比报告"""
        html = f'''
        <html>
        <head>
            <style>
                .comparison-container {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 30px;
                    margin: 20px 0;
                }}
                .text-column {{
                    border: 1px solid #ddd;
                    padding: 20px;
                    background: #f9f9f9;
                }}
                .image-pair {{
                    margin: 20px 0;
                    padding: 15px;
                    border: 1px solid #eee;
                }}
                .image-box {{ 
                    text-align: center;
                    margin: 10px;
                }}
                img {{ 
                    max-width: 90%;
                    height: auto;
                    border: 1px solid #ccc;
                }}
                del {{ 
                    background: #ffebee;
                    text-decoration: line-through;
                    color: #b71c1c;
                }}
                ins {{ 
                    background: #e8f5e9;
                    text-decoration: none;
                    color: #2e7d32;
                }}
                .similarity {{
                    text-align: center;
                    margin: 10px;
                    font-weight: bold;
                }}
            </style>
        </head>
        <body>
            <h1 style="text-align:center">文档对比报告</h1>
        
            <div class="comparison-container">
                <div class="text-column">
                    <h2>原稿内容</h2>
                    {data['text']['diff_original']}
                </div>
                <div class="text-column">
                    <h2>修改稿内容</h2>
                    {data['text']['diff_modified']}
                </div>
            </div>

            <div style="padding:20px">
                <h2>图片差异对比</h2>
                {data['images']['diff_html']}
            </div>

            <div style="padding:20px">
                <h2>表格差异对比</h2>
                {data['tables']['diff_html']}
            </div>
        </body>
        </html>
        '''
        
        with open("comparison_report.html", "w", encoding="utf-8") as f:
            f.write(html)


# 使用示例
if __name__ == "__main__":
    # 使用实际文件路径
    comparator = DocumentComparator(
        r"/Users/<USER>/py_workplace/llm/playground/llm_for_a/market/resources/国民信托私募产品管理人尽调底稿（模板）-尽调对象填写_20250415.docx",
        r"/Users/<USER>/py_workplace/llm/playground/llm_for_a/test.docx"
    )
    result = comparator.compare_all()
    print(f"比较完成，请查看 comparison_report.html \n总体相似度：{result['text']['similarity']:.2%}")
