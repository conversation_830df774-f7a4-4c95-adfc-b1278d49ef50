import shutil
from difflib import Differ, SequenceMatcher
import cv2
from skimage.metrics import structural_similarity as ssim
import os
import sys
import zipfile
from docx import Document
import re  # 新增这行


WORD_NS = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}


class DocumentComparator:
    def __init__(self, doc1_path, doc2_path):
        self.doc1_path = doc1_path
        self.doc2_path = doc2_path
        self.temp_dir = "temp_comparison"
        os.makedirs(self.temp_dir, exist_ok=True)

    def compare_all(self):
        """执行简化版比较流程"""
        # 创建临时目录
        img_dir1 = os.path.join(self.temp_dir, "images1")
        img_dir2 = os.path.join(self.temp_dir, "images2")
        os.makedirs(img_dir1, exist_ok=True)
        os.makedirs(img_dir2, exist_ok=True)

        comparison_data = {
            'text': {'similarity': 0, 'diff_original': '', 'diff_modified': ''},
            'images': {'similarity': 0, 'diff_html': ''}
        }

        # 提取文本和图片
        text1 = self._extract_all_text_from_docx(self.doc1_path)
        text2 = self._extract_all_text_from_docx(self.doc2_path)
        self._extract_images_from_docx(self.doc1_path, img_dir1)
        self._extract_images_from_docx(self.doc2_path, img_dir2)

        # 文本比较
        text_diff = self._generate_text_diff(text1, text2)
        text_similarity = SequenceMatcher(None, text1, text2).ratio()
        comparison_data['text'].update({
            'similarity': text_similarity,
            'diff_original': text_diff['diff_original'],
            'diff_modified': text_diff['diff_modified']
        })

        # 图片比较
        img_similarity, img_diff = self._compare_images()
        comparison_data['images'].update({
            'similarity': img_similarity,
            'diff_html': img_diff
        })

        self._generate_report(comparison_data)
        shutil.rmtree(self.temp_dir)
        return comparison_data

    def _extract_all_text_from_docx(self, docx_path):
        try:
            doc = Document(docx_path)
            all_text = []

            for element in doc.element.body:
                if element.tag.endswith('p'):
                    all_text.append(element.text)
                elif element.tag.endswith('tbl'):
                    table = self._process_table(element)
                    if table:
                        all_text.extend(table)

            return "\n".join(all_text)
        except Exception as e:
            print(f"提取文本时出错: {e}")
            return ""

    def _process_table(self, table_element):
        table_content = []
        merged_cells = set()

        # 注册命名空间
        ns = {'w': WORD_NS['w']}

        # 使用findall替代xpath
        rows = table_element.findall('.//w:tr', namespaces=ns)

        for i, row in enumerate(rows):
            row_text = []
            cells = row.findall('.//w:tc', namespaces=ns)

            for j, cell in enumerate(cells):
                if (i, j) in merged_cells:
                    continue

                # 处理列合并
                grid_span = self._get_grid_span(cell)
                # 处理行合并
                row_span = self._get_row_span(table_element, i, j, cell, ns)

                # 标记合并单元格
                for x in range(i, i + row_span):
                    for y in range(j, j + grid_span):
                        if x != i or y != j:
                            merged_cells.add((x, y))

                # 提取文本
                texts = cell.findall('.//w:t', namespaces=ns)
                cell_text = ''.join([t.text for t in texts if t.text]).strip() or "【空单元格】"
                row_text.append(cell_text)

            if row_text:
                table_content.append(" | ".join(row_text))

        return ["【表格】"] + table_content + ["【表格结束】"] if table_content else []

    def _get_grid_span(self, tc_element):
        grid_span = 1
        grid_span_elem = tc_element.find('.//w:gridSpan', namespaces=WORD_NS)
        if grid_span_elem is not None:
            grid_span = int(grid_span_elem.get('w:val', 1))
        return grid_span

    def _get_row_span(self, table, row_idx, col_idx, tc_element, ns):
        v_merge = tc_element.find('.//w:vMerge', namespaces=ns)
        if v_merge is None or v_merge.get('w:val') != 'restart':
            return 1

        span = 1
        rows = table.findall('.//w:tr', namespaces=ns)
        for next_row_idx in range(row_idx + 1, len(rows)):
            cells = rows[next_row_idx].xpath('.//w:tc', namespaces=WORD_NS)
            if col_idx >= len(cells):
                break
            next_vmerge = cells[col_idx].find('.//w:vMerge', namespaces=WORD_NS)
            if next_vmerge is not None and next_vmerge.get('w:val') == 'continue':
                span += 1
            else:
                break
        return span

    def _extract_images_from_docx(self, docx_path, output_dir):
        try:
            with zipfile.ZipFile(docx_path) as docx_zip:
                image_files = [f for f in docx_zip.namelist() if f.startswith('word/media/')]
                for idx, img_path in enumerate(image_files):
                    with docx_zip.open(img_path) as img_file:
                        output_path = os.path.join(output_dir, f"img{idx}{os.path.splitext(img_path)[1]}")
                        with open(output_path, 'wb') as f:
                            f.write(img_file.read())
        except Exception as e:
            print(f"提取图片时出错: {e}")

    def _compare_images(self):
        """简化版图片对比，修复展示问题"""
        report_dir = "report_output"
        report_img_dir = os.path.join(report_dir, "images")
        os.makedirs(report_img_dir, exist_ok=True)

        temp_img_dir1 = os.path.join(self.temp_dir, "images1")
        temp_img_dir2 = os.path.join(self.temp_dir, "images2")

        diff_html = []
        total_similarity = 0
        compared_pairs = 0

        # 获取排序后的图片列表
        img_files1 = sorted(os.listdir(temp_img_dir1))
        img_files2 = sorted(os.listdir(temp_img_dir2))
        max_compare = min(len(img_files1), len(img_files2))

        for i in range(max_compare):
            try:
                # 原始图片路径
                img1_path = os.path.join(temp_img_dir1, img_files1[i])
                img2_path = os.path.join(temp_img_dir2, img_files2[i])

                # 生成安全的文件名
                safe_name = re.sub(r'\W+', '_', os.path.splitext(img_files1[i])[0])[:30]

                # 报告用图片路径（包含目录）
                orig_filename = f"orig_{safe_name}.png"
                mod_filename = f"mod_{safe_name}.png"
                diff_filename = f"diff_{safe_name}.png"

                # 完整的保存路径
                orig_path = os.path.join(report_img_dir, orig_filename)
                mod_path = os.path.join(report_img_dir, mod_filename)
                diff_path = os.path.join(report_img_dir, diff_filename)

                # 处理图片
                img1 = cv2.imread(img1_path)
                img2 = cv2.imread(img2_path)
                if img1 is None or img2 is None:
                    continue

                img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))
                similarity = ssim(img1, img2, channel_axis=2)
                total_similarity += similarity
                compared_pairs += 1

                # 保存图片到指定路径
                cv2.imwrite(orig_path, img1)
                cv2.imwrite(mod_path, img2)
                cv2.imwrite(diff_path, self._highlight_image_diff(img1, img2))

                # 使用相对于报告目录的路径
                diff_html.append(f'''
                <div class="image-pair">
                    <div class="image-box">
                        <h4>原稿 ({img_files1[i]})</h4>
                        <img src="images/{orig_filename}">
                    </div>
                    <div class="image-box">
                        <h4>修改稿 ({img_files2[i]})</h4>
                        <img src="images/{mod_filename}">
                    </div>
                    <div class="image-box">
                        <h4>差异对比</h4>
                        <img src="images/{diff_filename}">
                        <div class="similarity">相似度: {similarity:.2%}</div>
                    </div>
                </div>
                ''')
            except Exception as e:
                print(f"处理图片 {img_files1[i]} 时出错: {str(e)}")

        # 处理数量不一致的情况
        if len(img_files1) != len(img_files2):
            diff_html.append(f'''
            <div class="warning">
                注意：原稿有 {len(img_files1)} 张图片，修改稿有 {len(img_files2)} 张图片
            </div>
            ''')

        avg_similarity = total_similarity / compared_pairs if compared_pairs > 0 else 1.0
        return avg_similarity, "\n".join(diff_html)

    def _highlight_image_diff(self, img1, img2):
        """改进差异可视化"""
        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 计算绝对差异
        diff = cv2.absdiff(gray1, gray2)

        # 增强可视化效果
        _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 在原图上绘制红色框
        result = img2.copy()
        for cnt in contours:
            if cv2.contourArea(cnt) > 100:  # 忽略小面积差异
                x, y, w, h = cv2.boundingRect(cnt)
                cv2.rectangle(result, (x, y), (x+w, y+h), (0, 0, 255), 2)

        return result

    # 在类中添加缺失的方法
    def _generate_text_diff(self, text1, text2):
        """生成文本差异对比"""
        d = Differ()
        diff = list(d.compare(text1.splitlines(), text2.splitlines()))

        original = []
        modified = []
        for line in diff:
            # 处理删除行
            if line.startswith('- '):
                original.append(f'<div class="diff-remove">❌ {self._highlight_changes(line[2:])}</div>')
            # 处理新增行
            elif line.startswith('+ '):
                modified.append(f'<div class="diff-add">✅ {self._highlight_changes(line[2:])}</div>')
            # 处理变化细节
            elif line.startswith('? '):
                original.append(f'<div class="diff-change">🔄 {self._highlight_changes(line[2:])}</div>')
                modified.append(f'<div class="diff-change">🔄 {self._highlight_changes(line[2:])}</div>')
            # 处理未变化行
            else:
                original.append(f'<div class="diff-common">{line[2:]}</div>')
                modified.append(f'<div class="diff-common">{line[2:]}</div>')

        return {
            'diff_original': '\n'.join(original),
            'diff_modified': '\n'.join(modified)
        }

    def _highlight_changes(self, text):
        """高亮具体变化字符"""
        # 使用正则表达式匹配差异标记（由difflib生成）
        highlighted = re.sub(r'(\^+)(.*?)(\^*)', 
                            r'<span class="highlight">\1\2\3</span>', 
                            text)
        # 高亮连续空格差异
        highlighted = highlighted.replace(' ', '·')
        return highlighted

    def _compare_text(self):
        """使用difflib生成带高亮标记的文本对比"""
        d = Differ()
        diff_result = list(d.compare(
            self.doc1_text.splitlines(),
            self.doc2_text.splitlines()
        ))

        original_html = []
        modified_html = []

        for line in diff_result:
            if line.startswith('- '):
                original_html.append(f'<div class="diff-remove">{line[2:]}</div>')
            elif line.startswith('+ '):
                modified_html.append(f'<div class="diff-add">{line[2:]}</div>')
            elif line.startswith('? '):
                # 高亮具体差异点
                highlight_line = line[2:].replace('^', '<span class="highlight">^</span>')
                original_html.append(f'<div class="diff-change">{highlight_line}</div>')
                modified_html.append(f'<div class="diff-change">{highlight_line}</div>')
            else:
                original_html.append(line[2:])
                modified_html.append(line[2:])

        return {
            'similarity': self._calculate_similarity(self.doc1_text, self.doc2_text),
            'diff_original': '<br>'.join(original_html),
            'diff_modified': '<br>'.join(modified_html)
        }

    def _calculate_similarity(self, text1, text2):
        """更精确的文本相似度计算"""
        seq_matcher = difflib.SequenceMatcher(None, text1, text2)
        return seq_matcher.ratio()

    def _get_similarity_color(self, similarity):
        """根据相似度返回颜色代码"""
        if similarity > 0.9:
            return "#4CAF50"  # 绿色
        elif similarity > 0.7:
            return "#FFC107"  # 橙色
        else:
            return "#F44336"  # 红色

    def _generate_report(self, data):
        """生成HTML报告"""
        report_dir = "report_output"
        images_dir = os.path.join(report_dir, "images")
        os.makedirs(report_dir, exist_ok=True)

        # 修改1：修复报告路径
        report_path = os.path.join(report_dir, "comparison_report.html")
        html = f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>文档对比报告 - 最终版</title>
            <style>
                .diff-add {{
                    background: #e6ffe6;
                    border-left: 3px solid #4CAF50;
                    padding: 8px;
                    margin: 4px 0;
                }}
                .diff-remove {{
                    background: #ffe6e6;
                    border-left: 3px solid #f44336;
                    padding: 8px;
                    margin: 4px 0;
                    text-decoration: line-through;
                }}
                .highlight {{
                    background-color: #fff9c4;
                    border-radius: 3px;
                    padding: 2px 4px;
                    font-weight: bold;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }}
                .diff-change {{
                    background: #fff3e0;
                    border-left: 3px solid #FFC107;
                    padding: 8px;
                    margin: 4px 0;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                    font-family: 'Microsoft Yahei', sans-serif;
                }}
                .notice {{
                    padding: 15px;
                    background: #f0f8ff;
                    border-left: 4px solid #2196F3;
                    margin-bottom: 20px;
                }}
                .diff-section {{
                    margin: 30px 0;
                    padding: 20px;
                    background: #fff;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .image-comparison {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 25px;
                    margin-top: 20px;
                }}
                .image-pair {{
                    border: 1px solid #eee;
                    padding: 15px;
                    border-radius: 8px;
                }}
                .image-box {{
                    text-align: center;
                    margin: 10px 0;
                }}
                .image-box img {{
                    max-width: 100%;
                    height: auto;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }}
                .similarity {{
                    color: {self._get_similarity_color(data['images']['similarity'])};
                    font-weight: bold;
                    margin-top: 10px;
                }}
                h1, h2 {{
                    color: #2c3e50;
                }}
                h1 {{
                    border-bottom: 2px solid #eee;
                    padding-bottom: 15px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>📄 文档对比报告</h1>
            
                <div class="notice">
                    📍 报告文件位置：{os.path.abspath(report_path)}<br>
                    📷 图片存储目录：{os.path.abspath(images_dir)}
                </div>

                <!-- 文本对比区块 -->
                <div class="diff-section">
                    <h2>📝 文本相似度: {data['text']['similarity']:.2%}</h2>
                    <div class="text-comparison">
                        <div style="float: left; width: 48%;">
                            <h3>📜 原稿内容</h3>
                            {data['text']['diff_original']}
                        </div>
                        <div style="float: right; width: 48%;">
                            <h3>✏️ 修改稿内容</h3>
                            {data['text']['diff_modified']}
                        </div>
                        <div style="clear: both;"></div>
                    </div>
                </div>

                <!-- 图片对比区块 -->
                <div class="diff-section">
                    <h2>🖼️ 图片相似度: {data['images']['similarity']:.2%}</h2>
                    <div class="image-comparison">
                        {data['images']['diff_html']}
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html)

        print(f"✅ 报告已生成：{os.path.abspath(report_path)}")
        return report_path


if __name__ == "__main__":
    # 直接指定路径
    original_path = "./market/resources/国民信托私募产品管理人尽调底稿（模板）-尽调对象填写_20250415.docx"
    modified_path = "./compare_document/改动.docx"  
    
    comparator = DocumentComparator(original_path, modified_path)
    print("开始比较Word文档...")
    result = comparator.compare_all()
    print(f"比较完成，请查看 comparison_report.html")