// 在浏览器控制台运行此代码，检查 globalStore 状态
console.log("=== globalStore 当前状态 ===");
try {
  // 如果有全局 globalStore
  if (window.globalStore) {
    const state = window.globalStore.getState();
    console.log("完整状态:", state);
    console.log("panel 状态:", state.panel);
    console.log("model:", state.model);
    console.log("knowledge:", state.knowledge);
    console.log("user:", state.user);
  } else {
    console.log("未找到 globalStore，可能还未初始化");
  }
} catch (e) {
  console.error("获取状态失败:", e);
}

// 检查 Accordion 相关 DOM
console.log("=== Accordion DOM 检查 ===");
const accordions = document.querySelectorAll('[data-accordion-item="true"]');
console.log("找到的 Accordion 项:", accordions.length);
accordions.forEach((item, index) => {
  console.log(`Accordion ${index}:`, item);
  console.log("  - 样式:", window.getComputedStyle(item).display);
  console.log("  - 展开状态:", item.getAttribute('data-state'));
});
