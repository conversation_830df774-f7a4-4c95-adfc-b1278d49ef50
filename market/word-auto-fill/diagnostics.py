import logging
import sys
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('word_plugin')

def test_api():
    logger.info("开始测试 API 连接")
    try:
        url = "http://llm.yanfuinvest.com/v1/models"
        headers = {
            "Authorization": "Bearer sk-ISyVIYc3933iApsiLaz-HQ"
        }
        
        logger.debug(f"发送请求到 {url}")
        response = requests.get(url, headers=headers)
        logger.info(f"API 响应状态码: {response.status_code}")
        logger.debug(f"API 响应内容: {response.text}")
        
    except Exception as e:
        logger.error(f"API 测试失败: {e}", exc_info=True)

def test_files():
    logger.info("开始测试文件访问")
    try:
        import toml
        import glob
        import os
        
        # 测试配置文件读取
        config_path = "config.toml"
        logger.debug(f"尝试读取配置文件: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
            logger.info("成功读取配置文件")
            logger.debug(f"配置内容: {config}")
            
        # 测试知识库文件访问
        kb_path = config['knowledge_bases']['base_yf_knowledge_base']['path']
        abs_kb_path = os.path.abspath(os.path.join(os.path.dirname(config_path), kb_path))
        logger.debug(f"知识库路径: {abs_kb_path}")
        
        files = glob.glob(abs_kb_path)
        logger.info(f"找到 {len(files)} 个知识库文件")
        for file in files:
            logger.debug(f"知识库文件: {file}")
            
            # 尝试读取文件
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.debug(f"成功读取文件 {file}, 大小: {len(content)} 字节")
                
    except Exception as e:
        logger.error(f"文件测试失败: {e}", exc_info=True)

if __name__ == "__main__":
    logger.info("=== 开始诊断测试 ===")
    logger.info(f"当前时间: {datetime.now()}")
    test_api()
    test_files()
    logger.info("=== 诊断测试结束 ===")
