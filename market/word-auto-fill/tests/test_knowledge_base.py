"""
Test knowledge base implementation
"""
import os
import json
import pytest
from unittest.mock import Mock, patch
from src.knowledge_base import KnowledgeBase
from enhanced_extraction.types import QueryType

@pytest.fixture
def kb():
    """Create knowledge base instance for testing"""
    return KnowledgeBase()

@pytest.fixture
def mock_llm():
    """Create mock LLM client"""
    return Mock()

@pytest.fixture
def mock_feishu():
    """Create mock Feishu client"""
    return Mock()

def test_initialization(kb, mock_llm, mock_feishu):
    """Test knowledge base initialization"""
    with patch("src.knowledge_base.ChatOpenAI") as mock_llm_class, \
         patch("src.knowledge_base.FeishuDoc") as mock_feishu_class:
        
        # Setup mocks
        mock_llm_class.return_value = mock_llm
        mock_feishu_class.return_value = mock_feishu
        
        # Initialize knowledge base
        kb.initialize("test-token")
        
        # Verify initialization
        assert kb.llm_client == mock_llm
        assert kb.doc_client == mock_feishu
        assert kb._initialized is True
        assert len(kb.processors) == 3
        
def test_process_docs(kb, mock_feishu, mock_llm):
    """Test document processing"""
    with patch("src.knowledge_base.FeishuDoc") as mock_feishu_class, \
         patch("src.knowledge_base.ChatOpenAI") as mock_llm_class:
        # Setup mock
        mock_feishu_class.return_value = mock_feishu
        mock_llm_class.return_value = mock_llm
        mock_feishu.list_docs.return_value = [
            {"token": "doc1"},
            {"token": "doc2"}
        ]
        mock_feishu.get_doc_content.return_value = {
            "text": "Test content",
            "tables": []
        }
        # Initialize and process docs
        kb.initialize("test-token")
        kb.process_docs()
        # Verify processing
        assert len(kb.test_data) == 2
        assert "doc1" in kb.test_data
        assert "doc2" in kb.test_data


def test_query_processing(kb, mock_llm):
    """Test query processing"""
    # Setup test data
    kb.test_data = {
        "doc1": {
            "content": {
                "text": "Test content with keyword",
                "tables": []
            },
            "config": {}
        }
    }
    with patch("src.knowledge_base.ChatOpenAI") as mock_llm_class:
        mock_llm_class.return_value = mock_llm
        kb.initialize("test-token")
        # Test basic info query
        result = kb.process_query("Find keyword")
        assert result["status"] == "success"
        assert "results" in result
        # Test table query  
        result = kb.process_query("Show table of contents")
        assert result["status"] == "success"
        assert "results" in result
        # Test extended query
        result = kb.process_query("Analyze the content")
        assert result["status"] == "success"
        assert "results" in result
    
def test_query_type_detection(kb):
    """Test query type detection"""
    assert kb._detect_query_type("Show the table") == QueryType.TABLE
    assert kb._detect_query_type("Analyze this") == QueryType.EXTENDED
    assert kb._detect_query_type("Basic info") == QueryType.BASIC_INFO
