# PowerShell 脚本：同时启动前端和后端开发服务器

Write-Host "启动 Word 加载项开发环境..." -ForegroundColor Green

# 检查 Python 环境
if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "错误: 未找到 Python。请确保 Python 已安装并添加到 PATH。" -ForegroundColor Red
    exit 1
}

# 检查 Node.js 环境
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "错误: 未找到 npm。请确保 Node.js 已安装并添加到 PATH。" -ForegroundColor Red
    exit 1
}

# 安装 Python 依赖
Write-Host "检查 Python 依赖..." -ForegroundColor Yellow
pip install -r requirements.txt

# 安装 Node.js 依赖
Write-Host "检查 Node.js 依赖..." -ForegroundColor Yellow
npm install

Write-Host "启动服务器..." -ForegroundColor Green

# 启动后端 Flask 服务器
$backendJob = Start-Job -ScriptBlock {
    Set-Location $args[0]
    python feishu_oauth_callback.py
} -ArgumentList (Get-Location)

Write-Host "后端服务器已启动 (端口 5000)" -ForegroundColor Cyan

# 等待一秒让后端启动
Start-Sleep -Seconds 2

# 启动前端开发服务器
Write-Host "启动前端开发服务器 (端口 3000)..." -ForegroundColor Cyan
npm run dev-server

# 清理后台任务
Write-Host "正在停止后端服务器..." -ForegroundColor Yellow
Stop-Job $backendJob
Remove-Job $backendJob

Write-Host "开发环境已停止。" -ForegroundColor Green
