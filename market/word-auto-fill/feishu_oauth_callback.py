import os
import sys
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
from datetime import datetime, timedelta
import json
from functools import wraps

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# market_dir = os.path.dirname(current_dir)
# sys.path.insert(0, market_dir)

# 暂时注释掉可能有问题的依赖
# from qa_agent.models.feishu_knowledge_base import FeishuKnowledgeBase

app = Flask(__name__)
CORS(app, origins=["https://localhost:3000"])  # 允许来自前端的跨域请求

FEISHU_APP_ID = "cli_a8fe2f0835bdd00c"
FEISHU_APP_SECRET = "GawFcmgUgkp6JN4464iRSb6G4arh5r4J"
FEISHU_BASE_URL = "https://open.feishu.cn/open-apis/authen/v1"
FEISHU_API_BASE_URL = "https://open.feishu.cn/open-apis/contact/v3"

def error_response(message, status_code=400):
    return jsonify({"error": message}), status_code

def require_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return error_response("Missing access token", 401)
        access_token = auth_header.split('Bearer ')[1]
        kwargs['access_token'] = access_token
        return f(*args, **kwargs)
    return decorated

@app.route("/feishu_oauth_callback")
def feishu_oauth_callback():
    """飞书OAuth回调接口"""
    code = request.args.get("code")
    if not code:
        return error_response("Missing code")

    # 用 code 换 user_access_token
    try:
        resp = requests.post(
            "https://open.feishu.cn/open-apis/authen/v1/access_token",
            json={
                "grant_type": "authorization_code",
                "code": code,
                "app_id": FEISHU_APP_ID,
                "app_secret": FEISHU_APP_SECRET,
            }
        )
        data = resp.json()
        user_access_token = data.get("data", {}).get("access_token")
        open_id = data.get("data", {}).get("open_id")
        expires_in = data.get("data", {}).get("expires_in", 7200)

        if not user_access_token or not open_id:
            return error_response(f"Failed to get access token: {data}")

    except Exception as e:
        return error_response(f"Error exchanging code for token: {str(e)}")

    # 获取用户信息
    try:
        user_info = _get_user_info(user_access_token)
        if "error" in user_info:
            return error_response(user_info["error"])

        # 保存用户信息和token到缓存或数据库
        _cache_user_info(open_id, {
            "access_token": user_access_token,
            "expires_at": datetime.now() + timedelta(seconds=expires_in),
            **user_info
        })

    except Exception as e:
        return error_response(f"Error fetching user info: {str(e)}")

    # 返回成功页面
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>飞书授权成功</title>
    <meta charset="utf-8">
    <script>
        function sendMessageToOpener() {{
            const message = {{
                type: 'feishu-login-success',
                data: {{
                    open_id: '{open_id}',
                    access_token: '{user_access_token}',
                    ...{json.dumps(user_info)}
                }}
            }};
            
            if (window.opener) {{
                try {{
                    window.opener.postMessage(message, '*');
                    console.log('已发送消息到父窗口');
                }} catch(e) {{
                    console.error('发送消息失败:', e);
                }}
                setTimeout(() => {{
                    window.close();
                }}, 1000);
            }} else {{
                console.error('找不到父窗口');
                document.getElementById('status').textContent = '无法与Word加载项通信，请手动关闭此窗口';
            }}
        }}
    </script>
</head>
<body onload="sendMessageToOpener()">
    <div style="text-align: center; padding: 20px;">
        <h3>飞书授权成功</h3>
        <p id="status">正在返回Word加载项...</p>
        <p style="color: gray; font-size: 12px;">如果页面没有自动关闭，请手动关闭此窗口</p>
    </div>
</body>
</html>
"""
    return html_content

def _get_user_info(access_token):
    """获取用户信息"""
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        resp = requests.get(f"{FEISHU_BASE_URL}/user_info", headers=headers)
        data = resp.json()
        
        if data["code"] == 0:
            return data["data"]
        else:
            return {"error": f"Failed to get user info: {data}"}

    except Exception as e:
        return {"error": f"Error fetching user info: {str(e)}"}

def _cache_user_info(open_id, user_data):
    """缓存用户信息（实际应用中应使用Redis或数据库）"""
    cache_dir = os.path.join(current_dir, 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    
    cache_file = os.path.join(cache_dir, f'user_{open_id}.json')
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(user_data, f, ensure_ascii=False, default=str)

@app.route("/api/refresh_token", methods=["POST"])
@require_auth
def refresh_token(access_token):
    """刷新访问令牌"""
    try:
        resp = requests.post(
            "https://open.feishu.cn/open-apis/authen/v1/refresh_access_token",
            json={
                "grant_type": "refresh_token",
                "refresh_token": access_token,
                "app_id": FEISHU_APP_ID,
                "app_secret": FEISHU_APP_SECRET,
            }
        )
        data = resp.json()
        
        if data["code"] == 0:
            return jsonify(data["data"])
        else:
            return error_response(f"Failed to refresh token: {data}")

    except Exception as e:
        return error_response(f"Error refreshing token: {str(e)}")

@app.route("/api/user/info", methods=["GET"])
@require_auth
def get_user_info(access_token):
    """获取用户信息"""
    user_info = _get_user_info(access_token)
    if "error" in user_info:
        return error_response(user_info["error"])
    return jsonify(user_info)

@app.route("/api/knowledge_bases", methods=["GET"])
@require_auth
def get_knowledge_bases(access_token):
    """获取知识库列表"""
    try:
        # 暂时返回模拟数据，待集成实际知识库模块时再修改
        return jsonify({
            "success": True, 
            "knowledge_bases": [
                {"id": "1", "name": "示例知识库1"},
                {"id": "2", "name": "示例知识库2"}
            ]
        })

    except Exception as e:
        return error_response(f"Error getting knowledge bases: {str(e)}")

@app.route("/api/refresh_knowledge_bases", methods=["POST"])
@require_auth
def refresh_knowledge_bases(access_token):
    """刷新知识库"""
    try:
        # 暂时返回模拟数据，待集成实际知识库模块时再修改
        return jsonify({
            "success": True,
            "knowledgeBases": [
                {"id": "1", "name": "示例知识库1"},
                {"id": "2", "name": "示例知识库2"}
            ]
        })

    except Exception as e:
        return error_response(f"Error refreshing knowledge bases: {str(e)}")

if __name__ == "__main__":
    app.run(host="127.0.0.1", port=5000, debug=True)

