@echo off
echo 启动 Word 加载项开发环境...

REM 检查 Python 环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python。请确保 Python 已安装并添加到 PATH。
    pause
    exit /b 1
)

REM 检查 Node.js 环境
npm --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 npm。请确保 Node.js 已安装并添加到 PATH。
    pause
    exit /b 1
)

echo 安装 Python 依赖...
pip install -r requirements.txt

echo 安装 Node.js 依赖...
npm install

echo 启动后端服务器 (端口 5000)...
start /B python feishu_oauth_callback.py

echo 等待后端启动...
timeout /t 3 >nul

echo 启动前端开发服务器 (端口 3000)...
npm run dev-server

echo 开发环境已停止。
pause
