import { create } from "zustand";
import { persist } from "zustand/middleware";
import { batchFillPromptTemplate, cursorFillPromptTemplate } from "../configs/promptTemplate";
import { produce } from "../utils/immer";

export interface FeishuUser {
  name: string;
  avatar: string;
  access_token: string;
  openId: string;
}

type Store = {
  panel: ("settings" | "cursorFill" | "contextFill")[];
  model: string;
  knowledge: string[];
  batchFillPrompt: string;
  cursorFillPrompt: string;
  timeout: number;
  temperature: number;
  reasoning: boolean;
  analyzeMode: "basic" | "balanced" | "complete";
  user: FeishuUser | null;
};

const defaultStore: Store = {
  panel: ["settings"],
  model: "",
  knowledge: [],
  batchFillPrompt: batchFillPromptTemplate,
  cursorFillPrompt: cursorFillPromptTemplate,
  timeout: 60,
  temperature: 0,
  reasoning: false,
  analyzeMode: "balanced",
  user: null,
};

export const globalStore = create<Store>()(
  persist(() => defaultStore, {
    name: "global",
    version: 8,
    migrate: (persistedState, version) => {
      const state: any = persistedState;
      if (version <= 1) {
        state.prompt = batchFillPromptTemplate;
      }
      if (version <= 2) {
        state.timeout = 60;
      }
      if (version <= 3) {
        state.batchFillPrompt = state.prompt;
        state.cursorFillPrompt = cursorFillPromptTemplate;
        state.prompt = undefined;
      }
      if (version <= 4) {
        state.panel = [state.panel];
      }
      if (version <= 5) {
        state.temperature = 0;
      }
      if (version <= 6) {
        state.reasoning = false;
        state.batchFillPrompt = state.batchFillPrompt.replace("/no_think", "").trim();
        state.cursorFillPrompt = state.cursorFillPrompt.replace("/no_think", "").trim();
      }
      if (version <= 7) {
        state.analyzeMode = "balanced";
      }
    },
  })
);

const state = globalStore.getState();
if (!state.knowledge.length || !state.model) {
  globalStore.setState(produce((draft) => (draft.panel = ["settings"])));
}
