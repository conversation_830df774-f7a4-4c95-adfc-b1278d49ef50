declare module "@fluentui/react-components" {
  import * as React from "react";

  // Theme
  export const webLightTheme: any;

  // Provider
  export interface FluentProviderProps {
    theme?: any;
    children?: React.ReactNode;
  }
  export const FluentProvider: React.FC<FluentProviderProps>;

  // Dialog Components
  export interface DialogProps {
    open?: boolean;
    children?: React.ReactNode;
    onOpenChange?: (open: boolean) => void;
  }

  export interface DialogSurfaceProps {
    children?: React.ReactNode;
  }

  export interface DialogBodyProps {
    children?: React.ReactNode;
  }

  export interface DialogTitleProps {
    children?: React.ReactNode;
  }

  export interface DialogContentProps {
    children?: React.ReactNode;
  }

  export interface DialogActionsProps {
    children?: React.ReactNode;
  }

  // Button Component
  export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    appearance?: "primary" | "secondary" | "outline" | "subtle" | "transparent";
    children?: React.ReactNode;
    onClick?: (ev: React.MouseEvent<HTMLButtonElement>) => void;
    disabled?: boolean;
    icon?: React.ReactNode;
    size?: 'small' | 'medium' | 'large';
    style?: React.CSSProperties;
  }

  // Spinner Component
  export interface SpinnerProps {
    size?: 'tiny' | 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large';
    label?: string;
  }

  // Field Component
  export interface FieldProps {
    label?: React.ReactNode;
    children?: React.ReactNode;
    validationMessage?: React.ReactNode;
    validationState?: 'error' | 'warning' | 'success' | 'none';
    required?: boolean;
    hint?: React.ReactNode;
    className?: string;
    size?: 'small' | 'medium' | 'large';
  }

  // Textarea Component
  export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    appearance?: 'outline' | 'filled' | 'filled-darker' | 'filled-lighter';
    size?: 'small' | 'medium' | 'large';
    resize?: 'none' | 'horizontal' | 'vertical' | 'both';
    onChange?: (ev: React.ChangeEvent<HTMLTextAreaElement>, data: { value: string }) => void;
  }

  // Select Component
  export interface SelectProps {
    appearance?: 'outline' | 'filled' | 'filled-darker' | 'filled-lighter';
    size?: 'small' | 'medium' | 'large';
    onChange?: (ev: React.ChangeEvent<HTMLSelectElement>, data: { value: string }) => void;
    value?: string;
    defaultValue?: string;
    children?: React.ReactNode;
  }

  // Checkbox Component
  export interface CheckboxProps {
    checked?: boolean;
    defaultChecked?: boolean;
    label?: React.ReactNode;
    onChange?: (ev: React.ChangeEvent<HTMLInputElement>, data: { checked: boolean }) => void;
    disabled?: boolean;
  }

  // Divider Component
  export interface DividerProps {
    appearance?: 'default' | 'strong' | 'brand' | 'subtle';
    vertical?: boolean;
    style?: React.CSSProperties;
  }

  // Link Component
  export interface LinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
    inline?: boolean;
  }

  // TagPicker Components
  export interface TagPickerProps {
    value?: string[];
    defaultValue?: string[];
    selectedOptions?: string[];
    children?: React.ReactNode;
    onChange?: (event: React.ChangeEvent<HTMLElement>, data: { value: string[] }) => void;
    onOptionSelect?: (event: React.MouseEvent<HTMLElement>, data: { value: string; selected: boolean }) => void;
    multiselect?: boolean;
  }

  export interface TagPickerButtonProps {
    children?: React.ReactNode;
  }

  export interface TagPickerControlProps {
    children?: React.ReactNode;
  }

  export interface TagPickerGroupProps {
    children?: React.ReactNode;
  }

  export interface TagPickerListProps {
    children?: React.ReactNode;
  }

  export interface TagPickerOptionProps {
    value: string;
    children?: React.ReactNode;
    secondaryContent?: React.ReactNode;
    media?: React.ReactNode;
    disabled?: boolean;
  }

  // Tag Component
  export interface TagProps {
    children?: React.ReactNode;
    value?: string;
    key?: string;
    shape?: string;
    media?: React.ReactNode;
  }

  // Avatar Component
  export interface AvatarProps {
    name?: string;
    size?: number | 'tiny' | 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large';
    image?: { src: string };
    color?: string;
  }

  // Toaster Component
  export interface ToasterProps {
    position?: 'top' | 'top-end' | 'top-start' | 'bottom' | 'bottom-end' | 'bottom-start';
    toasterId?: string;
  }

  // Input Component
  export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    appearance?: 'outline' | 'filled' | 'filled-darker' | 'filled-lighter';
    size?: 'small' | 'medium' | 'large';
    contentBefore?: React.ReactNode;
    contentAfter?: React.ReactNode;
  }

  // Slider Component
  export interface SliderProps {
    min?: number;
    max?: number;
    step?: number;
    value?: number;
    defaultValue?: number;
    onChange?: (ev: React.ChangeEvent<HTMLInputElement>, data: { value: number }) => void;
  }

  // Accordion Components
  export interface AccordionProps {
    children?: React.ReactNode;
    multiple?: boolean;
    collapsible?: boolean;
    defaultOpenItems?: string[];
    openItems?: string[];
  }

  export interface AccordionItemProps {
    children?: React.ReactNode;
    value: string;
  }

  export interface AccordionHeaderProps {
    children?: React.ReactNode;
  }

  export interface AccordionPanelProps {
    children?: React.ReactNode;
  }

  // Badge Component
  export interface BadgeProps {
    appearance?: 'filled' | 'ghost' | 'outline';
    color?: 'brand' | 'danger' | 'important' | 'informative' | 'severe' | 'subtle' | 'success' | 'warning';
    shape?: 'circular' | 'rounded';
    size?: 'tiny' | 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large';
    children?: React.ReactNode;
  }

  // List Components
  export interface ListProps {
    children?: React.ReactNode;
  }

  export interface ListItemProps {
    children?: React.ReactNode;
  }

  // Text Component
  export interface TextProps {
    children?: React.ReactNode;
    size?: number | 'tiny' | 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large';
    weight?: 'regular' | 'medium' | 'semibold' | 'bold';
    truncate?: boolean;
    wrap?: boolean;
    italic?: boolean;
    strikethrough?: boolean;
    underline?: boolean;
  }

  // Toast Components
  export interface ToastProps {
    toasterId?: string;
    timeout?: number;
    intent?: 'success' | 'error' | 'warning' | 'info';
    children?: React.ReactNode;
    appearance?: 'inverted' | 'normal';
  }

  export interface ToastTitleProps {
    children?: React.ReactNode;
  }

  export interface ToastBodyProps {
    children?: React.ReactNode;
  }

  export interface ToastTriggerProps {
    children?: React.ReactNode;
  }

  // Style Utilities
  export function makeStyles(styles: Record<string, any>): () => Record<string, string>;
  export const tokens: Record<string, string>;

    // Hooks
  export function useToastController(toasterId?: string): {
    dispatchToast: (
      content: React.ReactNode, 
      options?: { intent?: string; pauseOnHover?: boolean; pauseOnWindowBlur?: boolean }
    ) => void;
    dismissToast: (id: string) => void;
    dismissAllToasts: () => void;
  };

  // Component Exports
  export const Dialog: React.FC<DialogProps>;
  export const DialogSurface: React.FC<DialogSurfaceProps>;
  export const DialogBody: React.FC<DialogBodyProps>;
  export const DialogTitle: React.FC<DialogTitleProps>;
  export const DialogContent: React.FC<DialogContentProps>;
  export const DialogActions: React.FC<DialogActionsProps>;
  export const Button: React.FC<ButtonProps>;
  export const Spinner: React.FC<SpinnerProps>;
  export const Field: React.FC<FieldProps>;
  export const Textarea: React.FC<TextareaProps>;
  export const Select: React.FC<SelectProps>;
  export const Checkbox: React.FC<CheckboxProps>;
  export const Divider: React.FC<DividerProps>;
  export const Link: React.FC<LinkProps>;
  export const TagPicker: React.FC<TagPickerProps>;
  export const TagPickerButton: React.FC<TagPickerButtonProps>;
  export const TagPickerControl: React.FC<TagPickerControlProps>;
  export const TagPickerGroup: React.FC<TagPickerGroupProps>;
  export const TagPickerList: React.FC<TagPickerListProps>;
  export const TagPickerOption: React.FC<TagPickerOptionProps>;
  export const Tag: React.FC<TagProps>;
  export const Avatar: React.FC<AvatarProps>;
  export const Toaster: React.FC<ToasterProps>;
  export const Toast: React.FC<ToastProps>;
  export const ToastTitle: React.FC<ToastTitleProps>;
  export const ToastBody: React.FC<ToastBodyProps>;
  export const ToastTrigger: React.FC<ToastTriggerProps>;
  export const Input: React.FC<InputProps>;
  export const Slider: React.FC<SliderProps>;
  export const Accordion: React.FC<AccordionProps>;
  export const AccordionItem: React.FC<AccordionItemProps>;
  export const AccordionHeader: React.FC<AccordionHeaderProps>;
  export const AccordionPanel: React.FC<AccordionPanelProps>;
  export const Badge: React.FC<BadgeProps>;
  export const List: React.FC<ListProps>;
  export const ListItem: React.FC<ListItemProps>;
  export const Text: React.FC<TextProps>;
}
