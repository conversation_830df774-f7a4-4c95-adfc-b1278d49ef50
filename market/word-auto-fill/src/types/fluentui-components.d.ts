// 为 FluentUI 组件增加类型定义
import '@fluentui/react-components';

// 扩展 FluentUI 组件的类型定义
declare module '@fluentui/react-components' {
  // 扩展 AccordionProps 接口
  export interface AccordionProps {
    onToggle?: (event: React.SyntheticEvent, data: { value: string[] | string }) => void;
  }

  // 扩展 AccordionHeaderProps 接口
  export interface AccordionHeaderProps {
    icon?: React.ReactElement;
  }

  // 扩展 AccordionPanelProps 接口
  export interface AccordionPanelProps {
    className?: string;
  }

  // 扩展 BadgeProps 接口
  export interface BadgeProps {
    className?: string;
  }

  // 扩展 ListProps 接口
  export interface ListProps {
    style?: React.CSSProperties;
  }

  // 扩展 ListItemProps 接口
  export interface ListItemProps {
    className?: string;
  }

  // 扩展 ToastTitleProps 接口
  export interface ToastTitleProps {
    action?: React.ReactElement;
  }

  // 扩展 InputProps 接口的 onChange 类型
  export interface InputProps {
    onChange?: (event: React.ChangeEvent<HTMLInputElement>, data: { value: string }) => void;
  }

  // 扩展 Toast 控制器函数类型
  export interface ToastControllerMethods {
    dispatchToast: (
      content: React.ReactNode, 
      options?: { intent?: string; pauseOnHover?: boolean; pauseOnWindowBlur?: boolean }
    ) => void;
  }
}
