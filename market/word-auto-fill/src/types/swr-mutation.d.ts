declare module "swr/mutation" {
  import { SWRResponse, SWRConfiguration } from "swr";

  export interface SWRMutationResponse<Data = any, Error = any> extends SWRResponse<Data, Error> {
    trigger: (...args: any[]) => Promise<Data>;
    reset: () => void;
    isMutating: boolean;
  }

  export type SWRMutationHook<Data = any, Error = any, Args = any> = (
    key: string,
    fetcher: (...args: any[]) => Promise<Data>,
    options?: SWRConfiguration<Data, Error> & {
      onSuccess?: (data: Data, key: string, config: SWRConfiguration<Data, Error>) => void | Promise<void>;
      onError?: (err: Error, key: string, config: SWRConfiguration<Data, Error>) => void | Promise<void>;
    }
  ) => SWRMutationResponse<Data, Error>;

  const useSWRMutation: SWRMutationHook;
  export default useSWRMutation;
}
