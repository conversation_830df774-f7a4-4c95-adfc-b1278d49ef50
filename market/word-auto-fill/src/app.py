from flask import Flask
from flask_cors import CORS
from api.knowledge_base import kb_bp
from api.feishu_routes import feishu_bp

def create_app():
    """创建 Flask 应用实例"""
    app = Flask(__name__)
    CORS(app)  # 启用 CORS 支持

    # 注册蓝图，使用 URL 前缀 /api/v1
    app.register_blueprint(kb_bp, url_prefix='/api/v1/knowledge_base')
    
    # 注册飞书API蓝图（不使用前缀，因为路由中已包含 /api/feishu）
    app.register_blueprint(feishu_bp)

    return app

app = create_app()

if __name__ == '__main__':
    app.run(debug=True, port=5000)
