import { globalStore } from "../store";

export const getCursorFillPrompt = () => {
  const { reasoning, cursorFillPrompt, model } = globalStore.getState();

  if (!model.toLowerCase().includes("qwen3")) {
    return cursorFillPrompt;
  }

  if (reasoning) {
    return cursorFillPrompt.replace(/\/no_think/g, "");
  } else {
    return "/no_think" + cursorFillPrompt;
  }
};

export const getBatchFillPrompt = () => {
  const { reasoning, batchFillPrompt, model } = globalStore.getState();

  if (!model.toLowerCase().includes("qwen3")) {
    return batchFillPrompt;
  }

  if (reasoning) {
    return batchFillPrompt.replace(/\/no_think/g, "").trim();
  } else {
    return "/no_think\n" + batchFillPrompt;
  }
};
