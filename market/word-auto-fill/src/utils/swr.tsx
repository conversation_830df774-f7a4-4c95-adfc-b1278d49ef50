import {
  Link,
  Toast,
  ToastBody,
  Toast<PERSON><PERSON>le,
  Toast<PERSON>rigger,
  useToastController,
} from "@fluentui/react-components";
import { AxiosError } from "axios";
import React from "react";
import { globalToasterId } from "../configs/toaster";

export const voidKey = "void";

export const fetcher = (url: string) => {
  if (url.startsWith("ws://") || url.startsWith("wss://")) {
    // WebSocket连接逻辑
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(url);

      ws.onopen = () => resolve(ws);
      ws.onerror = (err) => reject(err);
      ws.onclose = () => reject(new Error("WebSocket连接已关闭"));
    });
  }

  // 默认HTTP请求逻辑
  return fetch(url);
};

export const extractErrorMessage = (e: unknown) => {
  if (!e) {
    return { message: "未知错误" };
  }
  if (e instanceof AxiosError) {
    if (e.code === "ECONNABORTED") {
      return { message: "请求超时" };
    }

    const message = e.message;
    const data = e.response?.data; // 使用可选链操作符确保e.response存在
    const description = data ? (typeof data === "string" ? data : JSON.stringify(data)) : undefined;

    return { message, description };
  }

  if (e instanceof Error) {
    const message = "出错";
    const description = e.message;

    return { message, description };
  }

  if (e instanceof Event && e.type === "error") {
    // WebSocket错误处理
    const message = "WebSocket连接错误";
    const description = "请检查WebSocket服务器是否运行并使用正确的协议";

    return { message, description };
  }

  const message = "出错";
  const description = typeof e === "string" ? e : JSON.stringify(e);

  return { message, description };
};

export const useOnError = () => {
  const { dispatchToast } = useToastController(globalToasterId);
  const onError = (e: unknown) => {
    const { message, description } = extractErrorMessage(e);
    
    // 确保 message 和 description 是字符串类型
    const safeMessage = typeof message === 'string' ? message : String(message || '未知错误');
    const safeDescription = description && typeof description === 'string' ? description : (description ? String(description) : undefined);    dispatchToast(
      <Toast intent="error">
        <ToastTitle
          action={
            <ToastTrigger>
              <Link>关闭</Link>
            </ToastTrigger>
          }
        >
          {safeMessage}
        </ToastTitle>
        {safeDescription ? <ToastBody>{safeDescription}</ToastBody> : undefined}
      </Toast>,
      { intent: "error", pauseOnHover: true, pauseOnWindowBlur: true }
    );
  };

  return { onError };
};

export const useOnSuccess = () => {
  const { dispatchToast } = useToastController(globalToasterId);

  const onSuccess = (message: React.ReactNode) => {
    // 确保 message 是有效的 React 节点
    const safeMessage = typeof message === 'string' ? message : 
                       typeof message === 'number' ? message :
                       React.isValidElement(message) ? message : 
                       String(message || '操作成功');    dispatchToast(
      <Toast intent="success">
        <ToastTitle
          action={
            <ToastTrigger>
              <Link>关闭</Link>
            </ToastTrigger>
          }
        >
          成功
        </ToastTitle>
        <ToastBody>{safeMessage}</ToastBody>
      </Toast>,
      { intent: "success", pauseOnHover: true, pauseOnWindowBlur: true }
    );
  };

  return { onSuccess };
};

export const Prefetch = <T extends any>({ query }: { query: () => T }) => {
  query();

  return <React.Fragment />;
};

export const getQueryKeyPredicate = (...queryKeys: unknown[]) => {
  return (value: unknown) => {
    if (Array.isArray(value)) {
      return queryKeys.includes(value[0]);
    }

    return queryKeys.includes(value);
  };
};
