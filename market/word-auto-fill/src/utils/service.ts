import axios from "axios";
import { API_CONFIG } from "../config/api";

// 创建统一的service实例
export const service = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:5000/api' : '/api',
  withCredentials: true,
  // 增加超时设置
  timeout: 30000,  // 30秒
});

console.log(`[Axios BaseURL] 当前环境: ${process.env.NODE_ENV}, BaseURL: ${service.defaults.baseURL}`);

// 添加请求拦截器
service.interceptors.request.use(config => {
  console.log(`[API请求] ${config.method?.toUpperCase()} ${config.url}`);
  
  // 检查是否是LLM API请求
  if (config.url?.startsWith('/llm/')) {
    config.baseURL = API_CONFIG.llm.baseUrl;
    config.headers['Authorization'] = `Bearer ${API_CONFIG.llm.apiKey}`;
  }
  return config;
});

// 添加响应拦截器
service.interceptors.response.use(
  response => {
    console.log(`[API响应] ${response.config.url} 成功:`, response.status);
    return response;
  },
  error => {
    if (error.response) {
      // 服务器返回了错误状态码
      console.error(`[API错误] ${error.config?.url} 状态码:`, error.response.status, error.response.data);
    } else if (error.request) {
      // 请求发出但没有收到响应
      console.error(`[API错误] ${error.config?.url} 无响应:`, error.message);
    } else {
      // 请求配置有误
      console.error(`[API错误] 配置错误:`, error.message);
    }
    return Promise.reject(error);
  }
);
