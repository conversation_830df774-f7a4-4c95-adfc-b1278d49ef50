export const validatePrompt = (prompt: string) => {
  if (!prompt.trim()) {
    return { success: false, message: "提示词不能为空" };
  }

  if (!prompt.includes("{context}")) {
    return { success: false, message: "提示词中必须包含 {context} 占位符" };
  }

  if (!prompt.includes("{knowledges_str}")) {
    return { success: false, message: "提示词中必须包含 {knowledges_str} 占位符" };
  }

  return { success: true, message: "提示词验证通过" };
};
