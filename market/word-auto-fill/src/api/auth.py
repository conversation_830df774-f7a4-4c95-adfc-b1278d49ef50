"""
Authentication utils for Feishu API
"""
from functools import wraps
from typing import Any, Dict, Optional
import httpx
from flask import request, jsonify

FEISHU_APP_ID = "cli_a8fe2f0835bdd00c"
FEISHU_APP_SECRET = "XrPxv7q7j3u2R6j5SL1iYhC0EsO9SBhZ"
FEISHU_API_BASE = "https://open.feishu.cn/open-apis"


def verify_feishu_token(access_token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Feishu access token and return user info
    """
    headers = {"Authorization": f"Bearer {access_token}"}
    
    try:
        # Get user info to verify token
        response = httpx.get(
            f"{FEISHU_API_BASE}/authen/v1/user_info",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json().get("data")
        return None
        
    except Exception as e:
        print(f"Error verifying Feishu token: {e}")
        return None


def feishu_auth_required(f):
    """
    Decorator to verify Feishu token in request headers
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get("Authorization")
        
        if not auth_header:
            return jsonify({"error": "No authorization header"}), 401
        
        try:
            # Extract token from Authorization: Bearer xxx
            token = auth_header.split(" ")[1]
        except IndexError:
            return jsonify({"error": "Invalid authorization header"}), 401
            
        # Verify the token
        user_info = verify_feishu_token(token)
        if not user_info:
            return jsonify({"error": "Invalid or expired access token"}), 401
            
        # Add user info to request
        request.feishu_user = user_info
        
        return f(*args, **kwargs)
    
    return decorated
