import sys
import os
# 动态添加 models 路径，兼容 importlib 加载
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../qa_agent/models')))
try:
    from knowledge_base_model import KnowledgeBase
except ImportError as e:
    # 容错处理，主服务可继续运行
    KnowledgeBase = None
    print(f"[警告] knowledge_base_model 导入失败: {e}")

# 动态添加 src/api 路径，兼容 importlib 加载
sys.path.insert(0, os.path.dirname(__file__))
try:
    from auth import feishu_auth_required
except ImportError as e:
    feishu_auth_required = lambda f: f  # 空装饰器，避免报错
    print(f"[警告] auth 导入失败: {e}")

import json
import logging
from typing import Any, Dict, Optional
from flask import Blueprint, request, jsonify, current_app

# Configure logging 
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint
kb_bp = Blueprint('knowledge_base', __name__)

# Global vars
_kb = None  # 不用类型注解，避免 None 报错

def get_kb():
    """Get or create knowledge base instance"""
    global _kb
    if KnowledgeBase is None:
        logger.warning("KnowledgeBase 未导入，相关功能不可用")
        return None
    if not _kb:
        _kb = KnowledgeBase()
    return _kb

@kb_bp.route('/initialize', methods=['POST']) 
@feishu_auth_required
def initialize():
    """Initialize knowledge base with Feishu token"""
    data = request.get_json()
    feishu_user = request.feishu_user

    try:
        # Get knowledge base instance
        kb = get_kb()
        
        # Initialize with Feishu token
        kb.initialize(data['access_token'])
        
        # Process Feishu docs
        kb.process_docs()
        
        return jsonify({
            'status': 'success',
            'message': 'Knowledge base initialized successfully',
            'user': feishu_user
        })
        
    except Exception as e:
        logger.error(f"Error initializing knowledge base: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@kb_bp.route('/query', methods=['POST'])
def query():
    """Process knowledge base query"""
    try:
        # Get request data
        data = request.get_json()
        query_text = data.get('query')
        
        if not query_text:
            return jsonify({
                'status': 'error',
                'message': 'Missing query text'
            }), 400

        # Get knowledge base instance and process query
        kb = get_kb()
        results = kb.process_query(query_text)
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@kb_bp.route('/list', methods=['GET'])
def list_knowledge_bases():
    """
    返回所有可用知识库的基本信息
    """
    try:
        kb = get_kb()
        # 假设 KnowledgeBase 有 list_knowledge_bases 方法
        kb_list = kb.list_knowledge_bases() if hasattr(kb, 'list_knowledge_bases') else [
            {"id": "base_yf_knowledge_base", "name": "本地知识库1", "type": "local_file"},
            {"id": "base_yf_knowledge_base_2", "name": "本地知识库2", "type": "local_file"}
        ]
        # 保证返回值为 list
        if isinstance(kb_list, dict):
            kb_list = [kb_list]
        return jsonify({
            'status': 'success',
            'data': kb_list
        })
    except Exception as e:
        logger.error(f"Error listing knowledge bases: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
