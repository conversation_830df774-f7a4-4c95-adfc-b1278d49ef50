"""
Feishu API routes
"""
from flask import Blueprint, request, jsonify
from .feishu_doc import FeishuDoc
import asyncio

# 创建飞书API蓝图
feishu_bp = Blueprint('feishu', __name__)

@feishu_bp.route('/api/feishu/ping', methods=['GET'])
def ping_feishu_api():
    """
    Ping the Feishu API to check connectivity
    This endpoint helps with reducing 504 timeouts by pre-warming the connection
    """
    # 从请求头获取令牌（可选）
    user_access_token = request.args.get('user_access_token')
    
    # 如果没有提供令牌，返回简单的成功响应
    if not user_access_token:
        return jsonify({
            "status": "ok",
            "message": "Ping successful, but no token provided for API verification"
        })
    
    # 如果提供了令牌，尝试验证API连接
    try:
        # 创建FeishuDoc实例并异步调用ping方法
        feishu_doc = FeishuDoc(user_access_token)
        result = asyncio.run(feishu_doc.ping())
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error pinging Feishu API: {str(e)}"
        }), 500
