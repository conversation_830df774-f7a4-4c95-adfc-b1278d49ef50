"""
Feishu document access utilities
"""
from typing import Dict, Any, List
import httpx
import toml
import os
import asyncio

from .auth import FEISHU_API_BASE

class FeishuDoc:
    def __init__(self, access_token: str):
        self.access_token = access_token
        self.headers = {
            "Authorization": f"Bearer {access_token}"
        }
        
    async def verify_token(self) -> bool:
        """
        Verify if the access token is valid by making a test API call
        Returns True if token is valid, False otherwise
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{FEISHU_API_BASE}/user/v1/info",
                    headers=self.headers,
                    timeout=10.0  # Set explicit timeout
                )
                return response.status_code == 200
        except Exception as e:
            print(f"Error verifying token: {e}")
            return False
            
    async def ping(self) -> Dict[str, Any]:
        """
        Simple ping endpoint to check API connectivity
        """
        try:
            # Use a lighter weight endpoint for ping
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{FEISHU_API_BASE}/user/v1/info",
                    headers=self.headers,
                    timeout=5.0  # Short timeout for ping
                )
                
                if response.status_code == 200:
                    return {"status": "ok", "message": "Feishu API is accessible"}
                else:
                    return {
                        "status": "error", 
                        "message": f"Feishu API returned status code {response.status_code}"
                    }
        except httpx.ReadTimeout:
            return {"status": "timeout", "message": "Feishu API request timed out"}
        except Exception as e:
            return {"status": "error", "message": f"Error pinging Feishu API: {str(e)}"}

    async def list_docs(self) -> List[Dict[str, Any]]:
        """
        List all docs accessible by the user
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{FEISHU_API_BASE}/drive/v1/files",
                    headers=self.headers
                )
                
                # If token expired, try to refresh and retry once
                if response.status_code == 401:
                    new_token = await self.refresh_token()
                    if new_token:
                        response = await client.get(
                            f"{FEISHU_API_BASE}/drive/v1/files",
                            headers=self.headers
                        )
                        
                response.raise_for_status()
                return response.json().get("data", {}).get("files", [])
        except Exception as e:
            print(f"Error listing docs: {e}")
            return []
            
    async def get_doc_content(self, doc_token: str) -> Dict[str, Any]:
        """
        Get content of a specific document
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{FEISHU_API_BASE}/doc/v2/{doc_token}/content",
                    headers=self.headers
                )
                
                # If token expired, try to refresh and retry once
                if response.status_code == 401:
                    new_token = await self.refresh_token()
                    if new_token:
                        response = await client.get(
                            f"{FEISHU_API_BASE}/doc/v2/{doc_token}/content",
                            headers=self.headers
                        )
                        
                response.raise_for_status()
                return response.json().get("data", {})
        except Exception as e:
            print(f"Error getting doc content: {e}")
            return {}
            
    async def refresh_token(self) -> str:
        """
        Refresh the access token using the app_id and app_secret
        Returns the new access token
        """
        try:
            async with httpx.AsyncClient() as client:
                # Get app credentials from config
                config_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 
                    "market", 
                    "qa_agent", 
                    "config.toml"
                )
                config = toml.load(config_path)
                feishu_config = config.get("knowledge_bases", {}).get("feishu", {}).get("market", {})
                app_id = feishu_config.get("app_id")
                app_secret = feishu_config.get("app_secret")
                
                if not app_id or not app_secret:
                    raise ValueError("app_id or app_secret not set in config.toml")
                
                response = await client.post(
                    f"{FEISHU_API_BASE}/auth/v3/tenant_access_token/internal",
                    json={
                        "app_id": app_id,
                        "app_secret": app_secret
                    }
                )
                response.raise_for_status()
                data = response.json()
                
                new_token = data.get("tenant_access_token")
                if not new_token:
                    raise ValueError("Failed to get new access token")
                
                # Update instance token and headers
                self.access_token = new_token
                self.headers = {
                    "Authorization": f"Bearer {new_token}"
                }
                
                return new_token
        except Exception as e:
            print(f"Error refreshing token: {e}")
            return None
