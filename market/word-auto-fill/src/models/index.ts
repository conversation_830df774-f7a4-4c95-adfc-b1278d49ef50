import { z } from "zod";

export const modelSchema = z.object({
  model: z.string(),
  api_key: z.string(),
  base_url: z.string(),
  max_tokens: z.number().optional(),
});
export const modelListOutputSchema = z.record(z.string(), modelSchema);

export const knowledgeSchema = z.object({
  id: z.string(),
  files: z.array(z.string()),
  type: z.string(),
  path: z.string(),
  raw_size: z.number(),
  token_size: z.number(),
});
export const knowledgeListOutputSchema = z.record(z.string(), knowledgeSchema);

// 定义两种可能的响应类型：成功响应和错误响应
const wordAnalyzeSuccessSchema = z.object({
  success: z.literal(true),
  file_path: z.string(),
});

const wordAnalyzeErrorSchema = z.object({
  success: z.literal(false),
  error: z.string().optional(),
  message: z.string().optional(),
});

// 使用联合类型组合两种可能的响应
export const wordAnalyzeOutputSchema = z.union([
  wordAnalyzeSuccessSchema,
  wordAnalyzeErrorSchema,
]);

export const queryPreviewInputSchema = z.object({
  prompt: z.string(),
  kl_ids: z.array(z.string()),
});
export const queryPreviewOutputSchema = z.object({
  prompt: z.string(),
});

export const queryInputSchema = z.object({
  prompt: z.string(),
  kl_ids: z.array(z.string()),
  llm_config: modelSchema,
  streamable: z.boolean().optional(),
});
export const queryOutputSchema = z.record(z.string(), z.string());
