{"test_sample_path": "test_sample.json", "basic_info": {"search_keywords": ["公司名称", "注册资本", "法定代表人", "成立时间", "主营业务", "组织结构", "人员规模", "资质牌照"], "section_keywords": ["公司概况", "基本情况", "公司简介", "历史沿革", "发展战略", "资金渠道"]}, "table": {"search_keywords": ["股权结构", "产品业绩", "团队介绍", "风险指标", "财务数据"], "table_titles": ["股权结构表", "产品业绩表", "团队成员表", "风险监控表", "财务指标表"]}, "extended": {"search_keywords": ["投资策略", "风险控制", "运营管理", "合规制度", "研究能力"], "section_keywords": ["投资研究", "风险管理", "运营流程", "内控制度", "投研团队"]}, "model": {"name": "Qwen3-235B-A22B", "base_url": "http://llm.yanfuinvest.com/v1", "api_key": "sk-ISyVIYc3933iApsiLaz-HQ", "temperature": 0, "max_retries": 3}, "extractors": {"basic_info": {"prompt_template": "Please extract specific information about \"{query}\" from the document.\n\nRequirements:\n1. Extract only content directly related to \"{query}\"\n2. Keep original text, no rewrites\n3. Return empty string if not found\n4. No extra notes\n\nDocument:\n{text_content}\n\nReturn answer directly without any other content.", "expected_fields": ["description", "content", "details"]}, "table": {"prompt_template": "Please analyze the table and extract information about \"{query}\":\n\n{table_content}\n\nRequirements:\n1. Extract rows directly related to \"{query}\"\n2. Keep original format and content\n3. Return empty string if not found\n\nReturn extracted table rows directly without any explanation.", "expected_fields": ["header", "rows", "columns"]}, "extended": {"prompt_template": "Please analyze the document and answer \"{query}\":\n\n{text_content}\n\nRequirements:\n1. Comprehensive analysis\n2. Concise and objective summary\n3. State if information not found\n4. Strictly based on document\n\nProvide analysis directly without repeating original text or extra notes.", "expected_fields": ["analysis", "summary", "points"]}}, "processors": {"basic_info": {"max_tokens": 1000, "temperature": 0, "retry_count": 3}, "table": {"max_tokens": 2000, "temperature": 0, "retry_count": 3}, "extended": {"max_tokens": 3000, "temperature": 0.3, "retry_count": 3}}, "doc_types": ["txt", "docx", "pdf", "md"], "max_doc_size": 10485760, "cache_ttl": 3600}