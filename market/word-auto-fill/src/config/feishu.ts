// Constants for Feishu OAuth configuration
export const FEISHU_APP_ID = "cli_a8fe2f0835bdd00c"; // 请替换为你的实际app_id

// Get the base URL from current window location
const getBaseUrl = () => {
    // 统一使用HTTPS和3000端口，与前端开发服务器保持一致
    if (process.env.NODE_ENV === 'development') {
        return 'https://localhost:3000';
    }
    const url = new URL(window.location.href);
    return `${url.protocol}//${url.host}`;
};

// Redirect URI for Feishu OAuth callback
export const FEISHU_REDIRECT_URI = encodeURIComponent(`${getBaseUrl()}/feishu_oauth_callback`);

// Feishu OAuth endpoints
export const FEISHU_AUTH_ENDPOINT = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${FEISHU_APP_ID}&redirect_uri=${FEISHU_REDIRECT_URI}&state=login`;
