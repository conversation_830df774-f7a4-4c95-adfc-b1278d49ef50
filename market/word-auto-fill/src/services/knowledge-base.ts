import { EventEmitter } from '../utils/events';
import { API_CONFIG } from '../config/api';

export interface KnowledgeBase {
  name: string;
  type: string;
  tokenCount: number; 
  docCount: number;
  data?: any;
}

export class KnowledgeBaseService {
  private static instance: KnowledgeBaseService;
  private knowledgeBases: KnowledgeBase[] = [];
  private events = new EventEmitter();

  private constructor() {}

  static getInstance(): KnowledgeBaseService {
    if (!KnowledgeBaseService.instance) {
      KnowledgeBaseService.instance = new KnowledgeBaseService();
    }
    return KnowledgeBaseService.instance;
  }

  async refresh(): Promise<KnowledgeBase[]> {
    try {
      // 尝试从服务器获取知识库列表
      const response = await fetch('/api/knowledge_list');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || '知识库刷新失败');
      }

      this.knowledgeBases = Array.isArray(data) ? data : data.data || [];
      this.events.emit('knowledgeBasesUpdated', this.knowledgeBases);
      return this.knowledgeBases;
      
    } catch (err) {
      console.error('从服务器获取知识库失败，尝试从本地加载:', err);
      
      // 如果服务器请求失败，尝试从本地加载
      try {
        const fs = require('fs');
        const path = require('path');
        const glob = require('glob');

        const basePath = path.resolve(__dirname, API_CONFIG.knowledgeBase.localPath);
        const files = glob.sync(basePath);
        
        this.knowledgeBases = await Promise.all(
          files.map(async (file: string) => {
            const content = fs.readFileSync(file, 'utf8');
            let data;
            try {
              data = JSON.parse(content);
            } catch (e) {
              console.error(`解析文件 ${file} 失败:`, e);
              data = {};
            }
            
            return {
              name: path.basename(file),
              type: 'local_file',
              tokenCount: content.length,
              docCount: 1,
              data
            };
          })
        );

        this.events.emit('knowledgeBasesUpdated', this.knowledgeBases);
        return this.knowledgeBases;

      } catch (localErr) {
        console.error('本地加载知识库失败:', localErr);
        this.knowledgeBases = [];
        this.events.emit('knowledgeBasesUpdated', this.knowledgeBases);
        return this.knowledgeBases;
      }
    }
  }

  // 获取所有知识库列表
  async getKnowledgeBases(): Promise<KnowledgeBase[]> {
    if (this.knowledgeBases.length === 0) {
      await this.refresh();
    }
    return this.knowledgeBases;
  }

  // 获取单个知识库内容
  async getKnowledgeBase(name: string): Promise<any> {
    const kb = this.knowledgeBases.find(kb => kb.name === name);
    if (!kb) {
      throw new Error(`Knowledge base ${name} not found`);
    }
    return kb.data;
  }

  // 监听知识库更新事件
  onKnowledgeBasesUpdated(callback: (bases: KnowledgeBase[]) => void): () => void {
    this.events.on('knowledgeBasesUpdated', callback);
    return () => this.events.off('knowledgeBasesUpdated', callback);
  }
}
