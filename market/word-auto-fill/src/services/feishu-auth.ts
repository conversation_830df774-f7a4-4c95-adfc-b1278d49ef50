// 飞书登录相关的状态和事件处理
import { FEISHU_APP_ID, FEISHU_REDIRECT_URI } from "../config/feishu";
import { KnowledgeBase } from "../types/knowledge-base";

export interface FeishuUser {
  name: string;
  avatar: string;
  openId: string;
  accessToken: string;
}

interface FeishuLoginSuccess {
  type: 'feishu-login-success';
  data: {
    open_id: string;
    access_token: string;
    name: string;
    avatar_url: string;
  };
}

export class FeishuAuthManager {
  private static instance: FeishuAuthManager;
  private userInfo: FeishuUser | null = null;
  private knowledgeBases: KnowledgeBase[] = [];

  private constructor() {
    this.setupMessageListener();
  }

  static getInstance(): FeishuAuthManager {
    if (!FeishuAuthManager.instance) {
      FeishuAuthManager.instance = new FeishuAuthManager();
    }
    return FeishuAuthManager.instance;
  }

  private setupMessageListener() {
    window.addEventListener("message", async (event) => {
      if (event.data.type === "feishu-login-success") {
        await this.handleLoginSuccess(event.data as FeishuLoginSuccess);
      }
    });
  }

  private async handleLoginSuccess(data: FeishuLoginSuccess) {
    this.userInfo = {
      name: data.data.name,
      avatar: data.data.avatar_url,
      openId: data.data.open_id,
      accessToken: data.data.access_token,
    };

    await this.updateUI(true);
    await this.refreshKnowledgeBases();
  }

  private async updateUI(isLoggedIn: boolean) {
    const loginBtn = document.getElementById("feishu-login-btn");
    const logoutBtn = document.getElementById("feishu-logout-btn");
    const userInfoDiv = document.getElementById("feishu-user-info");
    const kbStatusDiv = document.getElementById("kb-status");

    if (loginBtn) loginBtn.style.display = isLoggedIn ? "none" : "block";
    if (logoutBtn) logoutBtn.style.display = isLoggedIn ? "block" : "none";

    if (userInfoDiv) {
      if (isLoggedIn && this.userInfo) {
        userInfoDiv.innerHTML = `
          <div class="user-info">
            <img src="${this.userInfo.avatar}" alt="${this.userInfo.name}" class="user-avatar">
            <span>${this.userInfo.name}</span>
          </div>
        `;
        userInfoDiv.style.display = "block";
      } else {
        userInfoDiv.style.display = "none";
      }
    }

    if (kbStatusDiv && isLoggedIn) {
      await this.refreshKnowledgeBases();
    }
  }

  private async fetchWithAuth(url: string, options: RequestInit = {}) {
    if (!this.userInfo?.accessToken) {
      throw new Error("No access token available");
    }

    const headers = new Headers(options.headers || {});
    headers.set('Authorization', `Bearer ${this.userInfo.accessToken}`);

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }

  public async refreshKnowledgeBases() {
    const statusDiv = document.getElementById("kb-status");
    if (!statusDiv) return;

    try {
      statusDiv.textContent = "正在刷新知识库...";
      statusDiv.className = "status-text loading";

      const data = await this.fetchWithAuth('/api/refresh_knowledge_bases', {
        method: 'POST',
      });

      if (data.success) {
        this.knowledgeBases = data.knowledgeBases;
        
        const totalTokens = this.knowledgeBases.reduce((sum, kb) => sum + kb.tokenCount, 0);
        const totalDocs = this.knowledgeBases.reduce((sum, kb) => sum + kb.docCount, 0);
        
        statusDiv.textContent = `知识库已连接 (${totalDocs} 个文档, ${totalTokens} tokens)`;
        statusDiv.className = "status-text success";
      } else {
        throw new Error(data.error || "知识库刷新失败");
      }
    } catch (error) {
      console.error("刷新知识库失败:", error);
      statusDiv.textContent = `知识库刷新失败: ${error instanceof Error ? error.message : String(error)}`;
      statusDiv.className = "status-text error";
    }
  }

  public openLoginWindow() {
    const width = 800;
    const height = 600;
    const left = (window.screen.width - width) / 2;
    const top = (window.screen.height - height) / 2;

    const authUrl = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${FEISHU_APP_ID}&redirect_uri=${FEISHU_REDIRECT_URI}&state=login`;

    const popup = window.open(
      authUrl,
      "feishu_login",
      `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
    );

    if (!popup || popup.closed || typeof popup.closed === "undefined") {
      alert("弹窗被阻止，请允许此网站的弹窗以继续登录");
    }
  }

  public async handleLogout() {
    this.userInfo = null;
    this.knowledgeBases = [];
    await this.updateUI(false);
  }

  public getUser(): FeishuUser | null {
    return this.userInfo;
  }

  public getKnowledgeBases(): KnowledgeBase[] {
    return this.knowledgeBases;
  }

  public async refreshToken(): Promise<void> {
    if (!this.userInfo?.accessToken) {
      throw new Error("No refresh token available");
    }

    try {
      const data = await this.fetchWithAuth('/api/refresh_token', {
        method: 'POST',
      });

      if (data.access_token) {
        this.userInfo = {
          ...this.userInfo,
          accessToken: data.access_token,
        };
      } else {
        throw new Error("Failed to refresh token");
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      this.handleLogout();
      throw error;
    }
  }

  public async getUserInfo(): Promise<any> {
    return this.fetchWithAuth('/api/user/info');
  }
}
