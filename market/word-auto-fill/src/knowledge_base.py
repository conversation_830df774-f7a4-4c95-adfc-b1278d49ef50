"""
KnowledgeBase class implementation
"""
import os
import json
from typing import Dict, Any, List, Optional
from langchain_openai import <PERSON>t<PERSON><PERSON>A<PERSON>
from tenacity import retry, stop_after_attempt, wait_exponential

from .feishu_doc import FeishuDoc
from enhanced_extraction.utils.doc_extract_util import extract_word_content  
from enhanced_extraction.utils.config_generator import generate_config_for_doc
from enhanced_extraction.processors import (
    TableProcessor, 
    BasicInfoProcessor,
    ExtendedProcessor
)
from enhanced_extraction.types import Query, QueryType


class KnowledgeBase:
    """Knowledge base for processing documents and queries"""
    
    def __init__(self):
        self.llm_client = None
        self.doc_client = None
        self.processors = {}
        self.config = {}
        self.test_data = {}
        self._initialized = False
        
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(min=1, max=10))
    def initialize(self, access_token: str) -> None:
        """
        Initialize knowledge base with Feishu access token
        
        Args:
            access_token (str): Feishu access token for accessing docs
        """
        try:
            # Initialize LLM client
            self.llm_client = ChatOpenAI(
                model="Qwen3-235B-A22B",
                base_url="http://llm.yanfuinvest.com/v1",
                api_key="sk-ISyVIYc3933iApsiLaz-HQ", 
                temperature=0,
                max_retries=3
            )
            
            # Initialize Feishu doc client
            self.doc_client = FeishuDoc(access_token)
            
            # Load config
            config_path = os.path.join(os.path.dirname(__file__), "config", "kb_config.json")
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
            
            # Initialize processors
            self.processors = {
                QueryType.BASIC_INFO: BasicInfoProcessor(
                    self.llm_client,
                    self.config
                ),
                QueryType.TABLE: TableProcessor(
                    self.llm_client,
                    self.config  
                ),
                QueryType.EXTENDED: ExtendedProcessor(
                    self.llm_client,
                    self.config
                )
            }
            
            # Mark as initialized
            self._initialized = True
            
        except Exception as e:
            self._initialized = False
            raise RuntimeError(f"Failed to initialize knowledge base: {e}")
            
    async def process_docs(self) -> None:
        """Process docs from Feishu and build knowledge base"""
        if not self._initialized:
            raise RuntimeError("Knowledge base not initialized")
            
        try:
            # List available docs
            docs = await self.doc_client.list_docs()
            
            for doc in docs:
                # Get doc content
                content = await self.doc_client.get_doc_content(doc["token"])
                
                # Extract text and tables
                extracted = extract_word_content(content)
                
                # Generate config for doc
                doc_config = generate_config_for_doc(extracted["text"])
                
                # Store extracted content and config
                self.test_data[doc["token"]] = {
                    "content": extracted,
                    "config": doc_config
                }
                
        except Exception as e:
            raise RuntimeError(f"Failed to process docs: {e}")
    
    def process_query(self, query_text: str) -> Dict[str, Any]:
        """
        Process a user query
        
        Args:
            query_text (str): Query text from user
            
        Returns:
            Dict containing query results
        """
        if not self._initialized:
            raise RuntimeError("Knowledge base not initialized")
            
        try:
            # Create query object
            query = Query(
                text=query_text,
                type=self._detect_query_type(query_text)
            )
            
            # Get appropriate processor
            processor = self.processors.get(query.type)
            if not processor:
                raise ValueError(f"No processor for query type: {query.type}")
                
            # Process query and return results
            results = processor.process(query)
            return {
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
            
    def _detect_query_type(self, query_text: str) -> QueryType:
        """
        Detect query type from text
        
        Args:
            query_text (str): Query text to analyze
            
        Returns:
            QueryType enum value
        """
        # Default to basic info
        query_type = QueryType.BASIC_INFO
        
        # Check for table indicators
        if any(kw in query_text.lower() for kw in ["表格", "列表", "清单"]):
            query_type = QueryType.TABLE
            
        # Check for extended analysis indicators
        elif any(kw in query_text.lower() for kw in ["分析", "总结", "说明", "描述"]):
            query_type = QueryType.EXTENDED
            
        return query_type
