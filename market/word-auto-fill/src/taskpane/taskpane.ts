/* global Word console */

import z from "zod";
import { modelSchema } from "../models";
import * as service from "../service";
import { getBatchFillPrompt, getCursorFillPrompt } from "../utils/model";
import { escape } from "../utils/regexp";
import { marker, removeAttributes } from "../utils/word";
import { FEISHU_APP_ID, FEISHU_REDIRECT_URI } from "../config/feishu";

const withMarker = async <T>(
  context: Word.RequestContext,
  func: (marker: string, selection: Word.Range) => Promise<T> | T
) => {
  const selection = context.document.getSelection();
  context.load(selection, "paragraphs");
  await context.sync();

  selection.insertText(marker, Word.InsertLocation.replace);
  await context.sync();

  return await func(marker, selection);
};

export const getContextOfVariable = async (variables: string[]) => {
  if (!variables.length) return "";

  const [variable] = variables;
  return await Word.run(async (context) => {
    const body = context.document.body;
    const html = body.getHtml();
    await context.sync();

    const parser = new DOMParser();
    const doc = parser.parseFromString(html.value, "text/html");

    for (const attr of removeAttributes) {
      doc.querySelectorAll(`[${attr}]`).forEach((el) => el.removeAttribute(attr));
    }

    doc.querySelectorAll("span, a, strong, em, i, b, u, sub, sup").forEach((element) => {
      const text = element.textContent || "";
      element.parentNode?.replaceChild(doc.createTextNode(text), element);
    });

    const cleanedHtml = doc.body.innerHTML;
    let htmlString = cleanedHtml;

    const index = htmlString.indexOf(variable);
    if (index === -1) return "";

    const start = Math.max(0, index - 10000);
    const end = Math.min(htmlString.length, index + marker.length + 2000);
    return htmlString.substring(start, end);
  });
};

export const getContextUnderCursor = () => {
  return Word.run((context) => {
    return withMarker(context, (marker) => {
      return getContextOfVariable([marker]);
    });
  });
};

export const getVariables = () => {
  return Word.run(async (context) => {
    const searchResults = context.document.body.search("[{]*[}]", { matchWildcards: true });
    context.load(searchResults, "text");
    await context.sync();

    return searchResults.items.map((item) => item.text);
  });
};

export const getNextVariable = async (): Promise<string> => {
  const [variable] = await getVariables();
  return variable;
};

export const findAndReplace = async (key: string, value: string) => {
  return await Word.run(async (context) => {
    const searchResults = context.document.body.search(key);
    context.load(searchResults, "text");

    await context.sync();

    for (const item of searchResults.items) {
      item.insertText(value, Word.InsertLocation.replace);
    }
    await context.sync();
  });
};

export const queryContextUnderCursor = async (
  knowledge: string[],
  llmConfig: z.infer<typeof modelSchema>,
  stream: boolean,
  controller?: AbortController
) => {
  const contextText = await getContextUnderCursor();
  const prompt = getCursorFillPrompt().replace("{context}", contextText);

  return await service.query(
    { prompt, kl_ids: knowledge, llm_config: llmConfig, streamable: stream },
    controller
  );
};

export const queryContext = async (
  contextText: string,
  knowledge: string[],
  llmConfig: z.infer<typeof modelSchema>,
  stream: boolean,
  controller?: AbortController
) => {
  const prompt = getBatchFillPrompt().replace("{context}", contextText);

  return await service.query(
    { prompt, kl_ids: knowledge, llm_config: llmConfig, streamable: stream },
    controller
  );
};

export const getFilePath = () => {
  return new Promise<string>((resolve) =>
    Office.context.document.getFilePropertiesAsync((result) => resolve(result.value.url))
  );
};

export const reloadDocument = async (filePath: string) => {
  const currentFilePath = await getFilePath();
  if (filePath === currentFilePath) {
    throw new Error("文档已打开");
  }

  await Word.run(async (context) => {
    context.application.openDocument(filePath);
    context.document.close("SkipSave");
  });
};

// ===== 飞书 OAuth 登录按钮逻辑（临时调试用 API Explorer 回调地址） =====
// const FEISHU_APP_ID = "cli_a8fe2f0835bdd00c";
// const FEISHU_REDIRECT_URI = encodeURIComponent("https://open.feishu.cn/api-explorer/cli_a8fe2f0835bdd00c/oauth/callback");

// window.addEventListener("DOMContentLoaded", () => {
//   const btn = document.getElementById("feishu-login-btn");
//   if (btn) {
//     btn.addEventListener("click", () => {
//       window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${FEISHU_APP_ID}&redirect_uri=${FEISHU_REDIRECT_URI}&state=login`;
//     });
//   }
// });
interface OAuthMessage {
  type: string;
  open_id: string;
  access_token: string;
}

// 备用弹窗函数
function openPopupFallback(authUrl: string) {
  console.log("使用备用弹窗方式...");
  const width = 800;
  const height = 600;
  const left = (window.screen.width - width) / 2;
  const top = (window.screen.height - height) / 2;
  
  const popup = window.open(
    authUrl,
    "feishu_login",
    `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
  );

  if (!popup || popup.closed || typeof popup.closed == 'undefined') {
    console.error("弹窗被阻止");
    return;
  }

  // 创建一个新的消息监听器，避免重复监听
  const messageHandler = async (event: MessageEvent) => {
    console.log("收到消息事件:", event);    // 安全检查：确保消息来自预期的源
    if (event.origin !== "http://localhost:3000") {
      console.log("忽略来自未知源的消息:", event.origin);
      return;
    }

    try {
      const data = event.data;
      if (data && data.type === "feishu-login-success") {
        console.log("登录成功", data);
        
        // 移除消息监听器
        window.removeEventListener("message", messageHandler);
        
        // 关闭弹窗
        if (popup && !popup.closed) {
          popup.close();
        }
          // 更新UI状态
        updateLoginStatus(true, data);
        
        // 加载用户信息并更新UI
        const userInfo = data.data;
        const userNameElement = document.getElementById('user-name');
        if (userNameElement && userInfo.data && userInfo.data.name) {
          userNameElement.textContent = userInfo.data.name;
        }

        // 加载知识库
        const kbStatus = document.getElementById('kb-status');
        if (kbStatus) {
          kbStatus.textContent = "正在加载知识库...";
          try {
            await loadKnowledgeBase(userInfo.data.open_id, userInfo.data.access_token);
            kbStatus.textContent = "知识库加载成功";
            kbStatus.style.color = "green";
          } catch (error) {
            console.error("知识库加载失败", error);
            kbStatus.textContent = "知识库加载失败";
            kbStatus.style.color = "red";
          }
        }
      }
    } catch (error) {
      console.error("处理登录消息失败", error);
    }
  };

  // 添加消息监听器
  window.addEventListener("message", messageHandler);
  
  // 设置超时清理
  setTimeout(() => {
    if (popup && !popup.closed) {
      console.log("弹窗超时，自动清理");
      window.removeEventListener("message", messageHandler);
    }
  }, 300000); // 5分钟超时
}

// 飞书登录函数
function openFeishuLogin() {
  const authUrl = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${FEISHU_APP_ID}&redirect_uri=${FEISHU_REDIRECT_URI}&state=login`;
  // Office WebView2/桌面端推荐同窗口跳转
  window.location.href = authUrl;
}

// 更新UI状态
function updateLoginStatus(isLoggedIn: boolean, userInfo?: any) {
  const loginButton = document.getElementById('feishu-login-btn');
  const userInfoElement = document.getElementById('user-info');
  
  if (loginButton) {
    loginButton.style.display = isLoggedIn ? 'none' : 'block';
  }
  if (userInfoElement) {
    userInfoElement.style.display = isLoggedIn ? 'block' : 'none';
  }
    // 🔧 修复：同时更新 globalStore 中的 user 状态
  try {
    // 动态导入 globalStore（避免循环依赖）
    import('../store').then(({ globalStore }) => {
      globalStore.setState({
        user: isLoggedIn && userInfo?.data ? {
          name: userInfo.data.name || "飞书用户",
          avatar: userInfo.data.avatar_url || userInfo.data.avatar_thumb || "",
          access_token: userInfo.data.access_token || "",
          openId: userInfo.data.open_id || ""
        } : null
      });
      console.log("已更新 globalStore 中的 user 状态:", isLoggedIn ? "已登录" : "未登录");
    }).catch(e => {
      console.warn("更新 globalStore 失败:", e);
    });
  } catch (e) {
    console.warn("导入 globalStore 失败:", e);
  }
}

// 飞书知识库加载
async function loadKnowledgeBase(_openId: string, accessToken: string) {
  console.log("开始加载知识库...");
  
  try {    // 从配置中获取基础URL
    const baseUrl = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3000' 
      : '';
      
    console.log(`使用API地址: ${baseUrl}/api/refresh_knowledge_bases`);
    
    // 准备请求头，仅在有accessToken时添加Authorization头
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    if (accessToken && accessToken !== 'undefined' && accessToken !== 'null') {
      headers['Authorization'] = `Bearer ${accessToken}`;
      console.log("使用飞书认证token进行请求");
    } else {
      console.log("无飞书认证token，将尝试加载本地知识库");
    }
    
    const response = await fetch(`${baseUrl}/api/refresh_knowledge_bases`, {
      method: 'POST',
      headers: headers
    });

    console.log("API响应状态:", response.status, response.statusText);
    
    if (!response.ok) {
      console.error("API错误状态:", response.status, response.statusText);
      console.log("尝试加载本地知识库...");
      // 如果从服务器加载失败，尝试从本地加载
      return loadLocalKnowledgeBases();
    }

    const data = await response.json();
    console.log("API响应数据:", data);
    
    if (!data.success) {
      console.error("API业务逻辑错误:", data.error || "未知错误");
      throw new Error(data.error || '知识库刷新失败');
    }

    // 检查知识库tokens
    if (data.knowledgeBases && data.knowledgeBases.length > 0) {
      let totalTokens = 0;
      data.knowledgeBases.forEach((kb: any) => {
        console.log(`知识库 ${kb.name}: ${kb.tokenCount || 0} tokens`);
        totalTokens += (kb.tokenCount || 0);
      });
      console.log(`总Token数: ${totalTokens}`);
      
      if (totalTokens === 0) {
        console.warn("警告: 知识库总Token数为0，可能数据加载不完整");
      }
    } else {
      console.warn("警告: 没有找到知识库数据");
    }

    // 自动刷新知识库UI
    updateKnowledgeBaseUI(data.knowledgeBases || [], data.models || []);
    return data.knowledgeBases;  } catch (error) {
    console.error("加载知识库时发生异常:", error);
    console.log("尝试回退到本地知识库加载...");
    // 回退到本地知识库加载
    return await loadLocalKnowledgeBases();
  }
}

// 检查本地存储并刷新登录状态
// 加载本地知识库
async function loadLocalKnowledgeBases() {
  console.log("尝试加载本地知识库...");
  
  try {
    // 使用KnowledgeBaseService从本地文件加载知识库
    const KnowledgeBaseService = (await import('../services/knowledge-base')).KnowledgeBaseService;
    const kbService = KnowledgeBaseService.getInstance();
    const bases = await kbService.getKnowledgeBases();
    
    // 自动刷新知识库UI
    updateKnowledgeBaseUI(bases || [], []);
    console.log(`已加载 ${bases.length} 个本地知识库`);
    return bases;
  } catch (error) {
    console.error("加载本地知识库失败:", error);
    return [];
  }
}

function tryAutoLoginFromStorage() {
  let userInfoStr = localStorage.getItem('feishu_user_info') || sessionStorage.getItem('feishu_user_info');
  console.log('[tryAutoLoginFromStorage] 读取到的 userInfoStr:', userInfoStr);
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr);
      console.log('[tryAutoLoginFromStorage] 解析后的 userInfo:', userInfo);
      if (userInfo && userInfo.data && userInfo.data.access_token) {
        // 新增：后端校验 token 是否有效
        fetch('/api/feishu_login_status', {
          headers: { Authorization: `Bearer ${userInfo.data.access_token}` }
        })
        .then(res => res.json())
        .then(data => {
          if (data.status === 'success') {
            updateLoginStatus(true, userInfo);
            const userNameElement = document.getElementById('user-name');
            if (userNameElement && userInfo.data.name) {
              userNameElement.textContent = userInfo.data.name;
            }
            // 加载知识库
            const kbStatus = document.getElementById('kb-status');
            if (kbStatus) {
              kbStatus.textContent = "正在加载知识库...";
              loadKnowledgeBase(userInfo.data.open_id, userInfo.data.access_token)
                .then(() => {
                  kbStatus.textContent = "知识库加载成功";
                  kbStatus.style.color = "green";
                })
                .catch(() => {
                  kbStatus.textContent = "知识库加载失败";
                  kbStatus.style.color = "red";
                });
            }
          } else {
            // token 失效，切换为未登录状态
            updateLoginStatus(false);
            const kbStatus = document.getElementById('kb-status');
            if (kbStatus) {
              kbStatus.textContent = "飞书授权已过期，请重新登录";
              kbStatus.style.color = "red";
            }
            localStorage.removeItem('feishu_user_info');
            sessionStorage.removeItem('feishu_user_info');
          }
        })
        .catch(e => {
          console.error('token 校验失败', e);
          updateLoginStatus(false);
        });
        return true;
      } else {
        console.warn('[tryAutoLoginFromStorage] userInfo.data 或 access_token 不存在:', userInfo);
        localStorage.removeItem('feishu_user_info');
        sessionStorage.removeItem('feishu_user_info');
      }
    } catch (e) {
      console.error('[tryAutoLoginFromStorage] 自动登录解析失败', e);
      localStorage.removeItem('feishu_user_info');
      sessionStorage.removeItem('feishu_user_info');
    }
  }
  return false;
}

// 在Office初始化时绑定登录按钮和全局postMessage监听
Office.onReady((info) => {
  if (info.host === Office.HostType.Word) {
    const loginButton = document.getElementById('feishu-login-btn');
    if (loginButton) {
      loginButton.addEventListener('click', openFeishuLogin);
    } else {
      console.error('未找到登录按钮 feishu-login-btn，无法绑定登录事件');
    }
    // 绑定手动刷新按钮事件
    const manualRefreshBtn = document.getElementById('manual-refresh-btn');
    if (manualRefreshBtn) {
      manualRefreshBtn.onclick = () => {
        if (!tryAutoLoginFromStorage()) {
          // Office 桌面端不支持 alert，改为页面内红色提示
          let tip = document.getElementById('manual-refresh-tip');
          if (!tip) {
            tip = document.createElement('div');
            tip.id = 'manual-refresh-tip';
            tip.style.color = 'white';
            tip.style.background = '#f5222d';
            tip.style.fontWeight = 'bold';
            tip.style.margin = '8px 0';
            tip.style.padding = '6px 12px';
            tip.style.borderRadius = '4px';
            tip.style.fontSize = '14px';
            tip.innerText = '未检测到本地授权信息，请先完成飞书授权。';
            manualRefreshBtn.parentNode?.insertBefore(tip, manualRefreshBtn.nextSibling);
          } else {
            tip.innerText = '未检测到本地授权信息，请先完成飞书授权。';
            tip.style.display = 'block';
          }
          setTimeout(() => {
            if (tip) tip.style.display = 'none';
          }, 4000);
        }
      };
    } else {
      console.error('未找到手动刷新按钮 manual-refresh-btn');
    }
    // 检查是否有待处理的授权流程
    checkAuthStatus();
    // 全局监听postMessage，支持弹窗登录
    window.addEventListener('message', async (event) => {
      // 允许 https 和 http 本地开发
      if (!event.origin.startsWith('http://localhost:3000') && !event.origin.startsWith('https://localhost:3000')) return;
      if (event.data && event.data.type === 'feishu-login-success') {
        console.log('收到飞书登录成功消息:', event.data.data);
        const userInfo = event.data.data;
        // 写入本地存储，确保 taskpane WebView2 可用
        try {
          localStorage.setItem('feishu_user_info', JSON.stringify(userInfo));
          console.log('[postMessage] 已写入本地存储:', userInfo);        } catch (e) {
          console.error('写入本地存储失败', e);
        }
        updateLoginStatus(true, userInfo);
        const userNameElement = document.getElementById('user-name');
        if (userNameElement && userInfo.data && userInfo.data.name) {
          userNameElement.textContent = userInfo.data.name;
        }
        // 加载知识库
        const kbStatus = document.getElementById('kb-status');
        if (kbStatus) {
          kbStatus.textContent = "正在加载知识库...";
          try {
            await loadKnowledgeBase(userInfo.data.open_id, userInfo.data.access_token);
            kbStatus.textContent = "知识库加载成功";
            kbStatus.style.color = "green";
          } catch (error) {
            console.error("知识库加载失败", error);
            kbStatus.textContent = "知识库加载失败";
            kbStatus.style.color = "red";
          }
        }
        // 可选：清理URL参数
        try {
          const newUrl = window.location.pathname;
          window.history.replaceState({}, '', newUrl);
        } catch {}
      }
    });
    // 启动时自动检查本地存储
    tryAutoLoginFromStorage();
  }
});

// 检查授权状态
function checkAuthStatus() {
  console.log("正在检查授权状态...");
  console.log("当前URL:", window.location.href);
  
  // 检查URL参数中是否有授权信息
  const urlParams = new URLSearchParams(window.location.search);
  const feishuAuth = urlParams.get('feishu_auth');
  const authData = urlParams.get('data');
  const errorMsg = urlParams.get('error_message');
  
  // 处理授权错误
  if (feishuAuth === 'error' && errorMsg) {
    console.error("检测到飞书授权错误:", decodeURIComponent(errorMsg));
    const kbStatus = document.getElementById('kb-status');
    if (kbStatus) {
      kbStatus.textContent = "授权失败: " + decodeURIComponent(errorMsg).substring(0, 50) + "...";
      kbStatus.style.color = "red";
    }
    
    // 清理URL参数，避免刷新页面时重复处理
    try {
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    } catch (navError) {
      console.error("清理URL参数失败", navError);
    }
    
    return;
  }
    if (feishuAuth === 'success' && authData) {
    console.log("检测到飞书授权成功参数");
    try {
      // 解码授权数据
      const decodedData = decodeURIComponent(authData);
      console.log("已解码的数据:", decodedData);
      
      let userInfo;
      try {
        userInfo = JSON.parse(decodedData);
        console.log("已解析的用户信息:", userInfo);
      } catch (jsonError) {
        console.error("JSON解析失败:", jsonError);
        console.log("尝试替代解析方法...");
        // 尝试其他解析方式
      }
      
      // 添加健壮性检查
      if (!userInfo) {
        console.error("用户信息解析失败");
        const kbStatus = document.getElementById('kb-status');
        if (kbStatus) {
          kbStatus.textContent = "授权数据解析失败，请重新登录";
          kbStatus.style.color = "red";
        }
        return;
      }
      
      // 确保userInfo.data存在，如果不存在，则创建一个空对象
      if (!userInfo.data) {
        console.warn("用户信息中缺少data字段，使用默认值");
        userInfo.data = {};
      }
      // 写入本地存储，确保手动刷新可用
      try {
        localStorage.setItem('feishu_user_info', JSON.stringify(userInfo));      } catch (e) {
        console.error('写入本地存储失败', e);
      }
      // 更新UI状态
      updateLoginStatus(true, userInfo);
      // 写入本地存储，确保后续可自动检测
      try {
        localStorage.setItem('feishu_user_info', JSON.stringify(userInfo));
      } catch (e) {
        console.error('写入本地存储失败', e);
      }
      // 显示用户信息
      const userNameElement = document.getElementById('user-name');
      if (userNameElement && userInfo.data && userInfo.data.name) {
        userNameElement.textContent = userInfo.data.name;
        console.log("已设置用户名:", userInfo.data.name);
      } else {
        console.log("用户名设置失败", userInfo?.data);
        // 设置默认名称
        if (userNameElement) userNameElement.textContent = "飞书用户";
      }
      
      // 加载知识库
      const kbStatus = document.getElementById('kb-status');
      if (kbStatus) {
        kbStatus.textContent = "正在加载知识库...";
        
        // 确保我们有 access_token，使用可选链和默认值
        const accessToken = userInfo.data?.access_token || '';
        const openId = userInfo.data?.open_id || '';
          if (!accessToken) {
          console.error("缺少access_token，无法加载知识库");
          kbStatus.textContent = "授权信息不完整，请重新登录";
          kbStatus.style.color = "red";
          return;
        }
        
        // 确保accessToken是字符串类型
        const safeAccessToken = String(accessToken);
        const tokenPreview = safeAccessToken.length > 5 ? safeAccessToken.substring(0, 5) + "..." : safeAccessToken;
        console.log("开始加载知识库，使用token:", tokenPreview);
        loadKnowledgeBase(openId || "unknown", safeAccessToken)
          .then((data) => {
            console.log("知识库加载成功:", data);
            kbStatus.textContent = "知识库加载成功";
            kbStatus.style.color = "green";
          })
          .catch((error) => {
            console.error("知识库加载失败", error);
            kbStatus.textContent = "知识库加载失败";
            kbStatus.style.color = "red";
          });
      }
      
      // 清理URL参数，避免刷新页面时重复处理
      try {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
        console.log("已清理URL参数");
      } catch (navError) {
        console.error("清理URL参数失败", navError);
      }
    } catch (error) {
      console.error("解析授权数据失败", error);
    }
  } else {
    console.log("未检测到飞书授权参数");
  }
  
  // 检查是否从授权页面返回（旧的逻辑，作为备用）
//   const authInProgress = sessionStorage.getItem('feishu_auth_in_progress');
//   if (authInProgress === 'true') {
//     console.log("检测到进行中的授权流程，清理状态");
//     sessionStorage.removeItem('feishu_auth_in_progress');
    
//     // 检查URL参数中的授权码
//     const code = urlParams.get('code');
//     if (code) {
//       console.log("检测到授权码:", code);
//       handleAuthCallback(code);
//     } else {
//       console.log("未检测到授权码");
//     }
//   }
}

// 处理授权回调
async function handleAuthCallback(code: string) {  try {
    // 模拟授权成功，更新UI
    updateLoginStatus(true, null);
    
    const kbStatus = document.getElementById('kb-status');
    if (kbStatus) {
      kbStatus.textContent = "正在加载知识库...";
      try {
        // 这里应该用实际的token，暂时用code代替
        await loadKnowledgeBase("temp_open_id", code);
        kbStatus.textContent = "知识库加载成功";
        kbStatus.style.color = "green";
      } catch (error) {
        console.error("知识库加载失败", error);
        kbStatus.textContent = "知识库加载失败";
        kbStatus.style.color = "red";
      }
    }
  } catch (error) {
    console.error("处理授权回调失败", error);
  }
}

// feishu_oauth_callback.html 逻辑（无需postMessage，只需写localStorage并跳转回taskpane.html）
// <script>
//   const urlParams = new URLSearchParams(window.location.search);
//   const data = urlParams.get('data');
//   if (data) {
//     try {
//       const userInfo = JSON.parse(decodeURIComponent(data));
//       localStorage.setItem('feishu_user_info', JSON.stringify(userInfo));
//       window.location.href = '/taskpane.html';
//     } catch (e) {
//       document.body.innerHTML = '授权信息解析失败，请重试。';
//     }
//   } else {
//     document.body.innerHTML = '未获取到授权信息，请重试。';
//   }
// </script>

// 更新知识库相关UI（下拉框、token数量等）
function updateKnowledgeBaseUI(knowledgeBases = [], models = []) {
  // 更新知识库下拉框
  const kbSelect = document.getElementById('knowledge-base-select');
  if (kbSelect) {
    kbSelect.innerHTML = '';
    knowledgeBases.forEach(kb => {
      const option = document.createElement('option');
      option.value = kb.id || kb.name;
      option.textContent = kb.name + (kb.tokenCount ? ` (Token数: ${kb.tokenCount})` : '');
      kbSelect.appendChild(option);
    });
  }
  // 更新token数量显示
  const kbTokenCount = document.getElementById('kb-token-count');
  if (kbTokenCount && knowledgeBases.length > 0) {
    kbTokenCount.textContent = `Token 数量: ${knowledgeBases[0].tokenCount || 0}`;
  }
  // 更新模型下拉框
  const modelSelect = document.getElementById('model-select');
  if (modelSelect) {
    modelSelect.innerHTML = '';
    models.forEach(model => {
      const option = document.createElement('option');
      option.value = model.id || model.name;
      option.textContent = model.name + (model.tokenCount ? ` (Token数: ${model.tokenCount})` : '');
      modelSelect.appendChild(option);
    });
  }
  // 更新模型token数量显示
  const modelTokenCount = document.getElementById('model-token-count');
  if (modelTokenCount && models.length > 0) {
    modelTokenCount.textContent = `Token 数量: ${models[0].tokenCount || 0}`;
  }
}

// 批量填充/光标填充前强制校验token
async function checkTokenBeforeFill() {
  let userInfoStr = localStorage.getItem('feishu_user_info') || sessionStorage.getItem('feishu_user_info');
  if (!userInfoStr) {
    alert('请先登录飞书并授权，才能使用自动填充功能。');
    return false;
  }
  try {
    const userInfo = JSON.parse(userInfoStr);
    if (!userInfo || !userInfo.data || !userInfo.data.access_token) {
      alert('授权信息缺失，请重新登录。');
      return false;
    }
    // 后端校验token有效性
    const res = await fetch('/api/feishu_login_status', {
      headers: { Authorization: `Bearer ${userInfo.data.access_token}` }
    });
    const data = await res.json();
    if (data.status !== 'success') {
      alert('飞书授权已过期，请重新登录。');
      updateLoginStatus(false);
      return false;
    }
    return true;
  } catch {
    alert('授权信息解析失败，请重新登录。');
    return false;
  }
}

// 在批量填充/光标填充按钮事件前调用 checkTokenBeforeFill
const cursorFillBtn = document.getElementById('cursor-fill-btn');
if (cursorFillBtn) {
  cursorFillBtn.onclick = async () => {
    if (await checkTokenBeforeFill()) {
      // ...原有光标填充逻辑...
    }
  };
}
const batchFillBtn = document.getElementById('batch-fill-btn');
if (batchFillBtn) {
  batchFillBtn.onclick = async () => {
    if (await checkTokenBeforeFill()) {
      // ...原有批量填充逻辑...
    }
  };
}