<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license. -->
<!-- See LICENSE in the project root for license information -->

<!doctype html>
<html lang="en" data-framework="typescript">

<head>
    <meta charset="UTF-8" />    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">    <title>Word Auto Fill</title>    <!-- Office JavaScript API -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
      <!-- webpack will inject the script tags here during build -->
</head>

<body style="width: 100%; height: 100%; margin: 0; padding: 0;">
    <div id="container"></div>    <style>
        /* Removed duplicate auth styles - using React components instead */
    </style>

    <!-- 
        Fluent UI React v. 9 uses modern JavaScript syntax that is not supported in
        Trident (Internet Explorer) or EdgeHTML (Edge Legacy), so this add-in won't
        work in Office versions that use these webviews. The script below makes the 
        following div display when an unsupported webview is in use, and hides the 
        React container div. 
    -->
    <div id="tridentmessage" style="display: none; padding: 10;">
        This add-in will not run in your version of Office. Please upgrade either to perpetual Office 2021 (or later) 
        or to a Microsoft 365 account.
    </div>        <script>
        if ((navigator.userAgent.indexOf("Trident") !== -1) || (navigator.userAgent.indexOf("Edge") !== -1)) {
            var tridentMessage = document.getElementById("tridentmessage");
            var normalContainer = document.getElementById("container");
            tridentMessage.style.display = "block";
            normalContainer.style.display = "none";
        }
    </script>
</body>

</html>
