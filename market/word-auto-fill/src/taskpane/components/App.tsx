import { makeStyles, Toaster } from "@fluentui/react-components";
import * as React from "react";
import { SWRConfig } from "swr";
import { globalToasterId } from "../../configs/toaster";
import { useOnError } from "../../utils/swr";
import Body from "./Body";
import Header from "./Header";

interface AppProps {
  title: string;
}

const useStyles = makeStyles({
  root: {
    minHeight: "100vh",
  },
});

const App: React.FC<AppProps> = (props: AppProps) => {
  const styles = useStyles();

  return (
    <div className={styles.root}>
      <Header title={props.title} />
      <Body />
    </div>
  );
};

export default (props: AppProps) => {
  const { onError } = useOnError();

  return (
    <SWRConfig value={{ onError, shouldRetryOnError: false }}>
      <App {...props} />
      <Toaster toasterId={globalToasterId} />
    </SWRConfig>
  );
};
