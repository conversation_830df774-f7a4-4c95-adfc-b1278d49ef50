import { Button, makeStyles, tokens } from "@fluentui/react-components";
import * as taskpane from "../taskpane";
import * as React from "react";
import { useTaskpaneFunc } from "../../utils/office";

export interface HeaderProps {
  title: string;
}

const useStyles = makeStyles({
  welcome__header: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: "30px 24px 0 24px",
  },
  title: {
    fontSize: tokens.fontSizeHero700,
    fontWeight: tokens.fontWeightRegular,
    fontColor: tokens.colorNeutralBackgroundStatic,
  },
});

const Header: React.FC<HeaderProps> = (props: HeaderProps) => {
  const { title } = props;
  const styles = useStyles();

  const getContextOfVariable = useTaskpaneFunc(taskpane.getContextOfVariable);

  const onClick = async () => {
    const context = await getContextOfVariable(["{公司法定中文名称}"]);

    console.log(context);
  };

  return (
    <section className={styles.welcome__header}>
      <h1 className={styles.title}>{title}</h1>
      {/* <Button onClick={onClick}>test</Button> */}
    </section>
  );
};

export default Header;
