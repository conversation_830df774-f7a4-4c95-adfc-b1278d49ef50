import {
  Badge,
  BadgeProps,
  Button,
  Dialog,
  DialogActions,
  DialogBody,
  DialogContent,
  DialogSurface,
  DialogTitle,
  List,
  ListItem,
  makeStyles,
  Text,
} from "@fluentui/react-components";
import * as React from "react";
import useSWRMutation from "swr/mutation";
import * as query from "../../../queries";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";
import { useOnError, useOnSuccess, voidKey } from "../../../utils/swr";
import { createPopup } from "../../../utils/zustand";
import * as taskpane from "../../taskpane";
import LoadingButton from "../LoadingButton";

export const fillContentDialog = createPopup();

let controller = new AbortController();

const onTerminate = () => {
  controller.abort();
  controller = new AbortController();
};

type VariableState = {
  status: "idle" | "pending" | "success" | "failed";
  count: number;
  answer?: string;
};

const statusTextMap: Record<VariableState["status"], string> = {
  idle: "未开始",
  pending: "进行中",
  success: "成功",
  failed: "未找到答案",
};

const statusColorMap: Record<VariableState["status"], BadgeProps["color"]> = {
  idle: "informative",
  pending: "warning",
  success: "success",
  failed: "danger",
};

const useStyles = makeStyles({
  stateTag: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    wordBreak: "keep-all",
  },
  listItem: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
  },
});

const FillContentDialog: React.FC = () => {
  const { open } = fillContentDialog.use();
  const { data: modelList, isLoading: modelListLoading } = query.modelList();
  const { onError } = useOnError();
  const { onSuccess } = useOnSuccess();
  const styles = useStyles();

  const [variableStates, _setVariableStates] = React.useState<Record<string, VariableState>>({});

  const setVariableStates = (states: typeof variableStates) => {
    const { open } = fillContentDialog.store.getState();
    if (!open) {
      _setVariableStates({});
      return;
    }

    _setVariableStates(states);
  };

  const init = useSWRMutation(voidKey, taskpane.getVariables, {
    onSuccess: (variables) => {
      const localVariableStates: typeof variableStates = {};
      for (const variable of variables) {
        localVariableStates[variable] = { status: "idle", count: 0 };
      }
      setVariableStates(localVariableStates);
    },
  });

  React.useEffect(() => {
    if (!open) return undefined;

    init.trigger();

    return () => {
      setVariableStates({});
    };
  }, [open]);

  const mutation = useSWRMutation(
    voidKey,
    async () => {
      const { knowledge, model } = globalStore.getState();
      const llmConfig = modelList?.[model];
      if (!llmConfig) {
        throw new Error("模型不存在");
      }

      const variables = await taskpane.getVariables();
      if (!variables.length) {
        throw new Error("没有找到变量");
      }

      const localVariableStates: typeof variableStates = {};

      for (const variable of variables) {
        localVariableStates[variable] = { status: "idle", count: 0 };
      }
      setVariableStates({ ...localVariableStates });

      let count = 0;
      while (count < 30) {
        const variableEntries = Object.entries(localVariableStates).filter(
          ([_, state]) => !["success", "failed"].includes(state.status)
        );
        if (!variableEntries.length) break;

        const variables = variableEntries.map(([variable]) => variable);
        const contextText = await taskpane.getContextOfVariable(variables);
        const requestedVariables = variables.filter((variable) => contextText.includes(variable));
        if (!requestedVariables.length) break;

        for (const variable of requestedVariables) {
          const state = localVariableStates[variable];
          if (!state) continue;

          state.count++;
          state.status = "pending";
        }
        setVariableStates({ ...localVariableStates });

        const answer = await taskpane.queryContext(
          contextText,
          knowledge,
          llmConfig,
          false,
          controller
        );

        for (const variable of requestedVariables) {
          const state = localVariableStates[variable];
          if (!state) continue;

          const key = variable.slice().replace("{", "").replace("}", "");
          const value = answer[key];

          if (!!value) {
            state.status = "success";
            state.answer = value;
            await taskpane.findAndReplace(variable, value);
            continue;
          }

          if (state.count <= 1) {
            state.status = "idle";
            continue;
          }

          state.status = "failed";
          await taskpane.findAndReplace(variable, "");
        }
        setVariableStates({ ...localVariableStates });

        count++;
      }

      return localVariableStates;
    },
    {
      onSuccess: () => onSuccess("填充内容完成"),
      onError: (e) => {
        onError(e);

        const { open } = fillContentDialog.store.getState();
        if (!open) return;

        _setVariableStates(
          produce((draft) => {
            for (const state of Object.values(draft)) {
              if (state.status === "pending") {
                state.status = "idle";
              }
            }
          })
        );
      },
    }
  );

  const onConfirm = async () => {
    await mutation.trigger();
  };

  const getVariableStateTag = (state?: VariableState) => {
    if (!state) {
      return (
        <div className={styles.stateTag}>
          <Text>未开始</Text>
          <Badge size="small" color="informative" />
        </div>
      );
    }

    const { status } = state;

    return (
      <div className={styles.stateTag}>
        <Text>{statusTextMap[status]}</Text>
        <Badge size="small" color={statusColorMap[status]} />
      </div>
    );
  };
  const listItems = React.useMemo(() => {
    return Object.entries(variableStates).map(([variable, state]) => (
      <ListItem key={variable} {...{className: styles.listItem} as any}>
        <Text>{variable}</Text>
        {getVariableStateTag(state)}
      </ListItem>
    ));
  }, [variableStates]);

  return (
    <Dialog open={open}>
      <DialogSurface>
        <DialogBody>
          <DialogTitle>请确认</DialogTitle>
          <DialogContent>
            <div style={{ marginBottom: 16 }}>将为文档填充内容</div>
            <List {...{style: { maxHeight: 300, overflow: "auto" }} as any}>
              <ListItem>
                <Text>
                  <strong>填充进度</strong>
                </Text>
              </ListItem>
              {listItems.length ? (
                listItems
              ) : (
                <ListItem>
                  <Text>没有变量需要填充</Text>
                </ListItem>
              )}
            </List>
          </DialogContent>

          <DialogActions>
            {mutation.isMutating ? (
              <Button onClick={onTerminate}>终止</Button>
            ) : (
              <Button onClick={() => fillContentDialog.hide()}>关闭</Button>
            )}

            <LoadingButton
              appearance="primary"
              onClick={onConfirm}
              loading={modelListLoading || mutation.isMutating}
            >
              开始
            </LoadingButton>
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};

export default FillContentDialog;
