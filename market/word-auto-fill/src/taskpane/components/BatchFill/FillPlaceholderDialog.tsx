import {
  Button,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogBody,
  DialogContent,
  DialogSurface,
  DialogTitle,
} from "@fluentui/react-components";
import * as React from "react";
import useSWRMutation from "swr/mutation";
import * as service from "../../../service";
import { useTaskpaneFunc } from "../../../utils/office";
import { useOnError, useOnSuccess, voidKey } from "../../../utils/swr";
import { createPopup } from "../../../utils/zustand";
import * as taskpane from "../../taskpane";
import LoadingButton from "../LoadingButton";

export const fillPlaceholderDialog = createPopup();

let controller = new AbortController();

const onTerminate = () => {
  controller.abort();
  controller = new AbortController();
};

const FillPlaceholderDialog: React.FC = () => {
  const { open } = fillPlaceholderDialog.use();

  const { onError } = useOnError();
  const { onSuccess } = useOnSuccess();

  const reloadDocument = useTaskpaneFunc(taskpane.reloadDocument);
  const mutation = useSWRMutation(
    voidKey,
    async () => {
      try {
        const filePath = await taskpane.getFilePath();
        console.log("正在调用 wordAnalyze API...", filePath);
        const result = await service.wordAnalyze(filePath, controller);
        console.log("API调用结果:", result);
        return result;
      } catch (error) {
        console.error("API调用失败:", error);
        // 转换网络错误为友好的错误对象而不是抛出异常
        return {
          success: false,
          error: "网络连接失败",
          message: "无法连接到服务器，请检查网络连接或服务器状态",
        };
      }
    },    {      onSuccess: async (response) => {
        // 使用类型断言确保TypeScript类型检查能通过
        type SuccessResponse = { success: true; file_path: string };
        type ErrorResponse = { success: false; error?: string; message?: string };
        
        // 先判断成功状态
        if (!response.success) {
          // 这是失败响应，使用类型断言
          const errorResponse = response as ErrorResponse;
          let errorMsg = '未知错误';
          
          if (errorResponse.message) {
            errorMsg = errorResponse.message;
          } else if (errorResponse.error) {
            errorMsg = errorResponse.error;
          }
          
          onError(`填充占位符失败: ${errorMsg}`);
          return;
        }
        
        // 这是成功响应，使用类型断言确保TypeScript知道response包含file_path
        const successResponse = response as SuccessResponse;
        onSuccess("填充占位符成功，将重新打开文档");
        fillPlaceholderDialog.hide();
        setTimeout(() => {
          reloadDocument(successResponse.file_path);
        }, 1000);
      },
    }
  );

  const onConfirm = async () => {
    await mutation.trigger();
  };

  return (
    <Dialog open={open}>
      <DialogSurface>
        <DialogBody>
          <DialogTitle>请确认</DialogTitle>
          <DialogContent>将为文档填充占位符，未保存的所有修改将丢失。</DialogContent>
          <DialogActions>
            {mutation.isMutating ? (
              <Button onClick={onTerminate}>终止</Button>
            ) : (
              <Button onClick={() => fillPlaceholderDialog.hide()}>关闭</Button>
            )}
            <LoadingButton appearance="primary" onClick={onConfirm} loading={mutation.isMutating}>
              开始
            </LoadingButton>
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};

export default FillPlaceholderDialog;
