import { Button, Divider, makeStyles } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
import { validatePrompt } from "../../../utils/prompt";
import FillContentDialog, { fillContentDialog } from "./FillContentDialog";
import FillPlaceholderDialog, { fillPlaceholderDialog } from "./FillPlaceholderDialog";
import PromptInput from "./PromptInput";

const useStyles = makeStyles({
  batchFill: {},
  blockButton: {
    width: "100%",
  },
});

const BatchFill: React.FC = () => {
  const styles = useStyles();
  const prompt = useStore(globalStore, (state) => state.batchFillPrompt);
  const { success } = validatePrompt(prompt);

  const onFillPlaceholder = () => {
    fillPlaceholderDialog.show();
  };

  const onFillContent = () => {
    fillContentDialog.show();
  };

  return (
    <div className={styles.batchFill}>
      <PromptInput />
      <Divider style={{ margin: "16px 0" }} />
      <p>功能：批量自动填充 Word 文档中的表格内容。</p>
      <p>请按照以下步骤操作：</p>
      <p>
        第一步、填充占位符。程序将自动为表格中的空白单元格填写占位符，以便下一步使用。占位符填写完毕后会再次打开文档。请勿对同一份文档多次填充占位符。
      </p>
      <Button appearance="primary" className={styles.blockButton} onClick={onFillPlaceholder}>
        填充占位符
      </Button>
      <p>第二步、填充内容。程序将自动识别第一步中填充的占位符，并使用提示词填充内容。</p>
      <Button
        appearance="primary"
        className={styles.blockButton}
        onClick={onFillContent}
        disabled={!success}
      >
        填充内容
      </Button>
    </div>
  );
};

export default () => (
  <React.Fragment>
    <BatchFill />
    <FillContentDialog />
    <FillPlaceholderDialog />
  </React.Fragment>
);
