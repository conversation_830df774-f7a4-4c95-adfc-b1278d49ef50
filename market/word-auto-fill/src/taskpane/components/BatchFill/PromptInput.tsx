import { Field, Link, makeStyles, Textarea, TextareaProps } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import { batchFillPromptTemplate } from "../../../configs/promptTemplate";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";
import { validatePrompt } from "../../../utils/prompt";

export interface PromptInputProps {}

const useStyles = makeStyles({
  field: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
});

const PromptInput: React.FC<PromptInputProps> = (_: PromptInputProps) => {
  const prompt = useStore(globalStore, (state) => state.batchFillPrompt);
  const styles = useStyles();

  const { success, message } = validatePrompt(prompt);

  const onChange: TextareaProps["onChange"] = (_, data) => {
    globalStore.setState(produce((draft) => (draft.batchFillPrompt = data.value)));
  };

  const onReset = () => {
    globalStore.setState(produce((draft) => (draft.batchFillPrompt = batchFillPromptTemplate)));
  };

  return (
    <Field
      label={
        <div className={styles.field}>
          <span>提示词</span>
          <Link onClick={onReset}>重置</Link>
        </div>
      }
      validationState={success ? "success" : "error"}
      validationMessage={message}
    >
      <Textarea value={prompt} onChange={onChange} resize="vertical" rows={10} />
    </Field>
  );
};

export default PromptInput;
