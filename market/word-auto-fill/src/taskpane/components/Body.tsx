import {
  Accordion,
  Accordion<PERSON>eader,
  AccordionItem,
  AccordionPanel,
  AccordionProps,
  Badge,
  Button,
  makeStyles,
} from "@fluentui/react-components";
import {
  DocumentBulletListFilled,
  PersonRegular,
  SettingsFilled,
  SlideTextCursorFilled,
} from "@fluentui/react-icons";
import * as React from "react";
import { useStore } from "zustand";
import { globalStore } from "../../store";
import { produce } from "../../utils/immer";
import { useOnError } from "../../utils/swr";
import { KnowledgeBaseList } from "../../components/KnowledgeBaseList";
import { FEISHU_AUTH_ENDPOINT } from "../../config";
import BatchFill from "./BatchFill";
import CursorFill from "./CursorFill";
import Settings from "./Settings";

export interface BodyProps {}

const useStyles = makeStyles({
  body: {
    padding: "0 24px",
  },
  settingsPanel: {
    paddingBottom: "16px",
  },
  badge: {
    marginLeft: "8px",
  },
  loginContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    padding: "24px 0",
    gap: "16px",
  },
  userInfo: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
});

type PanelType = "settings" | "cursorFill" | "contextFill";

const Body: React.FC<BodyProps> = (_: BodyProps) => {
  const styles = useStyles();
  const { panel, knowledge, model, user } = useStore(globalStore);

  const { onError } = useOnError();

  let count = 0;
  if (!knowledge.length) count++;
  if (!model) count++;

  const handleLogin = () => {
    window.location.href = FEISHU_AUTH_ENDPOINT;
  };  const onToggle = (_, data: { value: string | string[] }) => {
    if (!!count && data.value !== "settings") {
      onError(new Error("请先完成设置"));
      return;
    }

    // Allow accessing knowledge bases without requiring Feishu login
    // Only certain operations might still require login, but basic functionality should work

    globalStore.setState(
      produce((state) => {
        // 修复：正确处理 Accordion 的 openItems 状态
        state.panel = Array.isArray(data.value) 
          ? data.value as PanelType[] 
          : [data.value as PanelType]; // 修复：单个值时包装为数组
      })
    );
  };

  return (
    <div className={styles.body}>
      {!user ? (
        <div className={styles.loginContainer}>
          <PersonRegular fontSize={48} />
          <Button appearance="primary" onClick={handleLogin}>
            飞书登录
          </Button>
        </div>
      ) : (
        <div className={styles.userInfo}>
          <img
            src={user.avatar}
            style={{ width: 32, height: 32, borderRadius: "50%" }}
          />
          <span>{user.name}</span>
        </div>
      )}      <Accordion
        openItems={panel}
        onToggle={onToggle as any}
        multiple
      >
        <AccordionItem value="settings">
          <AccordionHeader {...{icon: <SettingsFilled />} as any}>
            设置
            {count > 0 && (
              <Badge
                color="danger"
                className={styles.badge as any}
                size="small"
                shape="rounded"
              >
                {count}
              </Badge>
            )}
          </AccordionHeader>
          <AccordionPanel {...{className: styles.settingsPanel} as any}>
            <Settings />
          </AccordionPanel>
        </AccordionItem>

        <AccordionItem value="cursorFill">
          <AccordionHeader {...{icon: <SlideTextCursorFilled />} as any}>
            光标填充
          </AccordionHeader>
          <AccordionPanel>
            <CursorFill />
          </AccordionPanel>
        </AccordionItem>

        <AccordionItem value="contextFill">
          <AccordionHeader {...{icon: <DocumentBulletListFilled />} as any}>
            批量填充
          </AccordionHeader>
          <AccordionPanel>
            <BatchFill />
          </AccordionPanel>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default Body;
