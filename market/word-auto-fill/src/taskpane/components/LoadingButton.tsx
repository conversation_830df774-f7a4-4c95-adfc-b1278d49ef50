import { Button, ButtonProps, Spinner } from "@fluentui/react-components";
import * as React from "react";

type Props = ButtonProps & {
  loading?: boolean;
};

const LoadingButton = ({ loading, children, ...props }: Props) => {
  return (
    <Button disabled={loading} {...props} icon={loading ? <Spinner size="tiny" /> : undefined}>
      {loading ? '加载中' : children}
    </Button>
  );
};

export default LoadingButton;
