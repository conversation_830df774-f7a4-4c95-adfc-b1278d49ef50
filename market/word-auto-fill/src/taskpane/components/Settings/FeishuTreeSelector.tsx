// filepath: FeishuTreeSelector.tsx
import * as React from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogBody,
  DialogContent,
  DialogSurface,
  DialogTitle,
  Input,
  Spinner,
  makeStyles,
  tokens,
  Checkbox,
} from "@fluentui/react-components";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";

// 引入 FluentUI 组件
import {
  ProgressIndicator,
  Label,
  SearchBox,
  Icon
} from "@fluentui/react";

// 引入自定义树组件
import {
  Tree,
  TreeItem,
  TreeItemLayout,
} from "./TreeComponents";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "500px",
    width: "100%",
  },
  treeContainer: {
    height: "400px",
    overflowY: "auto",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    padding: "8px",
    marginTop: "10px",
    borderRadius: "4px",
  },
  searchContainer: {
    marginBottom: "10px",
    display: "flex",
    gap: "8px",
  },
  tokenInput: {
    marginBottom: "10px",
  },
  selectedFilesContainer: {
    marginTop: "10px",
    padding: "8px",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    borderRadius: "4px",
    maxHeight: "100px",
    overflowY: "auto",
  },
  selectedFile: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "4px",
    margin: "2px 0",
    borderRadius: "4px",
    "&:hover": {
      backgroundColor: tokens.colorNeutralBackground1Hover,
    },
  },
  fileName: {
    flexGrow: 1,
  },
  removeButton: {
    minWidth: "unset",
    height: "24px",
    padding: "0 8px",
  },
  loadingContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
  },
  statusMessage: {
    marginTop: "10px",
    padding: "8px",
    borderRadius: "4px",
  },
  success: {
    backgroundColor: tokens.colorStatusSuccessBackground1,
    color: tokens.colorStatusSuccessForeground1,
  },
  error: {
    backgroundColor: tokens.colorStatusDangerBackground1,
    color: tokens.colorStatusDangerForeground1,
  },
});

// 文件节点类型定义
interface FileNode {
  id: string;
  name: string;
  type: string;
  children?: FileNode[];
  parent_id?: string;
}

// 配置相关类型
interface FeishuConfig {
  user_access_token?: string;
  folder_token?: string;
}

// 尝试从配置文件读取飞书配置
const tryGetFeishuConfig = (): FeishuConfig => {
  try {
    // 尝试从localStorage中获取配置信息
    const configStr = localStorage.getItem('feishu_config');
    if (configStr) {
      return JSON.parse(configStr);
    }
    
    // 如果没有从localStorage获取到，尝试从API获取
    fetch('/api/config/feishu')
      .then(response => {
        if (response.ok) {
          return response.json();
        }
        throw new Error('获取飞书配置失败');
      })
      .then(config => {
        if (config && config.user_access_token) {
          // 保存到localStorage以便后续使用
          localStorage.setItem('feishu_config', JSON.stringify(config));
          return config;
        }
      })
      .catch(error => {
        console.error('获取飞书配置出错:', error);
      });
  } catch (error) {
    console.error('解析飞书配置出错:', error);
  }
  
  return {};
};

// 飞书树状选择器组件
export const FeishuTreeSelector: React.FC<{
  onSelect: (selectedFiles: Array<{id: string, name: string, type: string}>) => void;
}> = ({ onSelect }) => {
  const styles = useStyles();
  const [isOpen, setIsOpen] = React.useState(false);
  const [userToken, setUserToken] = React.useState("");
  const [fileTree, setFileTree] = React.useState<FileNode[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState("");  const [selectedFiles, setSelectedFiles] = React.useState<Map<string, {id: string, name: string, type: string}>>(new Map());
  const [expandedItems, setExpandedItems] = React.useState<Set<string>>(new Set());
  const [statusMessage, setStatusMessage] = React.useState<{type: "success" | "error" | "info", message: string} | null>(null);
  const [kbName, setKbName] = React.useState("feishu_kb");
  
  // 从全局状态获取用户信息
  const user = useStore(globalStore, (state) => state.user);
  
  // 从本地存储获取飞书用户信息
  const getFeishuAccessToken = React.useCallback(async () => {
    // 1. 优先从API获取
    try {
      const resp = await fetch('/api/config/feishu', { cache: 'no-store' });
      if (resp.ok) {
        const config = await resp.json();
        if (config && config.user_access_token) {
          localStorage.setItem('feishu_config', JSON.stringify(config));
          return config.user_access_token;
        }
      }
    } catch (e) {
      // 忽略，继续fallback
    }
    // 2. 再从localStorage/sessionStorage
    try {
      const configStr = localStorage.getItem('feishu_config');
      if (configStr) {
        const config = JSON.parse(configStr);
        if (config && config.user_access_token) {
          return config.user_access_token;
        }
      }
      const userInfoStr = localStorage.getItem('feishu_user_info') || sessionStorage.getItem('feishu_user_info');
      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        if (userInfo && userInfo.data && userInfo.data.access_token) {
          return userInfo.data.access_token;
        }
      }
    } catch (error) {
      console.error('获取飞书访问令牌失败:', error);
    }
    return '';
  }, [user]);
  // 加载文件树
  const loadFileTree = async (tokenToUse?: string, retryCount = 0) => {
    // 最大重试次数
    const MAX_RETRIES = 5; // 增加重试次数到5次
    
    // 确定使用哪个令牌
    let token = tokenToUse || userToken.trim();
    if (!token) {
      token = await getFeishuAccessToken();
    }
    if (!token) {
      setStatusMessage({ type: "error", message: "请先登录飞书账号以获取授权" });
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    if (retryCount === 0) {
      setStatusMessage({ type: "info", message: "正在加载飞书文件，请稍候..." });
    } else {
      setStatusMessage({ type: "info", message: `连接超时，正在进行第 ${retryCount + 1}/${MAX_RETRIES} 次尝试...` });
    }try {
      // 设置超时处理 - 增加到60秒
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时
      
      console.log("正在使用自动动获取的令牌加载文件树", token ? token.substring(0, 5) + "..." : "无令牌");
      console.log("[FeishuTreeSelector] Using token for file tree:", token ? token.substring(0, 6) + '*** (len=' + token.length + ')' : '无令牌');
        // 使用带有重试和更可靠的请求
      const fetchWithTimeout = async (url: string, options: RequestInit, timeoutMs: number) => {
        const controller = new AbortController();
        const { signal } = controller;
        const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
        
        try {
          const response = await fetch(url, { ...options, signal });
          clearTimeout(timeoutId);
          return response;
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      };
      
      const response = await fetchWithTimeout(
        `/api/feishu/tree?user_access_token=${encodeURIComponent(token)}`, 
        {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        },
        60000 // 60秒超时
      );
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        // 处理特定状态码
        if (response.status === 504 && retryCount < MAX_RETRIES) {
          console.log(`加载文件树超时，正在进行第 ${retryCount + 1} 次重试...`);
          // 使用指数退避算法增加等待时间
          const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 10000); // 最长10秒
          
          setStatusMessage({ 
            type: "info", 
            message: `连接超时，${backoffTime/1000}秒后自动重试 (${retryCount + 1}/${MAX_RETRIES})` 
          });
          
          // 等待一段时间后重试
          setTimeout(() => {
            loadFileTree(token, retryCount + 1);
          }, backoffTime);
          return;
        }
        
        // 根据不同状态码提供不同的错误消息
        let errorMessage = `API响应错误: ${response.status}`;
        if (response.status === 504) {
          errorMessage = "连接飞书服务超时，请检查网络连接或稍后再试";
        } else if (response.status === 401) {
          errorMessage = "飞书授权已过期，请重新登录";
        } else if (response.status === 403) {
          errorMessage = "没有足够的权限访问飞书文件，请确认登录账号";
        } else if (response.status >= 500) {
          errorMessage = "飞书服务器错误，请稍后再试";
        }
        
        throw new Error(errorMessage);
      }
      
      const data = await response.json();
      console.log('飞书API返回数据:', data);
      if (data.tree) {
        // 如果是使用自动获取的令牌，将令牌设置到状态中
        if (!userToken.trim() && !tokenToUse) {
          setUserToken(token);
        }
        setFileTree(data.tree || []); // 防止 tree 为 null
        console.log('setFileTree:', data.tree);
        // 默认展开第一层
        const firstLevel = new Set<string>();
        (data.tree || []).forEach((node: FileNode) => {
          if (node.type === "folder") {
            firstLevel.add(node.id);
          }
        });
        setExpandedItems(firstLevel);
      } else {
        setFileTree([]);
        console.log('setFileTree: [] (无数据)');
        throw new Error("未返回有效的文件树数据");
      }
    } catch (error) {
      console.error("加载文件树出错:", error);
      setFileTree([]); // 异常时也清空 fileTree，避免 UI 卡死
      
      // 检查是否是 AbortError（超时）
      if (error.name === 'AbortError' && retryCount < MAX_RETRIES) {
        console.log(`加载文件树超时，正在进行第 ${retryCount + 1} 次重试...`);
        // 使用指数退避算法增加等待时间
        const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 10000); // 最长10秒
        
        setStatusMessage({ 
          type: "info", 
          message: `请求超时，${backoffTime/1000}秒后自动重试 (${retryCount + 1}/${MAX_RETRIES})` 
        });
        
        // 等待一段时间后重试
        setTimeout(() => {
          loadFileTree(token, retryCount + 1);
        }, backoffTime);
        return;
      }
      
      // 网络错误特殊处理
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        setStatusMessage({ 
          type: "error", 
          message: "网络连接异常，请检查您的网络连接后重试" 
        });
      } else {
        setStatusMessage({ 
          type: "error", 
          message: `加载文件树失败: ${error instanceof Error ? error.message : String(error)}` 
        });
      }    } finally {
      setIsLoading(false);
    }
  };

  // 处理节点展开/折叠
  const handleExpandCollapse = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  // 处理文件选择
  const handleFileSelect = (file: FileNode) => {
    setSelectedFiles(prev => {
      const newMap = new Map(prev);
      if (newMap.has(file.id)) {
        newMap.delete(file.id);
      } else {
        newMap.set(file.id, { id: file.id, name: file.name, type: file.type });
      }
      return newMap;
    });
  };

  // 递归渲染文件树
  const renderTree = (nodes: FileNode[]) => {
    if (!nodes || nodes.length === 0) return null;

    return nodes.map(node => {
      const isFolder = node.type === "folder";
      const isExpanded = expandedItems.has(node.id);
      const isSelected = selectedFiles.has(node.id);
      
      // 搜索过滤
      if (searchTerm && !node.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        // 如果是文件夹，我们需要检查子项是否匹配
        if (isFolder && node.children) {
          const filteredChildren = node.children.filter(child => 
            child.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
          if (filteredChildren.length === 0) {
            return null;
          }
        } else {
          return null;
        }
      }

      // 使用文本图标代替Fluent UI图标
      const folderIcon = isExpanded ? "📂" : "📁";
      const docIcon = "📄";
      const sheetIcon = "📊";
      const pdfIcon = "📑";

      return (
        <TreeItem 
          key={node.id}
          itemType={isFolder ? "branch" : "leaf"}
          expanded={isExpanded}
          onClick={() => {
            if (isFolder) {
              handleExpandCollapse(node.id);
            } else {
              handleFileSelect(node);
            }
          }}
        >
          <TreeItemLayout 
            icon={<span style={{ marginRight: '5px' }}>
              {isFolder ? folderIcon : 
               node.type === "sheet" ? sheetIcon :
               node.type === "pdf" ? pdfIcon : docIcon}
            </span>}
            expandIcon={isFolder ? 
              <span>{isExpanded ? "▼" : "▶"}</span> : 
              undefined}
            endMedia={!isFolder && (
              <Checkbox 
                checked={isSelected}
                onChange={() => handleFileSelect(node)}
              />
            )}
          >
            {node.name || "(无标题)"}
          </TreeItemLayout>
          {isFolder && node.children && isExpanded && renderTree(node.children)}
        </TreeItem>
      );
    }).filter(Boolean);
  };

  // 确认选择文件
  const confirmSelection = () => {
    if (selectedFiles.size === 0) {
      setStatusMessage({ type: "error", message: "请至少选择一个文件" });
      return;
    }
    
    onSelect(Array.from(selectedFiles.values()));
    setIsOpen(false);
  };

  // 创建知识库
  const createKnowledgeBase = async () => {
    console.log('【调试】点击了创建知识库', Array.from(selectedFiles.values()), kbName);
    if (selectedFiles.size === 0) {
      setStatusMessage({ type: "error", message: "请至少选择一个文件" });
      return;
    }

    setIsLoading(true);
    setStatusMessage({ type: "success", message: "正在创建知识库..." });

    // 获取令牌，优先使用输入框中的，然后是全局/存储中的
    let token = userToken;
    if (!token) {
      token = await getFeishuAccessToken();
    }
    
    if (!token) {
      setStatusMessage({ type: "error", message: "没有可用的飞书访问令牌，请登录飞书或手动输入令牌" });
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/feishu/build_kb', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          file_tokens: Array.from(selectedFiles.keys()),
          user_access_token: token,
          kb_name: kbName,
          file_info_list: Array.from(selectedFiles.values()) // 新增，带上文件名和id
        })
      });

      if (!response.ok) {
        throw new Error(`API响应错误: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setStatusMessage({ 
          type: "success", 
          message: `知识库创建成功! 名称: ${data.kb_name}, 文件数: ${data.successful_files}/${data.total_files}` 
        });
        
        // 通知父组件刷新知识库列表
        onSelect(Array.from(selectedFiles.values()));
        
        // 2秒后关闭对话框
        setTimeout(() => setIsOpen(false), 2000);
      } else {
        throw new Error(data.error || "创建知识库失败");
      }
    } catch (error) {
      console.error("创建知识库出错:", error);
      setStatusMessage({ 
        type: "error", 
        message: `创建知识库失败: ${error instanceof Error ? error.message : String(error)}` 
      });
    } finally {
      setIsLoading(false);
    }
  };  
  
  // 对话框打开时，先预热API连接再加载文件树
  React.useEffect(() => {
    if (isOpen && !fileTree.length && !isLoading) {
      setStatusMessage({ type: 'info', message: '正在连接飞书服务...' });
      fetch('/api/feishu/ping', {
        method: 'GET',
        headers: { 'Cache-Control': 'no-cache' }
      })
        .then(() => console.log('API连接预热成功'))
        .catch(() => console.log('API预热请求失败，将继续尝试加载'))
        .finally(async () => {
          // 无论预热成功或失败，都尝试加载文件树
          const token = await getFeishuAccessToken();
          if (token) {
            // 自动使用获取的令牌加载文件树
            console.log('正在使用自动获取的令牌加载文件树');
            setTimeout(() => {
              loadFileTree(token);
            }, 500);
          } else {
            loadFileTree();
            console.log('无可用令牌，请确保用户已登录飞书');
          }
        });
    }
  }, [isOpen, fileTree.length, isLoading, getFeishuAccessToken]);

  // 组件挂载时预加载令牌并预初始化
  React.useEffect(() => {
    (async () => {
      const token = await getFeishuAccessToken();
      if (token) {
        setUserToken(token);
        console.log('已自动获取飞书访问令牌');
        fetch('/api/feishu/ping', {
          method: 'GET',
          headers: { 'Cache-Control': 'no-cache' }
        }).catch(() => {
          console.log('API预热请求已发送');
        });
      }
    })();
  }, [getFeishuAccessToken]);

  return (
    <>
      <Button appearance="primary" onClick={() => setIsOpen(true)}>选择飞书文件</Button>
      <Dialog open={isOpen} onOpenChange={() => {
        setIsOpen(false);
        setIsLoading(false);
        setStatusMessage(null);
        setFileTree([]);
        setSelectedFiles(new Map());
      }}>
        <DialogSurface>
          <DialogTitle>飞书知识库文件选择器</DialogTitle>
          <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            <DialogContent>
              {isLoading ? (
                <div style={{ textAlign: "center", padding: "20px" }}>
                  <Spinner size="large" label="正在加载飞书文件..." />
                  {statusMessage && (
                    <div style={{
                      marginTop: "15px",
                      padding: "10px",
                      backgroundColor: statusMessage.type === "error" ? "#FDE7E9" :
                        statusMessage.type === "info" ? "#EFF6FC" : "#DFF6DD",
                      borderRadius: "4px"
                    }}>
                      {statusMessage.message}
                    </div>
                  )}
                </div>
              ) : fileTree.length === 0 ? (
                <div style={{ textAlign: "center", padding: "20px" }}>
                  <div style={{ color: '#888', marginBottom: 12 }}>未获取到飞书文件树</div>
                  {statusMessage && (
                    <div style={{
                      marginTop: "15px",
                      padding: "10px",
                      backgroundColor: statusMessage.type === "error" ? "#FDE7E9" :
                        statusMessage.type === "info" ? "#EFF6FC" : "#DFF6DD",
                      borderRadius: "4px"
                    }}>
                      {statusMessage.message}
                    </div>
                  )}
                  <Button appearance="primary" onClick={() => loadFileTree()}>
                    重新加载文件树
                  </Button>
                </div>
              ) : (
                <>
                  <div className={styles.searchContainer}>
                    <Input
                      value={searchTerm}
                      onChange={(_, data) => setSearchTerm(data.value)}
                      placeholder="搜索文件..."
                      style={{ flexGrow: 1 }}
                    />
                    <Button onClick={() => setSearchTerm("")} disabled={!searchTerm}>清除</Button>
                  </div>
                  <div className={styles.treeContainer}>
                    <Tree aria-label="飞书文件树">
                      {renderTree(fileTree)}
                    </Tree>
                  </div>
                  <div className={styles.selectedFilesContainer}>
                    <h4>已选择 {selectedFiles.size} 个文件</h4>
                    {Array.from(selectedFiles.values()).map(file => (
                      <div key={file.id} className={styles.selectedFile}>
                        <span className={styles.fileName}>{file.name || "(无标题)"} ({file.type})</span>
                        <Button
                          className={styles.removeButton}
                          size="small"
                          appearance="subtle"
                          onClick={() => {
                            setSelectedFiles(prev => {
                              const newMap = new Map(prev);
                              newMap.delete(file.id);
                              return newMap;
                            });
                          }}
                        >
                          移除
                        </Button>
                      </div>
                    ))}
                  </div>
                  <div style={{ marginTop: "10px" }}>
                    <div style={{ marginBottom: "5px" }}>知识库名称:</div>
                    <Input
                      value={kbName}
                      onChange={(_, data) => setKbName(data.value)}
                      placeholder="知识库名称"
                    />
                  </div>
                  {statusMessage && (
                    <div className={`${styles.statusMessage} ${statusMessage.type === 'success' ? styles.success : styles.error}`}>
                      {statusMessage.message}
                    </div>
                  )}
                </>
              )}
            </DialogContent>
          </div>
          <DialogActions>
            <Button appearance="secondary" onClick={() => {
              setIsOpen(false);
              setIsLoading(false);
              setStatusMessage(null);
              setFileTree([]);
              setSelectedFiles(new Map());
            }}>取消</Button>
            {/* 优化：始终渲染按钮，只是 disabled 状态 */}
            {fileTree.length > 0 && (
              <>
                <Button appearance="primary" onClick={confirmSelection} disabled={selectedFiles.size === 0 || isLoading}>
                  确认选择
                </Button>
                <Button appearance="primary" onClick={() => { console.log('【调试】创建知识库按钮被点击'); createKnowledgeBase(); }} disabled={selectedFiles.size === 0 || isLoading}>
                  创建知识库
                </Button>
              </>
            )}
            {fileTree.length === 0 && !isLoading && (
              <Button appearance="primary" onClick={() => loadFileTree()}>
                重新加载文件树
              </Button>
            )}
          </DialogActions>
        </DialogSurface>
      </Dialog>
    </>
  );
};
