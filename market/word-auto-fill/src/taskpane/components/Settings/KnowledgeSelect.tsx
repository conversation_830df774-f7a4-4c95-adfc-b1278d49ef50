// filepath: KnowledgeSelect.tsx
import {
  Avatar,
  Field,
  makeStyles,
  Tag,
  TagPicker,
  TagPickerButton,
  TagPickerControl,
  TagPickerGroup,
  TagPickerList,
  TagPickerOption,
  TagPickerProps,
  tokens,
  Button,
  Divider,
} from "@fluentui/react-components";
import { sumBy } from "lodash";
import * as React from "react";
import { useStore } from "zustand";
import * as query from "../../../queries";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";
import AutoFeishuTreeSelector from "./AutoFeishuTreeSelector";

const useStyles = makeStyles({
  secondaryContent: {
    color: tokens.colorNeutralStroke1,
    fontStyle: "italic",
  },
});

export interface ModelSelectProps {}

const ModelSelect: React.FC<ModelSelectProps> = (_: ModelSelectProps) => {
  const styles = useStyles();

  const { data, isLoading, mutate } = query.knowledgeList();

  console.log("知识库下拉数据", data);

  const knowledge = useStore(globalStore, (state) => state.knowledge);

  const selected = React.useMemo(() => {
    const selected: React.JSX.Element[] = [];
    for (const item of knowledge) {
      const config = data?.[item] as any;
      if (!config) continue;

      selected.push(
        <Tag
          key={item}
          value={item}
          shape="rounded"
          media={<Avatar name={config.type} color="colorful" />}
        >
          {item}
        </Tag>
      );
    }

    return selected;
  }, [knowledge, data]);

  const tokenSize = React.useMemo(
    () => sumBy(knowledge, (it) => data?.[it]?.token_size || 0),
    [knowledge, data]
  );

  const options = React.useMemo(() => {
    const options: React.JSX.Element[] = [];

    for (const [id, config] of Object.entries(data || {})) {
      if (knowledge.includes(id)) continue;

      const c = config as any;
      options.push(
        <TagPickerOption
          key={id}
          value={id}
          secondaryContent={
            <div>
              <div>Token 数量：{c.token_size?.toLocaleString?.()}</div>
              <div className={styles.secondaryContent}>{c.path}</div>
            </div>
          }
          media={<Avatar name={c.type} color="colorful" />}
        >
          {id}
        </TagPickerOption>
      );
    }

    if (!options.length) {
      options.push(
        <TagPickerOption key="__empty__" value="">
          没有更多了
        </TagPickerOption>
      );
    }

    return options;
  }, [knowledge, data]);

  const onOptionSelect: TagPickerProps["onOptionSelect"] = (_, data) => {
    if (!data.value) return;

    globalStore.setState(
      produce((draft) => {
        if (draft.knowledge.includes(data.value)) {
          draft.knowledge = draft.knowledge.filter((it) => it !== data.value);
        } else {
          draft.knowledge.push(data.value);
        }
      })
    );
  };  
  
  // 处理从飞书树状选择器中选择的文件
  const [feishuSyncing, setFeishuSyncing] = React.useState(false);
  const [feishuSyncError, setFeishuSyncError] = React.useState("");

  const handleFeishuFilesSelected = React.useCallback(
    (selectedFiles: Array<{ id: string; name: string; type: string }>) => {
      setFeishuSyncing(true);
      setFeishuSyncError("");
      console.log("[Feishu] 选中文件:", selectedFiles);
      mutate && mutate();

      // 最多等2秒，超时给出提示
      let timeoutId = setTimeout(() => {
        setFeishuSyncing(false);
        setFeishuSyncError("飞书文件同步超时，请重试");
        console.log("[Feishu] 同步超时，data:", data, "knowledge:", globalStore.getState().knowledge);
      }, 2000);

      // 轮询检测 data 是否已包含所有新知识库，最多轮询10次
      let pollCount = 0;
      const poll = () => {
        const kbData = data || {};
        // 检查所有所选文件是否都已注册为知识库
        const allSynced = selectedFiles.every((file) => {
          const kbId = file.id;
          const kbConfig = kbData[kbId];
          console.log(`[Feishu] 检查kbId=${kbId}, kbConfig=`, kbConfig);
          return (
            kbConfig && (kbConfig.type === "feishu" || kbConfig.type === "feishu_tree")
          );
        });
        if (allSynced) {
          // 只保留本次所选的知识库id
          globalStore.setState(
            produce((draft) => {
              draft.knowledge = selectedFiles.map(f => f.id);
            })
          );
          setFeishuSyncing(false);
          clearTimeout(timeoutId);
          console.log("[Feishu] 已同步所有知识库:", selectedFiles.map(f => f.id));
        } else if (pollCount > 10) {
          setFeishuSyncing(false);
          clearTimeout(timeoutId);
          setFeishuSyncError("飞书文件同步失败，请重试");
        } else {
          pollCount++;
          setTimeout(poll, 200);
        }
      };
      setTimeout(poll, 300);
    },
    [mutate, data]
  );

  // 至少选一个知识库或飞书文件
  const hasFeishuKb = React.useMemo(() => {
    // 判断 knowledge 里是否有 type 为 feishu/feishu_tree 的知识库
    if (!data) return false;
    return knowledge.some(id => {
      const config = data[id];
      return config && (config.type === 'feishu' || config.type === 'feishu_tree');
    });
  }, [knowledge, data]);

  const canSubmit = knowledge.length > 0 || hasFeishuKb;

  return (
    <>
      <Field
        label="知识库"
        validationState={canSubmit ? "none" : "error"}
        validationMessage={
          feishuSyncing
            ? "正在同步飞书文件，请稍候..."
            : feishuSyncError
            ? feishuSyncError
            : canSubmit
            ? `Token 数量：${tokenSize.toLocaleString()}`
            : "请至少选择一个知识库或飞书文件"
        }
      >
        <TagPicker selectedOptions={knowledge} onOptionSelect={onOptionSelect}>
          <TagPickerControl>
            <TagPickerGroup>{selected}</TagPickerGroup>
            <TagPickerButton />
          </TagPickerControl>
          <TagPickerList>{options}</TagPickerList>
        </TagPicker>
      </Field>
      <Divider style={{ margin: "12px 0" }} />
      <div style={{ display: "flex", justifyContent: "center", margin: "8px 0" }}>
        <AutoFeishuTreeSelector onSelect={handleFeishuFilesSelected} />
      </div>
      {/* 你可以在此处加一个确认按钮，disabled={!canSubmit} */}
    </>
  );
};

export default ModelSelect;
