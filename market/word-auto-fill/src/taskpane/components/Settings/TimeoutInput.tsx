import { Field, Input, InputProps } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";

export interface TimeoutInputProps {}

const TimeoutInput: React.FC<TimeoutInputProps> = (_: TimeoutInputProps) => {
  const timeout = useStore(globalStore, (state) => state.timeout);
  const onChange = (_, data: { value: string }) => {
    const { value } = data;
    if (!value) {
      globalStore.setState(produce((draft) => (draft.timeout = 0)));
      return;
    }

    if (!/^[0-9]*$/.test(value)) {
      return;
    }

    globalStore.setState(produce((draft) => (draft.timeout = parseInt(value))));
  };
  return (
    <Field label="超时时间（秒）" validationState="none" validationMessage="设为 0 表示不超时">
      <Input value={timeout.toString()} onChange={onChange as any} type="number" />
    </Field>
  );
};

export default TimeoutInput;
