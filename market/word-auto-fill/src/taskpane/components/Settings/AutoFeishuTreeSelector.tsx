// 自动获取飞书令牌的树状选择器包装组件
import * as React from "react";
import { FeishuTreeSelector } from "./FeishuTreeSelector";
import { getFeishuToken } from "../../utils/feishuTokenHelper";

/**
 * 飞书树状选择器的自动令牌包装组件
 * 自动从本地存储或配置中获取飞书访问令牌，并注入到原组件中
 */
const AutoFeishuTreeSelector: React.FC<{
  onSelect: (selectedFiles: Array<{id: string, name: string, type: string}>) => void;
}> = (props) => {
  const { onSelect } = props;
  const [tokenAvailable, setTokenAvailable] = React.useState(false);
  const [isInitialized, setIsInitialized] = React.useState(false);
  
  // 组件挂载时自动检查是否有可用令牌并预热连接
  React.useEffect(() => {
    // 获取令牌并保存到localStorage中
    const token = getFeishuToken();
    if (token) {
      setTokenAvailable(true);
      
      // 模拟用户登录，将令牌保存到期望的格式中
      const fakeUserInfo = {
        data: {
          access_token: token
        }
      };
      localStorage.setItem('feishu_user_info', JSON.stringify(fakeUserInfo));
      console.log("自动令牌注入成功", token.substring(0, 5) + "...");
      
      // 预热连接以减少超时概率
      try {
        fetch('/api/feishu/ping', { 
          method: 'GET',
          headers: { 'Cache-Control': 'no-cache' }
        }).then(() => {
          console.log("飞书API连接预热成功");
          setIsInitialized(true);
        }).catch(() => {
          console.log("飞书API连接预热失败，将在实际请求时重试");
          setIsInitialized(true);
        });
      } catch (e) {
        console.log("无法预热API连接");
        setIsInitialized(true);
      }
    } else {
      console.log("无法获取飞书令牌，将依赖用户手动输入");
      setIsInitialized(true);
    }
  }, []);
  
  // 在组件未初始化时显示加载状态
  if (!isInitialized) {
    return <div style={{ textAlign: 'center', padding: '20px' }}>
      <div>初始化飞书连接...</div>
    </div>;
  }
  
  return <FeishuTreeSelector onSelect={onSelect} />;
};

export default AutoFeishuTreeSelector;
