import { Field, Slider, SliderProps } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";

export interface TemperatureInputProps {}

const TemperatureInput: React.FC<TemperatureInputProps> = (_: TemperatureInputProps) => {
  const temperature = useStore(globalStore, (state) => state.temperature);

  const onChange: SliderProps["onChange"] = (_, data) => {
    globalStore.setState(produce((draft) => (draft.temperature = data.value)));
  };

  return (
    <Field label="温度" validationState="none" validationMessage={temperature}>
      <Slider min={0} max={1} step={0.1} value={temperature} onChange={onChange} />
    </Field>
  );
};

export default TemperatureInput;
