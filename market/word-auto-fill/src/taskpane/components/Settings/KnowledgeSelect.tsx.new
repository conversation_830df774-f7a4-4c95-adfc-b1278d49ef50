// filepath: KnowledgeSelect.tsx
import {
  Avatar,
  Field,
  makeStyles,
  Tag,
  TagPicker,
  TagPickerButton,
  TagPickerControl,
  TagPickerGroup,
  TagPickerList,
  TagPickerOption,
  TagPickerProps,
  tokens,
  Button,
  Divider,
} from "@fluentui/react-components";
import { sumBy } from "lodash";
import * as React from "react";
import { useStore } from "zustand";
import * as query from "../../../queries";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";
import { FeishuTreeSelector } from "./FeishuTreeSelector";

const useStyles = makeStyles({
  secondaryContent: {
    color: tokens.colorNeutralStroke1,
    fontStyle: "italic",
  },
});

export interface ModelSelectProps {}

const ModelSelect: React.FC<ModelSelectProps> = (_: ModelSelectProps) => {
  const styles = useStyles();

  const { data, isLoading, mutate } = query.knowledgeList();

  console.log("知识库下拉数据", data);

  const knowledge = useStore(globalStore, (state) => state.knowledge);

  const selected = React.useMemo(() => {
    const selected: React.JSX.Element[] = [];
    for (const item of knowledge) {
      const config = data?.[item] as any;
      if (!config) continue;

      selected.push(
        <Tag
          key={item}
          value={item}
          shape="rounded"
          media={<Avatar name={config.type} color="colorful" />}
        >
          {item}
        </Tag>
      );
    }

    return selected;
  }, [knowledge, data]);

  const tokenSize = React.useMemo(
    () => sumBy(knowledge, (it) => data?.[it]?.token_size || 0),
    [knowledge, data]
  );

  const options = React.useMemo(() => {
    const options: React.JSX.Element[] = [];

    for (const [id, config] of Object.entries(data || {})) {
      if (knowledge.includes(id)) continue;

      const c = config as any;
      options.push(
        <TagPickerOption
          key={id}
          value={id}
          secondaryContent={
            <div>
              <div>Token 数量：{c.token_size?.toLocaleString?.()}</div>
              <div className={styles.secondaryContent}>{c.path}</div>
            </div>
          }
          media={<Avatar name={c.type} color="colorful" />}
        >
          {id}
        </TagPickerOption>
      );
    }

    if (!options.length) {
      options.push(
        <TagPickerOption key="__empty__" value="">
          没有更多了
        </TagPickerOption>
      );
    }

    return options;
  }, [knowledge, data]);

  const onOptionSelect: TagPickerProps["onOptionSelect"] = (_, data) => {
    if (!data.value) return;

    globalStore.setState(
      produce((draft) => {
        if (draft.knowledge.includes(data.value)) {
          draft.knowledge = draft.knowledge.filter((it) => it !== data.value);
        } else {
          draft.knowledge.push(data.value);
        }
      })
    );
  };  
  
  // 处理从飞书树状选择器中选择的文件
  const handleFeishuFilesSelected = React.useCallback((selectedFiles: Array<{id: string, name: string, type: string}>) => {
    // 这里我们只需要通知知识库列表刷新，新创建的知识库会出现在列表中
    // 使用mutate方法重新获取数据
    mutate && mutate();
    console.log("已选择飞书文件:", selectedFiles);
  }, [mutate]);

  return (
    <>
      <Field
        label="知识库"
        validationState={!!knowledge.length ? "none" : "error"}
        validationMessage={
          !!knowledge.length ? `Token 数量：${tokenSize.toLocaleString()}` : "请至少选择一个知识库"
        }
      >
        <TagPicker selectedOptions={knowledge} onOptionSelect={onOptionSelect}>
          <TagPickerControl>
            <TagPickerGroup>{selected}</TagPickerGroup>
            <TagPickerButton />
          </TagPickerControl>
          <TagPickerList>{options}</TagPickerList>
        </TagPicker>
      </Field>
      
      <Divider style={{ margin: "12px 0" }} />
      
      <div style={{ display: "flex", justifyContent: "center", margin: "8px 0" }}>
        <FeishuTreeSelector onSelect={handleFeishuFilesSelected} />
      </div>
    </>
  );
};

export default ModelSelect;
