import { makeStyles } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
// import AnalyzeModeInput from "./AnalyzeModeInput";
import KnowledgeSelect from "./KnowledgeSelect";
import ModelSelect from "./ModelSelect";
import ReasoningToggle from "./ReasoningToggle";
import TemperatureInput from "./TemperatureInput";
import TimeoutInput from "./TimeoutInput";

const useStyles = makeStyles({
  settings: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
  },
  modelSelect: {
    display: "flex",
    gap: "16px",
  },
});

export interface SettingsProps {}

const Settings: React.FC<SettingsProps> = (_: SettingsProps) => {
  const styles = useStyles();
  const model = useStore(globalStore, (state) => state.model);

  return (
    <div className={styles.settings}>
      <div className={styles.modelSelect}>
        <div style={{ flex: 1 }}>
          <ModelSelect />
        </div>
        {model.toLowerCase().includes("qwen3") && <ReasoningToggle />}
      </div>
      <KnowledgeSelect />
      <TimeoutInput />
      <TemperatureInput />
      {/* <AnalyzeModeInput /> */}
    </div>
  );
};

export default Settings;
