// 简单实现树状组件，兼容 Fluent UI 树组件的基本接口
import * as React from "react";
import { makeStyles, tokens } from "@fluentui/react-components";

const useStyles = makeStyles({
  tree: {
    listStyle: "none",
    padding: 0,
    margin: 0,
  },
  treeItem: {
    padding: "4px 0",
    cursor: "pointer",
  },
  treeItemLayout: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
  },
  childrenContainer: {
    paddingLeft: "20px",
    borderLeft: `1px dashed ${tokens.colorNeutralStroke2}`,
    marginLeft: "10px",
  },
});

export interface TreeProps {
  "aria-label"?: string;
  children?: React.ReactNode;
}

export const Tree: React.FC<TreeProps> = (props) => {
  const styles = useStyles();
  return (
    <ul className={styles.tree} role="tree" aria-label={props["aria-label"]}>
      {props.children}
    </ul>
  );
};

export interface TreeItemProps {
  key?: string;
  itemType?: "branch" | "leaf";
  expanded?: boolean;
  onClick?: () => void;
  children?: React.ReactNode;
}

export const TreeItem: React.FC<TreeItemProps> = (props) => {
  const styles = useStyles();
  const { itemType, expanded, onClick, children } = props;
  
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick && onClick();
  };

  // 分离 TreeItemLayout 和其他子组件
  let layout: React.ReactNode = null;
  let childItems: React.ReactNode = null;

  React.Children.forEach(children, child => {
    if (React.isValidElement(child) && child.type === TreeItemLayout) {
      layout = child;
    } else if (itemType === "branch" && expanded) {
      childItems = childItems ? [childItems, child] : child;
    }
  });

  return (
    <li className={styles.treeItem} role="treeitem" aria-expanded={expanded} onClick={handleClick}>
      {layout}
      {childItems && (
        <div className={styles.childrenContainer}>
          {childItems}
        </div>
      )}
    </li>
  );
};

export interface TreeItemLayoutProps {
  icon?: React.ReactNode;
  expandIcon?: React.ReactNode;
  endMedia?: React.ReactNode;
  children?: React.ReactNode;
}

export const TreeItemLayout: React.FC<TreeItemLayoutProps> = (props) => {
  const styles = useStyles();
  const { icon, expandIcon, endMedia, children } = props;
  
  return (
    <div className={styles.treeItemLayout}>
      {expandIcon && <span>{expandIcon}</span>}
      {icon && <span>{icon}</span>}
      <span style={{ flexGrow: 1 }}>{children}</span>
      {endMedia && <span onClick={e => e.stopPropagation()}>{endMedia}</span>}
    </div>
  );
};
