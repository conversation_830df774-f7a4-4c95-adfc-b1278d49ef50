import { Checkbox, CheckboxProps, Field } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";

export interface ReasoningToggleProps {}

const ReasoningToggle: React.FC<ReasoningToggleProps> = (_: ReasoningToggleProps) => {
  const reasoning = useStore(globalStore, (state) => state.reasoning);

  const onChange: CheckboxProps["onChange"] = (_, data) => {
    globalStore.setState(produce((draft) => (draft.reasoning = data.checked === true)));
  };

  return (
    <Field label="思考模式" validationState="none" validationMessage="仅对 Qwen3 模型生效">
      <Checkbox checked={reasoning} onChange={onChange} />
    </Field>
  );
};

export default ReasoningToggle;
