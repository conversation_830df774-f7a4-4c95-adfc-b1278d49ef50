// filepath: FeishuTreeSelector.tsx
import * as React from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogBody,
  DialogContent,
  DialogSurface,
  DialogTitle,
  Input,
  Spinner,
  makeStyles,
  tokens,
  Checkbox,
} from "@fluentui/react-components";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";

// 引入 FluentUI 组件
import {
  ProgressIndicator,
  Label,
  SearchBox,
  Icon
} from "@fluentui/react";

// 引入自定义树组件
import {
  Tree,
  TreeItem,
  TreeItemLayout,
} from "./TreeComponents";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "500px",
    width: "100%",
  },
  treeContainer: {
    height: "400px",
    overflowY: "auto",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    padding: "8px",
    marginTop: "10px",
    borderRadius: "4px",
  },
  searchContainer: {
    marginBottom: "10px",
    display: "flex",
    gap: "8px",
  },
  tokenInput: {
    marginBottom: "10px",
  },
  selectedFilesContainer: {
    marginTop: "10px",
    padding: "8px",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    borderRadius: "4px",
    maxHeight: "100px",
    overflowY: "auto",
  },
  selectedFile: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "4px",
    margin: "2px 0",
    borderRadius: "4px",
    "&:hover": {
      backgroundColor: tokens.colorNeutralBackground1Hover,
    },
  },
  fileName: {
    flexGrow: 1,
  },
  removeButton: {
    minWidth: "unset",
    height: "24px",
    padding: "0 8px",
  },
  loadingContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
  },
  statusMessage: {
    marginTop: "10px",
    padding: "8px",
    borderRadius: "4px",
  },
  success: {
    backgroundColor: tokens.colorStatusSuccessBackground1,
    color: tokens.colorStatusSuccessForeground1,
  },
  error: {
    backgroundColor: tokens.colorStatusDangerBackground1,
    color: tokens.colorStatusDangerForeground1,
  },
});

// 文件节点类型定义
interface FileNode {
  id: string;
  name: string;
  type: string;
  children?: FileNode[];
  parent_id?: string;
}

// 飞书树状选择器组件
export const FeishuTreeSelector: React.FC<{
  onSelect: (selectedFiles: Array<{id: string, name: string, type: string}>) => void;
}> = ({ onSelect }) => {
  const styles = useStyles();
  const [isOpen, setIsOpen] = React.useState(false);
  const [userToken, setUserToken] = React.useState("");
  const [fileTree, setFileTree] = React.useState<FileNode[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState("");
  const [selectedFiles, setSelectedFiles] = React.useState<Map<string, {id: string, name: string, type: string}>>(new Map());
  const [expandedItems, setExpandedItems] = React.useState<Set<string>>(new Set());
  const [statusMessage, setStatusMessage] = React.useState<{type: "success" | "error", message: string} | null>(null);
  const [kbName, setKbName] = React.useState("feishu_kb");
  
  // 从全局状态获取用户信息
  const user = useStore(globalStore, (state) => state.user);
  
  // 从本地存储获取飞书用户信息
  const getFeishuAccessToken = React.useCallback(() => {
    // 优先从全局状态获取
    if (user?.access_token) {
      return user.access_token;
    }
    
    // 从本地存储获取
    try {
      const userInfoStr = localStorage.getItem('feishu_user_info') || sessionStorage.getItem('feishu_user_info');
      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        if (userInfo && userInfo.data && userInfo.data.access_token) {
          return userInfo.data.access_token;
        }
      }
    } catch (error) {
      console.error("获取飞书访问令牌失败:", error);
    }
    
    return "";
  }, [user]);

  // 加载文件树
  const loadFileTree = async (tokenToUse?: string) => {
    // 确定使用哪个令牌
    const token = tokenToUse || userToken.trim() || getFeishuAccessToken();
    
    if (!token) {
      setStatusMessage({ type: "error", message: "没有可用的飞书访问令牌，请登录飞书或手动输入令牌" });
      return;
    }

    setIsLoading(true);
    setStatusMessage(null);

    try {
      const response = await fetch(`/api/feishu/tree?user_access_token=${encodeURIComponent(token)}`);
      
      if (!response.ok) {
        throw new Error(`API响应错误: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.tree) {
        // 如果是使用自动获取的令牌，将令牌设置到状态中
        if (!userToken.trim() && !tokenToUse) {
          setUserToken(token);
        }
        
        setFileTree(data.tree);
        // 默认展开第一层
        const firstLevel = new Set<string>();
        data.tree.forEach((node: FileNode) => {
          if (node.type === "folder") {
            firstLevel.add(node.id);
          }
        });
        setExpandedItems(firstLevel);
      } else {
        throw new Error("未返回有效的文件树数据");
      }
    } catch (error) {
      console.error("加载文件树出错:", error);
      setStatusMessage({ 
        type: "error", 
        message: `加载文件树失败: ${error instanceof Error ? error.message : String(error)}` 
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理节点展开/折叠
  const handleExpandCollapse = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  // 处理文件选择
  const handleFileSelect = (file: FileNode) => {
    setSelectedFiles(prev => {
      const newMap = new Map(prev);
      if (newMap.has(file.id)) {
        newMap.delete(file.id);
      } else {
        newMap.set(file.id, { id: file.id, name: file.name, type: file.type });
      }
      return newMap;
    });
  };

  // 递归渲染文件树
  const renderTree = (nodes: FileNode[]) => {
    if (!nodes || nodes.length === 0) return null;

    return nodes.map(node => {
      const isFolder = node.type === "folder";
      const isExpanded = expandedItems.has(node.id);
      const isSelected = selectedFiles.has(node.id);
      
      // 搜索过滤
      if (searchTerm && !node.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        // 如果是文件夹，我们需要检查子项是否匹配
        if (isFolder && node.children) {
          const filteredChildren = node.children.filter(child => 
            child.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
          if (filteredChildren.length === 0) {
            return null;
          }
        } else {
          return null;
        }
      }

      // 使用文本图标代替Fluent UI图标
      const folderIcon = isExpanded ? "📂" : "📁";
      const docIcon = "📄";
      const sheetIcon = "📊";
      const pdfIcon = "📑";

      return (
        <TreeItem 
          key={node.id}
          itemType={isFolder ? "branch" : "leaf"}
          expanded={isExpanded}
          onClick={() => {
            if (isFolder) {
              handleExpandCollapse(node.id);
            } else {
              handleFileSelect(node);
            }
          }}
        >
          <TreeItemLayout 
            icon={<span style={{ marginRight: '5px' }}>
              {isFolder ? folderIcon : 
               node.type === "sheet" ? sheetIcon :
               node.type === "pdf" ? pdfIcon : docIcon}
            </span>}
            expandIcon={isFolder ? 
              <span>{isExpanded ? "▼" : "▶"}</span> : 
              undefined}
            endMedia={!isFolder && (
              <Checkbox 
                checked={isSelected}
                onChange={() => handleFileSelect(node)}
              />
            )}
          >
            {node.name || "(无标题)"}
          </TreeItemLayout>
          {isFolder && node.children && isExpanded && renderTree(node.children)}
        </TreeItem>
      );
    }).filter(Boolean);
  };

  // 确认选择文件
  const confirmSelection = () => {
    if (selectedFiles.size === 0) {
      setStatusMessage({ type: "error", message: "请至少选择一个文件" });
      return;
    }
    
    onSelect(Array.from(selectedFiles.values()));
    setIsOpen(false);
  };

  // 创建知识库
  const createKnowledgeBase = async () => {
    if (selectedFiles.size === 0) {
      setStatusMessage({ type: "error", message: "请至少选择一个文件" });
      return;
    }

    setIsLoading(true);
    setStatusMessage({ type: "success", message: "正在创建知识库..." });

    // 获取令牌，优先使用输入框中的，然后是全局/存储中的
    const token = userToken || getFeishuAccessToken();
    
    if (!token) {
      setStatusMessage({ type: "error", message: "没有可用的飞书访问令牌，请登录飞书或手动输入令牌" });
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/feishu/build_kb', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          file_tokens: Array.from(selectedFiles.keys()),
          user_access_token: token,
          kb_name: kbName
        })
      });

      if (!response.ok) {
        throw new Error(`API响应错误: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setStatusMessage({ 
          type: "success", 
          message: `知识库创建成功! 名称: ${data.kb_name}, 文件数: ${data.successful_files}/${data.total_files}` 
        });
        
        // 通知父组件刷新知识库列表
        onSelect(Array.from(selectedFiles.values()));
        
        // 2秒后关闭对话框
        setTimeout(() => setIsOpen(false), 2000);
      } else {
        throw new Error(data.error || "创建知识库失败");
      }
    } catch (error) {
      console.error("创建知识库出错:", error);
      setStatusMessage({ 
        type: "error", 
        message: `创建知识库失败: ${error instanceof Error ? error.message : String(error)}` 
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 对话框打开时，自动尝试加载文件树
  React.useEffect(() => {
    if (isOpen && !fileTree.length && !isLoading) {
      const token = getFeishuAccessToken();
      if (token) {
        loadFileTree(token);
      }
    }
  }, [isOpen, fileTree.length, isLoading]);

  return (
    <>
      <Button appearance="primary" onClick={() => setIsOpen(true)}>选择飞书文件</Button>
      
      <Dialog open={isOpen} onOpenChange={() => setIsOpen(false)}>
        <DialogSurface>
          <DialogTitle>飞书知识库文件选择器</DialogTitle>
          <DialogContent>
            {!fileTree.length ? (
              <div>
                <p>请输入飞书访问令牌以加载文件树</p>
                <Input 
                  className={styles.tokenInput}
                  value={userToken} 
                  onChange={(e, data) => setUserToken(data.value)} 
                  placeholder="飞书访问令牌..." 
                />
                <Button onClick={() => loadFileTree()} disabled={isLoading}>
                  {isLoading ? <Spinner size="tiny" /> : "加载文件树"}
                </Button>
              </div>
            ) : (
              <>
                <div className={styles.searchContainer}>
                  <Input 
                    value={searchTerm} 
                    onChange={(e, data) => setSearchTerm(data.value)} 
                    placeholder="搜索文件..." 
                    style={{ flexGrow: 1 }}
                  />
                  <Button onClick={() => setSearchTerm("")} disabled={!searchTerm}>清除</Button>
                </div>
                
                {isLoading ? (
                  <div className={styles.loadingContainer}>
                    <Spinner label="加载中..." />
                  </div>
                ) : (
                  <div className={styles.treeContainer}>
                    <Tree aria-label="飞书文件树">
                      {renderTree(fileTree)}
                    </Tree>
                  </div>
                )}

                <div className={styles.selectedFilesContainer}>
                  <h4>已选择 {selectedFiles.size} 个文件</h4>
                  {Array.from(selectedFiles.values()).map(file => (
                    <div key={file.id} className={styles.selectedFile}>
                      <span className={styles.fileName}>{file.name || "(无标题)"} ({file.type})</span>
                      <Button 
                        className={styles.removeButton}
                        size="small"
                        appearance="subtle"
                        onClick={() => {
                          setSelectedFiles(prev => {
                            const newMap = new Map(prev);
                            newMap.delete(file.id);
                            return newMap;
                          });
                        }}
                      >
                        移除
                      </Button>
                    </div>
                  ))}
                </div>

                <div style={{ marginTop: "10px" }}>
                  <div style={{ marginBottom: "5px" }}>知识库名称:</div>
                  <Input
                    value={kbName}
                    onChange={(e, data) => setKbName(data.value)}
                    placeholder="知识库名称"
                  />
                </div>

                {statusMessage && (
                  <div className={`${styles.statusMessage} ${statusMessage.type === 'success' ? styles.success : styles.error}`}>
                    {statusMessage.message}
                  </div>
                )}
                
                {isLoading && (
                  <ProgressIndicator 
                    label="正在处理..."
                    styles={{ root: { marginTop: "10px" } }}
                  />
                )}
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button appearance="secondary" onClick={() => setIsOpen(false)}>取消</Button>
            {fileTree.length > 0 && (
              <>
                <Button appearance="primary" onClick={confirmSelection} disabled={selectedFiles.size === 0 || isLoading}>
                  确认选择
                </Button>
                <Button appearance="primary" onClick={createKnowledgeBase} disabled={selectedFiles.size === 0 || isLoading}>
                  创建知识库
                </Button>
              </>
            )}
          </DialogActions>
        </DialogSurface>
      </Dialog>
    </>
  );
};
