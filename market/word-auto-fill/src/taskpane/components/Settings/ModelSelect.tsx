import { Field, Select, SelectProps } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import * as query from "../../../queries";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";

export interface ModelSelectProps {}

const ModelSelect: React.FC<ModelSelectProps> = (_: ModelSelectProps) => {
  const { data, isLoading } = query.modelList();

  const model = useStore(globalStore, (state) => state.model);

  const maxTokens = data?.[model]?.max_tokens || 0;
  console.log("模型数据：", data);
  console.log("当前选中模型id：", model);

  const options = React.useMemo(() => {
    const options: React.JSX.Element[] = [
      <option key="__empty__" value="">
        请选择
      </option>,
    ];

    // data 是对象，遍历其 key
    if (data && typeof data === "object") {
      for (const id of Object.keys(data)) {
        const config = data[id];
        if (config && config.model) {
          options.push(
            <option key={id} value={id}>
              {config.model}
            </option>
          );
        }
      }
    }

    return options;
  }, [data]);

  const onChange: SelectProps["onChange"] = (_, data) => {
    globalStore.setState(produce((draft) => (draft.model = data.value)));
  };

  return (
    <Field
      label="模型"
      validationState={model ? "none" : "error"}
      validationMessage={model ? `Token 数量：${maxTokens.toLocaleString()}` : "请选择模型"}
    >
      <Select value={model} onChange={onChange}>
        {options}
      </Select>
    </Field>
  );
};

export default ModelSelect;
