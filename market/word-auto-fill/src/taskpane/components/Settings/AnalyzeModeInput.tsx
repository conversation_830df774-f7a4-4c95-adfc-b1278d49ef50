import { Field, Select, SelectProps } from "@fluentui/react-components";
import * as React from "react";
import { useStore } from "zustand";
import { globalStore } from "../../../store";
import { produce } from "../../../utils/immer";

export interface AnalyzeModeInputProps {}

const AnalyzeModeInput: React.FC<AnalyzeModeInputProps> = (_: AnalyzeModeInputProps) => {
  const analyzeMode = useStore(globalStore, (state) => state.analyzeMode);

  const onChange: SelectProps["onChange"] = (_, data) => {
    globalStore.setState(
      produce((draft) => (draft.analyzeMode = data.value as typeof draft.analyzeMode))
    );
  };

  return (
    <Field label="填充占位符分析模式">
      <Select value={analyzeMode} onChange={onChange}>
        <option value="basic">基础</option>
        <option value="balanced">平衡</option>
        <option value="complete">完整</option>
      </Select>
    </Field>
  );
};

export default AnalyzeModeInput;
