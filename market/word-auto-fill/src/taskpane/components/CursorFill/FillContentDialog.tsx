import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogBody,
  DialogContent,
  DialogSurface,
  DialogTitle,
  Spinner,
} from "@fluentui/react-components";
import * as React from "react";
import { createPopup } from "../../../utils/zustand";

export const fillContentDialog = createPopup();

interface FillContentDialogProps {
  onTerminate: () => void;
}

const FillContentDialog: React.FC<FillContentDialogProps> = (props) => {
  const { open } = fillContentDialog.use();

  const onTerminate = () => {
    props.onTerminate();
    fillContentDialog.hide();
  };

  return (
    <Dialog open={open}>
      <DialogSurface>
        <DialogBody>
          <DialogTitle>填充中</DialogTitle>
          <DialogContent>
            <Spinner />
          </DialogContent>
          <DialogActions>
            <Button onClick={onTerminate}>终止</Button>
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};

export default FillContentDialog;
