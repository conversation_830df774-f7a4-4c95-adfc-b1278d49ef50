import { But<PERSON>, Divider, makeStyles } from "@fluentui/react-components";
import * as React from "react";
import useSWRMutation from "swr/mutation";
import * as query from "../../../queries";
import { globalStore } from "../../../store";
import { useOnError, useOnSuccess, voidKey } from "../../../utils/swr";
import { marker } from "../../../utils/word";
import * as taskpane from "../../taskpane";
import PromptInput from "./PromptInput";
import LoadingButton from "../LoadingButton";
import FillContentDialog, { fillContentDialog } from "./FillContentDialog";
import { validatePrompt } from "../../../utils/prompt";
import { useStore } from "zustand";

let controller = new AbortController();

const onTerminate = () => {
  controller.abort();
  controller = new AbortController();
};

const useStyles = makeStyles({
  cursorFill: {},
  blockButton: {
    width: "100%",
  },
});

const CursorFill: React.FC = () => {
  const { data: modelList, isLoading: modelListLoading } = query.modelList();
  const { onError } = useOnError();
  const { onSuccess } = useOnSuccess();
  const styles = useStyles();

  const prompt = useStore(globalStore, (state) => state.cursorFillPrompt);
  const { success } = validatePrompt(prompt);

  const mutation = useSWRMutation(
    voidKey,
    async () => {
      const { knowledge, model } = globalStore.getState();
      const llmConfig = modelList?.[model];
      if (!llmConfig) {
        throw new Error("模型不存在");
      }
      try {
        const answer = await taskpane.queryContextUnderCursor(
          knowledge,
          llmConfig,
          false,
          controller
        );
        const [value] = Object.values(answer);
        await taskpane.findAndReplace(marker, value || "");

        return value;
      } catch (e) {
        await taskpane.findAndReplace(marker, "");
        throw e;
      }
    },
    {
      onSuccess: async (value) => {
        fillContentDialog.hide();
        if (!value) {
          onError("未找到答案");
        } else {
          onSuccess("填充内容完成");
        }
      },
      onError: (e) => {
        fillContentDialog.hide();
        onError(e);
      },
    }
  );

  const onFillContent = async () => {
    fillContentDialog.show();
    await mutation.trigger();
  };

  return (
    <div className={styles.cursorFill}>
      <PromptInput />
      <Divider style={{ margin: "16px 0" }} />
      <p>功能：在光标处自动填充内容。</p>
      <p>请点击文档中需要填写的位置，然后点击下面的按钮。</p>
      <LoadingButton
        appearance="primary"
        className={styles.blockButton}
        onClick={onFillContent}
        disabled={!success}
        loading={modelListLoading || mutation.isMutating}
      >
        填充内容
      </LoadingButton>
      {mutation.isMutating && (
        <Button onClick={onTerminate} className={styles.blockButton} style={{ marginTop: 12 }}>
          终止
        </Button>
      )}
    </div>
  );
};

export default () => (
  <React.Fragment>
    <CursorFill />
    <FillContentDialog onTerminate={onTerminate} />
  </React.Fragment>
);
