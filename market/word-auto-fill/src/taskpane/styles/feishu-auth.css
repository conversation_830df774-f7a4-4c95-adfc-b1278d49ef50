/* taskpane styles */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.status-text {
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  margin-top: 8px;
}

.status-text.loading {
  background: #e6f7ff;
  color: #1890ff;
}

.status-text.success {
  background: #f6ffed;
  color: #52c41a;
}

.status-text.error {
  background: #fff2f0;
  color: #ff4d4f;
}

#feishu-login-btn,
#feishu-logout-btn {
  width: 100%;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

#feishu-login-btn {
  background: #00b42a;
  color: white;
}

#feishu-login-btn:hover {
  background: #06a11e;
}

#feishu-logout-btn {
  background: #ff4d4f;
  color: white;
}

#feishu-logout-btn:hover {
  background: #ff1f1f;
}
