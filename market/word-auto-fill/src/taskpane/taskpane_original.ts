/* global Word console */

import z from "zod";
import { modelSchema } from "../models";
import * as service from "../service";
import { getBatchFillPrompt, getCursorFillPrompt } from "../utils/model";
import { escape } from "../utils/regexp";
import { marker, removeAttributes } from "../utils/word";
import { FEISHU_APP_ID, FEISHU_REDIRECT_URI, FEISHU_AUTH_ENDPOINT } from "../config/feishu";
import { KnowledgeBaseService } from "../services/knowledge-base";
import { FeishuAuthManager } from "../services/feishu-auth";

const withMarker = async <T>(
  context: Word.RequestContext,
  func: (marker: string, selection: Word.Range) => Promise<T> | T
) => {
  const selection = context.document.getSelection();
  context.load(selection, "paragraphs");
  await context.sync();

  selection.insertText(marker, Word.InsertLocation.replace);
  await context.sync();

  return await func(marker, selection);
};

export const getContextOfVariable = async (variables: string[]) => {
  if (!variables.length) return "";

  const [variable] = variables;
  return await Word.run(async (context) => {
    const body = context.document.body;
    const html = body.getHtml();
    await context.sync();

    const parser = new DOMParser();
    const doc = parser.parseFromString(html.value, "text/html");

    for (const attr of removeAttributes) {
      doc.querySelectorAll(`[${attr}]`).forEach((el) => el.removeAttribute(attr));
    }

    doc.querySelectorAll("span, a, strong, em, i, b, u, sub, sup").forEach((element) => {
      const text = element.textContent || "";
      element.parentNode?.replaceChild(doc.createTextNode(text), element);
    });

    const cleanedHtml = doc.body.innerHTML;
    let htmlString = cleanedHtml;

    const index = htmlString.indexOf(variable);
    if (index === -1) return "";

    const start = Math.max(0, index - 10000);
    const end = Math.min(htmlString.length, index + marker.length + 2000);
    return htmlString.substring(start, end);
  });
};

export const getContextUnderCursor = () => {
  return Word.run((context) => {
    return withMarker(context, (marker) => {
      return getContextOfVariable([marker]);
    });
  });
};

export const getVariables = () => {
  return Word.run(async (context) => {
    const searchResults = context.document.body.search("[{]*[}]", { matchWildcards: true });
    context.load(searchResults, "text");
    await context.sync();

    return searchResults.items.map((item) => item.text);
  });
};

export const getNextVariable = async (): Promise<string> => {
  const [variable] = await getVariables();
  return variable;
};

export const findAndReplace = async (key: string, value: string) => {
  return await Word.run(async (context) => {
    const searchResults = context.document.body.search(key);
    context.load(searchResults, "text");

    await context.sync();

    for (const item of searchResults.items) {
      item.insertText(value, Word.InsertLocation.replace);
    }
    await context.sync();
  });
};

export const queryContextUnderCursor = async (
  knowledge: string[],
  llmConfig: z.infer<typeof modelSchema>,
  stream: boolean,
  controller?: AbortController
) => {
  const contextText = await getContextUnderCursor();
  const prompt = getCursorFillPrompt().replace("{context}", contextText);

  return await service.query(
    { prompt, kl_ids: knowledge, llm_config: llmConfig, streamable: stream },
    controller
  );
};

export const queryContext = async (
  contextText: string,
  knowledge: string[],
  llmConfig: z.infer<typeof modelSchema>,
  stream: boolean,
  controller?: AbortController
) => {
  const prompt = getBatchFillPrompt().replace("{context}", contextText);

  return await service.query(
    { prompt, kl_ids: knowledge, llm_config: llmConfig, streamable: stream },
    controller
  );
};

export const getFilePath = () => {
  return new Promise<string>((resolve) =>
    Office.context.document.getFilePropertiesAsync((result) => resolve(result.value.url))
  );
};

export const reloadDocument = async (filePath: string) => {
  const currentFilePath = await getFilePath();
  if (filePath === currentFilePath) {
    throw new Error("文档已打开");
  }

  await Word.run(async (context) => {
    context.application.openDocument(filePath);
    context.document.close("SkipSave");
  });
};

// ===== 飞书 OAuth 登录逻辑 =====
let userInfo: any = null;
const kbService = KnowledgeBaseService.getInstance();

// 更新UI状态的函数
const updateUIState = async (isLoggedIn: boolean = false) => {
  const loginBtn = document.getElementById("feishu-login-btn");
  const logoutBtn = document.getElementById("feishu-logout-btn");
  const userInfoDiv = document.getElementById("feishu-user-info");
  const kbStatusDiv = document.getElementById("kb-status");
  
  if (loginBtn) loginBtn.style.display = isLoggedIn ? "none" : "block";
  if (logoutBtn) logoutBtn.style.display = isLoggedIn ? "block" : "none";
  
  if (userInfoDiv && isLoggedIn && userInfo) {
    userInfoDiv.innerHTML = `欢迎，${userInfo.name}`;
    userInfoDiv.style.display = "block";
  } else if (userInfoDiv) {
    userInfoDiv.style.display = "none";
  }

  if (kbStatusDiv && isLoggedIn) {
    try {
      kbStatusDiv.textContent = "正在刷新知识库...";
      kbStatusDiv.className = "status-text";
      
      const kbs = await kbService.refresh();
      const totalTokens = kbs.reduce((sum, kb) => sum + kb.tokenCount, 0);
      const totalDocs = kbs.reduce((sum, kb) => sum + kb.docCount, 0);
      
      kbStatusDiv.textContent = `知识库已连接 (${totalDocs} 个文档, ${totalTokens} tokens)`;
      kbStatusDiv.className = "status-text success";
    } catch (error) {
      kbStatusDiv.textContent = `知识库刷新失败: ${error.message}`;
      kbStatusDiv.className = "status-text error";
    }
  } else if (kbStatusDiv) {
    kbStatusDiv.textContent = "";
  }
};

// 处理飞书登录回调消息
window.addEventListener("message", (event) => {
  if (event.data.type === "feishu-login-success") {
    userInfo = event.data.data;
    updateUIState(true);
  }
});

window.addEventListener("DOMContentLoaded", () => {
  const btn = document.getElementById("feishu-login-btn");
  const logoutBtn = document.getElementById("feishu-logout-btn");
  
  if (btn) {
    btn.addEventListener("click", () => {      // 打开飞书授权窗口
      const authUrl = `${FEISHU_AUTH_ENDPOINT}`;
      const width = 800;
      const height = 600;
      const left = (window.innerWidth - width) / 2;
      const top = (window.innerHeight - height) / 2;
      
      window.open(
        authUrl,
        "feishu-login",
        `width=${width},height=${height},left=${left},top=${top}`
      );
    });
  }
  
  if (logoutBtn) {
    logoutBtn.addEventListener("click", () => {
      userInfo = null;
      updateUIState(false);
    });
  }
  
  // 初始化UI状态
  updateUIState(false);
});

Office.onReady(info => {
  if (info.host === Office.HostType.Word) {    // 初始化飞书登录管理器
    FeishuAuthManager.getInstance();
  }
});
