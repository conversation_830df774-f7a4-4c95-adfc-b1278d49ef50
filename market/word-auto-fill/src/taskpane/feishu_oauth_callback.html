<!DOCTYPE html>
<html>
<head>
    <title>飞书登录回调</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
        }
        .container {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .loading {
            margin: 20px 0;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="loading">正在处理登录请求...</div>
    </div>
    
    <script>
        window.onload = async function() {
            try {
                // Get URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                const state = urlParams.get('state');

                if (code && state === 'login') {
                    // Get base URL
                    const baseUrl = process.env.NODE_ENV === 'development' 
                        ? 'http://localhost:3000' 
                        : '';

                    // Exchange code for user info
                    const response = await fetch(`${baseUrl}/api/feishu/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ code })
                    });

                    if (!response.ok) {
                        throw new Error(`登录失败: ${response.status}`);
                    }

                    const data = await response.json();
                    
                    // Store user info
                    window.localStorage.setItem('feishu_user_info', JSON.stringify(data));

                    // Post message to parent window
                    window.opener?.postMessage({
                        type: 'feishu-login-success',
                        data
                    }, '*');

                    // Close window after a short delay
                    setTimeout(() => {
                        if (window.opener) {
                            window.close();
                        } else {
                            window.location.href = '/taskpane.html';
                        }
                    }, 1000);
                }
            } catch (error) {
                console.error('登录处理失败:', error);
                document.querySelector('.loading').textContent = '登录失败，请重试';
            }
        }
    } else {
      document.getElementById('msg').textContent = '未获取到授权信息，请重试。';
    }
  </script>
</body>
</html>
