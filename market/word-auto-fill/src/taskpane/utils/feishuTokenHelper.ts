// 飞书令牌管理工具
// 用于获取和存储飞书访问令牌，简化组件中的令牌管理

// 默认的硬编码令牌，作为最后的备选项
const DEFAULT_FEISHU_TOKEN = "u-f8JZ40v7de_FraCC0spN3511kyJAh18rMgG04hg20d66";

// 保存令牌到本地存储
export function saveFeishuToken(token: string): void {
  try {
    localStorage.setItem('feishu_token', token);
  } catch (error) {
    console.error("保存飞书令牌失败:", error);
  }
}

// 从各种可能的位置获取飞书令牌
export function getFeishuToken(): string {
  try {
    // 1. 尝试从localStorage直接获取
    const savedToken = localStorage.getItem('feishu_token');
    if (savedToken) {
      console.log("从localStorage获取到令牌");
      return savedToken;
    }

    // 2. 尝试从飞书用户信息获取
    const userInfoStr = localStorage.getItem('feishu_user_info') || sessionStorage.getItem('feishu_user_info');
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr);
        if (userInfo && userInfo.data && userInfo.data.access_token) {
          // 保存以便后续使用
          saveFeishuToken(userInfo.data.access_token);
          console.log("从用户信息获取到令牌");
          return userInfo.data.access_token;
        }
      } catch (e) {
        console.warn("解析用户信息失败，尝试其他来源");
      }
    }

    // 3. 尝试从配置获取
    const configStr = localStorage.getItem('feishu_config');
    if (configStr) {
      try {
        const config = JSON.parse(configStr);
        if (config && config.user_access_token) {
          // 保存以便后续使用
          saveFeishuToken(config.user_access_token);
          console.log("从配置获取到令牌");
          return config.user_access_token;
        }
      } catch (e) {
        console.warn("解析配置失败，尝试其他来源");
      }
    }

    // 4. 使用默认硬编码令牌
    if (DEFAULT_FEISHU_TOKEN) {
      // 保存默认令牌以便后续使用
      saveFeishuToken(DEFAULT_FEISHU_TOKEN);
      console.log("使用默认硬编码令牌");
      return DEFAULT_FEISHU_TOKEN;
    }
  } catch (error) {
    console.error("获取飞书令牌失败:", error);
  }

  return "";
}

// 检查是否有可用的飞书令牌
export function hasFeishuToken(): boolean {
  return !!getFeishuToken();
}
