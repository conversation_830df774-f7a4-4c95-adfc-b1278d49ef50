<!DOCTYPE html>
<html>
<head>
    <title>飞书授权成功</title>
    <meta charset="utf-8">
</head>
<body>
    <h3>授权成功，正在返回...</h3>
    <script>
        // 从 URL 获取参数
        const params = new URLSearchParams(window.location.search);
        const token = params.get('access_token');
        const openId = params.get('open_id');

        // 发送消息给父窗口
        if (window.opener) {
            window.opener.postMessage({
                type: 'oauthComplete',
                access_token: token,
                open_id: openId
            }, '*');
            // 关闭当前窗口
            window.close();
        }
    </script>
</body>
</html>
