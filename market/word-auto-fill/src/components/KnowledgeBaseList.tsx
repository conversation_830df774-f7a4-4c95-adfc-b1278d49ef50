import React, { useEffect, useState } from 'react';
import { KnowledgeBase, KnowledgeBaseService } from '../services/knowledge-base';

export const KnowledgeBaseList: React.FC = () => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const service = KnowledgeBaseService.getInstance();
    let unsubscribe: (() => void) | undefined;

    const loadKnowledgeBases = async () => {
      try {
        setLoading(true);
        const bases = await service.getKnowledgeBases();
        setKnowledgeBases(bases);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载知识库失败');
      } finally {
        setLoading(false);
      }
    };

    // 初始加载
    loadKnowledgeBases();

    // 订阅更新
    unsubscribe = service.onKnowledgeBasesUpdated((bases) => {
      setKnowledgeBases(bases);
    });

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  if (loading) {
    return <div>加载中...</div>;
  }

  if (error) {
    return <div>错误: {error}</div>;
  }

  if (knowledgeBases.length === 0) {
    return <div>没有找到知识库</div>;
  }

  return (
    <div>
      <h2>可用知识库</h2>
      <ul>
        {knowledgeBases.map((kb) => (
          <li key={kb.name}>
            {kb.name} ({kb.type})
            <br />
            文档数: {kb.docCount} | Token数: {kb.tokenCount}
          </li>
        ))}
      </ul>
    </div>
  );
};
