import { z } from "zod";
import {
  knowledgeListOutputSchema,
  modelListOutputSchema,
  queryInputSchema,
  queryOutputSchema,
  queryPreviewInputSchema,
  queryPreviewOutputSchema,
  wordAnalyzeOutputSchema,
} from "../models";
import { globalStore } from "../store";
import { service } from "../utils/service";

const getTimeout = () => globalStore.getState().timeout * 1000;

export interface KnowledgeConfig {
  token_size: number;
  path: string;
  type: string;
}

export const knowledgeList = async () => {
  const response = await service.get<{ data: Record<string, KnowledgeConfig> }>(
    "/knowledge_list",
    { timeout: getTimeout() }
  );
  console.log("[service.knowledgeList] response.data:", response.data);
  return response.data.data;
};

export const wordAnalyze = async (filePath: string, controller?: AbortController): Promise<z.infer<typeof wordAnalyzeOutputSchema>> => {
  try {
    console.log(`发送请求: /word_analyze, 文件路径: ${filePath}`);
    
    // 设置较长的超时时间，避免复杂文档处理超时
    const timeout = Math.max(getTimeout(), 30000); // 至少30秒
    
    const response = await service.get<{ data: any }>(
      "/word_analyze",
      {
        params: { file_path: filePath, mode: globalStore.getState().analyzeMode },
        signal: controller?.signal,
        timeout: timeout,
      }
    );
    
    console.log("请求成功:", response.status, response.data);
    
    // 确保响应数据符合预期
    if (!response.data || !response.data.data) {
      console.error("响应格式错误:", response.data);
      return { 
        success: false,
        error: "数据格式错误",
        message: "服务器返回了不正确的数据格式" 
      } as z.infer<typeof wordAnalyzeOutputSchema>;
    }
    
    // 检查响应结构
    const result = response.data.data;
    if (result.success === true && 'file_path' in result) {
      // 成功响应
      return {
        success: true,
        file_path: result.file_path
      } as z.infer<typeof wordAnalyzeOutputSchema>;
    } else {
      // 失败响应
      return {
        success: false,
        error: result.error || "未知错误",
        message: result.message || "处理失败但未提供详细信息"
      } as z.infer<typeof wordAnalyzeOutputSchema>;
    }
  } catch (error: any) {
    console.error("请求失败:", error);
    
    // 检查是否是主动终止
    if (controller?.signal?.aborted) {
      return { 
        success: false, 
        error: "请求已终止",
        message: "用户终止了操作" 
      } as z.infer<typeof wordAnalyzeOutputSchema>;
    }
    
    // 根据错误类型返回适当的错误信息
    if (error.response) {
      // 服务器返回了错误响应
      const status = error.response.status;
      const errorData = error.response.data || {};
      return {
        success: false,
        error: `服务器错误: ${status}`,
        message: errorData.message || errorData.error || "服务器处理请求时出错"
      } as z.infer<typeof wordAnalyzeOutputSchema>;
    } else if (error.request) {
      // 请求发送成功但没有收到响应
      return {
        success: false,
        error: "网络连接错误",
        message: "无法连接到服务器，请检查网络连接"
      } as z.infer<typeof wordAnalyzeOutputSchema>;
    } else {
      // 请求配置错误
      return {
        success: false,
        error: "请求配置错误",
        message: error.message || "发送请求时出错"
      } as z.infer<typeof wordAnalyzeOutputSchema>;
    }
  }
};

export const modelList = async () => {
  const response = await service.get<{ [key: string]: any }>(
    "/llm_model_list",
    { timeout: getTimeout() }
  );
  console.log("[service.modelList] response.data:", response.data);
  return response.data;
};

export const query = async (
  arg: z.infer<typeof queryInputSchema>,
  controller?: AbortController
) => {
  try {
    const response = await service.post<{ data: z.infer<typeof queryOutputSchema> }>(
      "/query",
      {
        ...arg,
        llm_config: { ...arg.llm_config, temperature: globalStore.getState().temperature },
      },
      { signal: controller?.signal, timeout: getTimeout() }
    );

    return response.data.data;
  } catch (error) {
    if (controller?.signal?.aborted) {
      // 请求被终止时不抛出异常，返回明确的 aborted 标记
      return { aborted: true };
    }
    throw error;
  }
};

export const queryPreview = async (arg: z.infer<typeof queryPreviewInputSchema>) => {
  const response = await service.post<{ data: z.infer<typeof queryPreviewOutputSchema> }>(
    "/query_preview",
    arg,
    { timeout: getTimeout() }
  );
  return response.data.data;
};
