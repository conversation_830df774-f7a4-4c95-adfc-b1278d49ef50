/**
 * 自定义 webpack 插件，用于处理 node: 协议前缀
 * 将 node: 协议前缀的模块转换为普通的模块导入
 */
class WebpackNodePrefixPlugin {
  apply(compiler) {
    console.log('WebpackNodePrefixPlugin: Plugin registered');
    
    // 在 NormalModuleFactory hook 中拦截模块解析
    compiler.hooks.normalModuleFactory.tap('WebpackNodePrefixPlugin', (factory) => {
      // 拦截 resolve hook
      // In Webpack 5, beforeResolve is a bailing hook, not a waterfall hook
      // This means we should modify the data object in place and not return it
      factory.hooks.beforeResolve.tapAsync('WebpackNodePrefixPlugin', (data, callback) => {
        if (data && data.request && data.request.startsWith('node:')) {
          console.log(`WebpackNodePrefixPlugin: Converting ${data.request} to ${data.request.substring(5)}`);
          // 将 node:xxx 改为 xxx, modifying data in place
          data.request = data.request.substring(5);
        }
        // Just call callback with null (no error) to continue the process
        // For bailing hooks, returning true means "continue processing"
        callback(null);
      });
    });
  }
}

module.exports = WebpackNodePrefixPlugin;
