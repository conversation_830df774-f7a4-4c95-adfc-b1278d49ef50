/* eslint-disable no-undef */

const devCerts = require("office-addin-dev-certs");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const webpack = require("webpack");
const path = require("path");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const WebpackNodePrefixPlugin = require("./src/webpack-node-prefix-plugin");

// Define URLs for development and production environments
const urlDev = "https://localhost:3000/";
const urlProd = "https://www.contoso.com/"; // Update this to your production URL

async function getHttpsOptions() {
  const httpsOptions = await devCerts.getHttpsServerOptions();
  return { ca: httpsOptions.ca, key: httpsOptions.key, cert: httpsOptions.cert };
}

module.exports = async (env, options) => {
  const dev = options.mode === "development";
  const config = {
    devtool: "source-map",
    entry: {
      polyfill: ["core-js/stable", "regenerator-runtime/runtime"],
      react: ["react", "react-dom"],
      taskpane: {
        import: ["./src/taskpane/index.tsx", "./src/taskpane/taskpane.html"],
        dependOn: "react",
      },
      commands: "./src/commands/commands.ts",
    },    output: {
      clean: true,
    },
    // 指定某些模块作为外部模块
    externals: {
      'node:events': 'commonjs events',
      'node:fs': 'commonjs fs',
      'node:fs/promises': 'commonjs fs/promises',
      'node:path': 'commonjs path',
      'node:stream': 'commonjs stream',
      'node:string_decoder': 'commonjs string_decoder',
      'node:url': 'commonjs url',
      'node:crypto': 'commonjs crypto',
      'node:os': 'commonjs os',
      'node:process': 'commonjs process',
    },resolve: {
      extensions: [".ts", ".tsx", ".html", ".js"],
      fullySpecified: false,      fallback: {
        assert: require.resolve('assert'),
        buffer: require.resolve('buffer'),
        console: require.resolve('console-browserify'),
        constants: require.resolve('constants-browserify'),
        crypto: require.resolve('crypto-browserify'),
        domain: require.resolve('domain-browser'),
        events: require.resolve('events'),
        http: require.resolve('stream-http'),
        https: require.resolve('https-browserify'),
        os: require.resolve('os-browserify/browser'),
        path: require.resolve('path-browserify'),
        punycode: require.resolve('punycode'),
        process: require.resolve('process/browser'),
        querystring: require.resolve('querystring-es3'),
        stream: require.resolve('stream-browserify'),
        string_decoder: require.resolve('string_decoder'),
        sys: require.resolve('util'),
        timers: require.resolve('timers-browserify'),
        tty: require.resolve('tty-browserify'),
        url: require.resolve('url'),
        util: require.resolve('util'),
        vm: require.resolve('vm-browserify'),
        zlib: require.resolve('browserify-zlib'),
        fs: false,
        'fs/promises': false,
        'process/browser': require.resolve('process/browser'),
      },
      alias: {
        'process/browser$': require.resolve('process/browser'),
        'process$': require.resolve('process/browser')
      }
    },    module: {
      rules: [        // 处理所有 JS/TS 文件中的 node: 协议导入
        {
          test: /\.(js|ts|tsx|jsx)$/,
          use: [
            {
              loader: path.resolve('./strip-node-protocol-loader.js')
            }
          ]
        },
        {
          test: /\.m?js$/,
          resolve: {
            fullySpecified: false
          }
        },
        {
          test: /\.[tj]sx?$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              plugins: [
                ["@babel/plugin-transform-modules-commonjs", { allowTopLevelThis: true }],                ["module-resolver", {
                  root: ["."],
                  alias: {
                    "node:(.+)": "$1",
                    "^node:(.+)$": "$1"
                  },
                }],
              ],
              presets: [
                ["@babel/preset-env", {
                  targets: "defaults",
                  modules: "commonjs"
                }],
                "@babel/preset-typescript"
              ]
            }
          }
        },        {
          test: /\.tsx?$/,
          exclude: /node_modules/,
          use: ["ts-loader"]
        },
        {
          test: /\.html$/,
          exclude: /node_modules/,
          use: "html-loader"
        },
        {
          test: /\.(png|jpg|jpeg|gif|ico)$/,
          type: "asset/resource",
          generator: {
            filename: "assets/[name][ext][query]"
          }
        }
      ]
    },    plugins: [
      new NodePolyfillPlugin(),
      new WebpackNodePrefixPlugin(), // 确保插件在这里被明确引用
      new webpack.ProvidePlugin({
        process: require.resolve('process/browser'),
        Buffer: ['buffer', 'Buffer']
      }),
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(dev ? 'development' : 'production'),
        'process.env': JSON.stringify({}),
        'process.browser': true,
        'process.version': JSON.stringify(process.version)
      }),
      new HtmlWebpackPlugin({
        filename: "taskpane.html",
        template: "./src/taskpane/taskpane.html",
        chunks: ["polyfill", "taskpane", "react"]
      }),
      new HtmlWebpackPlugin({
        filename: "commands.html",
        template: "./src/commands/commands.html",
        chunks: ["polyfill", "commands"]
      }),
      new CopyWebpackPlugin({
        patterns: [
          {
            from: "assets/*",
            to: "assets/[name][ext]"
          },
          {
            from: "manifest*.xml",
            to: "[name]" + "[ext]"
          },          {
            from: "./src/taskpane/feishu_oauth_callback.html",
            to: "feishu_oauth_callback.html"
          }
        ]
      }),
    ],    devServer: {
      headers: {
        "Access-Control-Allow-Origin": "*"
      },
      server: {
        type: "https",
        options: env.WEBPACK_BUILD || options.https !== undefined ? options.https : await getHttpsOptions()
      },
      port: process.env.npm_package_config_dev_server_port || 3000,      historyApiFallback: true,proxy: [
        {
          context: ['/feishu_oauth_callback'],
          target: 'http://127.0.0.1:5000', // 改回 http
          changeOrigin: true,
          secure: false,
          logLevel: 'debug'
        },
        {
          context: ['/api'],
          target: 'http://127.0.0.1:5000', // 改回 http
          changeOrigin: true,
          secure: false,
          logLevel: 'debug'
        }
      ]
    }
  };

  return config;
};
