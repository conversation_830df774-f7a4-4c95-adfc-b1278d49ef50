{"name": "office-addin-taskpane-react", "version": "0.0.1", "repository": {"type": "git", "url": "https://github.com/OfficeDev/Office-Addin-TaskPane-React.git"}, "license": "MIT", "config": {"app_to_debug": "word", "app_type_to_debug": "desktop", "dev_server_port": 3000}, "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "dev-server": "webpack serve --mode development", "start:backend": "python feishu_oauth_callback.py", "start:dev": "powershell -ExecutionPolicy Bypass -File start-dev.ps1", "lint": "office-addin-lint check", "lint:fix": "office-addin-lint fix", "prettier": "office-addin-lint prettier", "signin": "office-addin-dev-settings m365-account login", "signout": "office-addin-dev-settings m365-account logout", "start": "office-addin-debugging start manifest.xml", "stop": "office-addin-debugging stop manifest.xml", "validate": "office-addin-manifest validate manifest.xml", "watch": "webpack --mode development --watch"}, "dependencies": {"@fluentui/react-components": "^9.55.1", "@fluentui/react-icons": "^2.0.307", "@microsoft/fetch-event-source": "^2.0.1", "axios": "^0.27.2", "browser-process": "^0.0.1", "es6-promise": "^4.2.8", "fs": "^0.0.1-security", "glob": "^11.0.3", "immer": "^10.1.1", "lodash": "^4.17.21", "path": "^0.12.7", "react": "^18.2.0", "react-dom": "^18.2.0", "regenerator-runtime": "^0.14.1", "swr": "^2.3.3", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@types/glob": "^8.1.0", "@types/lodash": "^4.17.18", "@types/node": "^24.1.0", "@types/office-js": "^1.0.377", "@types/office-runtime": "^1.0.35", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/webpack": "^5.28.5", "acorn": "^8.11.3", "assert": "^2.1.0", "babel-loader": "^9.2.1", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-import-meta": "^2.3.3", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "console-browserify": "^1.2.0", "constants-browserify": "^1.0.0", "copy-webpack-plugin": "^12.0.2", "core-js": "^3.44.0", "crypto-browserify": "^3.12.1", "domain-browser": "^5.7.0", "eslint-plugin-office-addins": "^4.0.3", "eslint-plugin-react": "^7.28.0", "events": "^3.3.0", "file-loader": "^6.2.0", "html-loader": "^5.0.0", "html-webpack-plugin": "^5.6.0", "https-browserify": "^1.0.0", "less": "^4.2.0", "less-loader": "^12.2.0", "node-polyfill-webpack-plugin": "^4.1.0", "office-addin-cli": "^2.0.3", "office-addin-debugging": "^6.0.3", "office-addin-dev-certs": "^2.0.3", "office-addin-lint": "^3.0.3", "office-addin-manifest": "^2.0.3", "office-addin-prettier-config": "^2.0.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "punycode": "^2.3.1", "querystring-es3": "^0.2.1", "source-map-loader": "^5.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "string_decoder": "^1.3.0", "timers-browserify": "^2.0.12", "ts-loader": "^9.5.1", "tty-browserify": "^0.0.1", "typescript": "^5.4.2", "url": "^0.11.4", "util": "^0.12.5", "vm-browserify": "^1.1.2", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "prettier": "office-addin-prettier-config", "browserslist": ["last 2 versions", "ie 11"], "proxy": {"/api": "http://localhost:5000", "/feishu_oauth_callback": "http://localhost:5000"}}