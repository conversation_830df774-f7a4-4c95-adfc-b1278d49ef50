/**
 * 简单的 webpack loader，用于移除 node: 协议前缀
 * 适用于所有模块，包括 node_modules 中的模块
 */
module.exports = function(source) {
  // 声明这是一个 webpack 加载器，让 webpack 知道这是一个异步操作
  const callback = this.async();
  
  const originalSource = source;
  
  // 将所有 'node:模块名' 替换为 '模块名'
  const modifiedSource = source.replace(/(['"])node:([^'"]+)(['"])/g, function(match, quote1, moduleName, quote2) {
    return quote1 + moduleName + quote2;
  });
  
  // 如果有修改，记录日志
  if (originalSource !== modifiedSource && this.resourcePath) {
    const matches = originalSource.match(/(['"])node:([^'"]+)(['"])/g);
    if (matches) {
      console.log(`[strip-node-protocol-loader] Fixed ${matches.length} node: imports in ${this.resourcePath.split('\\').pop()}`);
    }
  }
  
  // 使用 callback 返回结果，确保异步加载器正确工作
  callback(null, modifiedSource);
};
