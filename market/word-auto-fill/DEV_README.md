# Word 加载项 - 飞书集成开发指南

## 开发环境配置

本项目集成了前端 Office 加载项和后端 Flask API 服务器，实现飞书 OAuth 认证功能。

### 前置要求

1. **Node.js** (推荐版本 16+)
2. **Python** (推荐版本 3.8+)
3. **npm** 或 **yarn**

### 快速启动

#### 方法一：使用批处理脚本（推荐）

```bash
# Windows 批处理文件
start-dev.bat

# 或使用 PowerShell 脚本
start-dev.ps1
```

#### 方法二：手动启动

1. **安装依赖**
   ```bash
   # Python 依赖
   pip install -r requirements.txt
   
   # Node.js 依赖
   npm install
   ```

2. **启动后端服务器** (新终端窗口)
   ```bash
   python feishu_oauth_callback.py
   ```
   后端服务器将在 `http://localhost:5000` 启动

3. **启动前端开发服务器** (另一个终端窗口)
   ```bash
   npm run dev-server
   ```
   前端服务器将在 `https://localhost:3000` 启动

### 服务架构

```
┌─────────────────────┐    ┌─────────────────────┐
│   前端 (React)      │    │   后端 (Flask)      │
│   localhost:3000    │◄──►│   localhost:5000    │
│   - Office 加载项   │    │   - OAuth 回调      │
│   - 用户界面        │    │   - API 接口        │
└─────────────────────┘    └─────────────────────┘
           │                           │
           │                           │
           ▼                           ▼
┌─────────────────────┐    ┌─────────────────────┐
│      Word 应用      │    │     飞书 API        │
│                     │    │                     │
└─────────────────────┘    └─────────────────────┘
```

### API 端点

#### 后端 Flask 服务器 (localhost:5000)

- `GET /feishu_oauth_callback` - 飞书 OAuth 回调处理
- `POST /api/refresh_token` - 刷新访问令牌
- `GET /api/user/info` - 获取用户信息
- `GET /api/knowledge_bases` - 获取知识库列表
- `POST /api/refresh_knowledge_bases` - 刷新知识库

#### 前端代理配置

前端开发服务器会自动将以下请求代理到后端：
- `/feishu_oauth_callback` → `http://localhost:5000`
- `/api/*` → `http://localhost:5000`

### 飞书应用配置

确保在飞书开放平台配置中设置：

- **回调地址**: `https://localhost:3000/feishu_oauth_callback`
- **应用 ID**: `cli_a8fe2f0835bdd00c`

### 开发调试

1. **查看 webpack 构建日志**：前端终端会显示详细的构建信息
2. **查看 Flask 服务器日志**：后端终端会显示 API 请求日志
3. **浏览器开发者工具**：检查网络请求和 JavaScript 错误

### 常见问题

#### 1. CORS 错误
确保后端 Flask 服务器已启动并配置了 CORS：
```python
from flask_cors import CORS
CORS(app, origins=["https://localhost:3000"])
```

#### 2. OAuth 回调失败
检查：
- 飞书应用配置中的回调地址是否正确
- 网络代理配置是否正确
- 后端服务器是否正常运行

#### 3. 证书问题
Office 加载项需要 HTTPS，开发环境会自动生成自签名证书。如果遇到证书问题：
```bash
npm run signout
npm run signin
```

### 构建生产版本

```bash
npm run build
```

生产构建会输出到 `dist/` 目录。

### 项目结构

```
word-auto-fill/
├── src/
│   ├── taskpane/           # 前端 React 组件
│   ├── config/             # 配置文件
│   └── commands/           # Office 命令
├── feishu_oauth_callback.py # 后端 Flask 服务器
├── webpack.config.js       # Webpack 配置
├── package.json           # Node.js 依赖
├── requirements.txt       # Python 依赖
└── start-dev.*           # 开发启动脚本
```
