{"compilerOptions": {"allowUnusedLabels": false, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "module": "ES2020", "moduleResolution": "node", "noImplicitReturns": true, "noUnusedParameters": true, "outDir": "dist", "removeComments": false, "sourceMap": true, "target": "es5", "lib": ["es7", "dom"], "pretty": true, "typeRoots": ["node_modules/@types", "src/types"], "skipLibCheck": true}, "exclude": ["node_modules"], "compileOnSave": false, "buildOnSave": false, "ts-node": {"compilerOptions": {"module": "commonjs"}, "files": true}}