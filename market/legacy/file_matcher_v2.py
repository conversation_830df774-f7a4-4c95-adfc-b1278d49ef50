from utils.iter import chunk_generator
from utils.llm import ModelName, get_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field
from typing import List


class MatchedFile(BaseModel):
    matched_file: str = Field(description="匹配的文件,要求一定要返回完整路径，不能有任何删减")
    score: int = Field(description="匹配得分, 范围为0-100, 0表示不匹配, 100表示完全匹配")
    reason: str = Field(description="匹配原因")


class MatchedFileList(BaseModel):
    matched_file_list: List[MatchedFile] = Field(description="匹配的文件列表")


def query(
    file_path: str, target: str, debug=False, model_name: ModelName = "DeepSeek-V3"
) -> bool:
    parser = PydanticOutputParser(pydantic_object=MatchedFileList)
    system_prompt = f"""
背景：
客户在投资前和投资过程中，通常会对我司进行尽职调查，并提供一份《材料清单》，其中会列明所需的各类材料（如：尽调问卷、产品净值数据、绩效归因报告、营业执照等），格式包括 Word、Excel、PPT、PDF 等。
任务：
根据客户提出的材料需求，从提供的文件列表中识别并提取出所有满足需求的文件，并返回一个标准化 JSON 输出。
输入包括：
文件列表：一个字符串列表，包含所有可供选择的文件名（含扩展名）
材料需求：客户在《材料清单》中提出的具体材料需求（如：营业执照）
输出要求：
只包含匹配得分 score ≥ 60 的文件
{parser.get_format_instructions()}
文件列表：
{file_path}
    """
    model = get_model(model_name)
    model = model.with_structured_output(
        MatchedFileList
    )

    input = [SystemMessage(system_prompt), HumanMessage(target)]
    if model_name == "Qwen3-235B-A22B":
        input.insert(0, SystemMessage("/no_think"))

    response = model.invoke(input)
    return response.model_dump()


def query_single(
    file_list: List[str],
    keyword: str,
    debug: bool = False,
    model_name: ModelName = "DeepSeek-V3",
    chunk_size: int = 1000,
    min_score: int = 60,
):
    matched_file_list = []
    for chunk in chunk_generator(file_list, chunk_size):
        result = query(chunk, keyword, debug, model_name)
        matched_file_list.extend(result['matched_file_list'])

    matched_file_list = [x for x in matched_file_list if x['score'] >= min_score]
    result = {"query": keyword, "matches": matched_file_list}

    return result


if __name__ == "__main__":
    import json
    with open("./tests/file_match/file_list.json") as file:
        file_list = json.load(file)
        file_list = [x for x in file_list if "常用材料" in x]
    result = query_single(
        file_list,
        "营业执照正/副本复印件",
    )
    print(result)
