import json
from docx import Document
from utils.doc_extract_util import convert_to_json_with_xpath
from utils.llm import get_model
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import PydanticOutputParser

model_name = "openai/gpt-4o-mini"


class Task(BaseModel):
    query: str = Field(description="需要填写的内容，忠于原文，不要做任何修改")
    answer_xpath: str = Field(description="需要填写的位置xpath")
    answer_type: str = Field(description="任务类型, 1: 文本类，如文本中包含填写关键字或者其他公司名称、统一社会信用代码等，都属于需要填写文本； 2: 文件类 如查询营业执照、复印件等，都属于需要提供文件", required=False)
    category: str = Field(description="问题分类：1：客观题， 只有一个确定的答案，如公司名称、成立时间等 2. 主观题，按自己的立即根据知识库总结内容", required=False)


class TaskList(BaseModel):
    tasks: list[Task] = Field(description="任务列表")


def generate_task_for_table(table_json_obj, model_name="DeepSeek-R1", model_source="local"):
    parser = PydanticOutputParser(pydantic_object=TaskList)
    system_prompt = f"""
    你是一个资料填写助手，用户会以json形式传入从word中提取的表格。你需要从中识别需要填写哪些内容,并且将任务分类内容填写和文件查找两种类型。并且返回最终需要填入的位置xpath。"没有相关文件"代表不需要做任何操作。
    要求：
    1. 你需要从中识别需要填写哪些内容。
    2. 识别任务分类，任务分类分为两种：1：客观题， 只有一个确定的答案，如公司名称、成立时间等 2. 主观题，需要用户根据知识库总结内容，填写主观题。如：公司基本情况、公司基本情况等。
    3. 识别问题需要提供的答案类型，1: 文本类，如文本中包含填写关键字或者其他公司名称、统一社会信用代码等，都属于需要填写文本； 2: 文件类 如查询营业执照、复印件等，都属于需要提供文件。
    4. 判断答案需要填写的位置，并且返回xpath。

    {parser.get_format_instructions()}
    """
    model = get_model(model_name, model_source)
    result = model.invoke([SystemMessage(system_prompt), HumanMessage(json.dumps(table_json_obj, ensure_ascii=False))], timeout=600).content

    result = parser.parse(result)
    return result


def generate_tasks(doc_file, model_name="DeepSeek-R1", model_source="local", max=10000):
    document = Document(doc_file)
    doc_json = convert_to_json_with_xpath(document)
    tables = [element for element in doc_json.get("content") if element.get('type') == 'Table']
    tasks = []
    for table in tables:
        result = generate_task_for_table(table, model_name, model_source).model_dump()
        tasks.extend(result['tasks'])
        if len(tasks) >= max:
            break
    return tasks[:max]


def generate_tasks_for_doc(doc_file, model_name="DeepSeek-R1", model_source="local", max=10000):
    document = Document(doc_file)
    doc_json = convert_to_json_with_xpath(document)
    parser = PydanticOutputParser(pydantic_object=TaskList)
    system_prompt = f"""
    你是一个资料填写助手，用户会以json形式传入从word内容。你需要从中识别需要填写哪些内容。并且返回最终需要填入的位置xpath。
    要求：
    1. 因为用户没有提供具体的产品名称，所以不要提取产品相关的问题。
    2. 你需要从中识别需要填写哪些内容。
    3. 识别任务分类，任务分类分为两种：1：客观题， 只有一个确定的答案，如公司名称、成立时间等 2. 主观题，需要用户根据知识库总结内容，填写主观题。如：公司基本情况、公司基本情况等。
    4. 识别问题需要提供的答案类型，1: 文本类，如文本中包含填写关键字或者其他公司名称、统一社会信用代码等，都属于需要填写文本； 2: 文件类 如查询营业执照、复印件等，都属于需要提供文件。
    5. 判断答案需要填写的位置，并且返回xpath。

    {parser.get_format_instructions()}
    """
    model = get_model(model_name, model_source)
    result = model.invoke([SystemMessage(system_prompt), HumanMessage(json.dumps(doc_json, ensure_ascii=False))], timeout=600).content

    result = parser.parse(result)
    return result


if __name__ == "__main__":
    doc_file = "/Users/<USER>/py_workplace/llm/playground/llm_for_a/knowledge_base/尽调报告/代销机构代销业务/关于私募证券产品投资管理人调查问卷（空）—国信证券.docx"
    document = Document(doc_file)
    tasks = generate_tasks_for_doc(doc_file)
    print(tasks)


# 帮我提取表格中需要填充的内容，以下面的形式返回
# [{"query": "营业执照", "answer_position": ""}]
