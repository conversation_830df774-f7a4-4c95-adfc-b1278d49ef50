import json
from docx import Document
from market.task_generator import generate_tasks
from file_matcher_v2 import query_single as query_file
from content_matcher import query_single
from utils.doc_extract_util import write_by_xpath
from utils.doc_compare_directly import DocumentComparator


def do_match_file_task(task, file_list, document, output_file):
    result = query_file(file_list, task.query, debug=True, model_name="Qwen3-235B-A22B")
    target_files = [x['matched_file'] for x in result["matches"]]
    if len(target_files) == 0:
        target_files = ["已完成"]
    target_files = "\n".join(target_files)
    write_by_xpath(document, task.answer_xpath, target_files)
    document.save(output_file)


def do_content_match_task(task, document, output_file):
    text = query_single("./tests/content_match/衍复尽调内部资料库 2025年一季度末.docx", task.query, debug=True)
    write_by_xpath(document, task.answer_xpath, text)
    document.save(output_file)


def do_task(task, file_list, document, output_file):
    if task.task_type == "2":
        do_match_file_task(task, file_list, document, output_file)
    elif task.task_type == "1":
        do_content_match_task(task, document, output_file)


def do_compare(doc_file, output_file):
    comparator = DocumentComparator(doc_file, output_file)
    print("开始比较Word文档...")
    result = comparator.compare_all()
    print("比较完成，请查看 comparison_report.html", result)


def main():
    doc_file = "/Users/<USER>/py_workplace/llm/playground/llm_for_a/market/resources/国民信托私募产品管理人尽调底稿（模板）-尽调对象填写(9)(1).docx"
    output_file = "test2.docx"
    document = Document(doc_file)
    tasks = generate_tasks(
        doc_file
    )
    with open("./tests/file_match/file_list.json") as file:
        file_list = json.load(file)
        file_list = [x for x in file_list if "常用材料" in x]

    for task in tasks:
        do_task(task, file_list, document, output_file)
    do_compare(doc_file, output_file)


if __name__ == "__main__":
    main()
