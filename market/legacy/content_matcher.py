from docx import Document
from utils.doc_extract_util import extract_text
from utils.llm import ModelName, get_model
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from typing import Dict, List, Literal


class ResponseFormatter(BaseModel):
    match: bool = Field(description="是否匹配")
    type: Literal["simple", "medium", "complex"] = Field(
        description="提取文本的长度/复杂度"
    )
    extracted_text: str = Field(description="提取的文本")


class ResponseFormatterDebug(BaseModel):
    match: bool = Field(description="是否匹配")
    type: Literal["simple", "medium", "complex"] = Field(
        description="提取文本的长度/复杂度"
    )
    extracted_text: str = Field(description="提取的文本")
    score: int = Field(description="匹配得分")
    reason: List[str] = Field(description="匹配原因")


prompt_template_debug = """
### 角色背景
您正在扮演私募基金公司文职助理角色，负责处理客户尽职调查的材料需求。

### 处理流程
请按照以下步骤处理输入的《材料清单》原文片段 "{text}"：

**1. 目标匹配判断**
- 匹配检查：识别文本是否包含所需的 "{target}" 类型信息
- 输出字段：match (布尔值)

**2. 内容分类评估**
判断信息复杂度（仅当 match=true 时进行）：
- ◆ simple: 20字符内的基础字段 » 名称/编号/日期等
- ◆ medium: ≈100字符的简述 » 概念说明/现状概括等
- ◆ complex: 不限长度的完整说明 » 详细分析/流程描述等
- 输出字段：type (三选一)

**3. 信息精确提取**（仅当 match=true 时执行）
- √ 严格遵循类型规范：
  - simple: 提炼关键值（可加工）
  - medium/complex: 必须返回原文（禁止修改）
- √ 需符合填表用途要求
- 输出字段：extracted_text

**4. 可信度量化评估**
- 评分标准：
  ▶ 0-50: 信息不相关或存疑
  ▶ 51-80: 部分匹配但需人工核对
  ▶ 81-100: 准确匹配可置信
- 输出字段：score (整数)

**5. 置信依据说明**（仅当 score≥70 时需要）
- 指出至少2个关键判断依据：
  例："包含完整的归因分析段落" 
      "日期与备案记录对应"
- 输出字段：reason (数组)

### 响应格式
请始终返回规范的JSON对象：
{{
  "match": Boolean,
  "type": String|null,
  "extracted_text": String|null,
  "score": Integer,
  "reason": String[]|null
}}

### 示例响应
{{
  "match": true,
  "type": "medium",
  "extracted_text": "本产品Q3夏普比率为1.35，最大回撤控制在12%以内...（完整原文节选）",
  "score": 88,
  "reason": ["包含完整季度绩效指标","参数格式与请求模板吻合"]
}}
"""

prompt_template = """
### 角色背景
您正在扮演私募基金公司文职助理角色，负责处理客户尽职调查的材料需求。

### 处理流程
请按照以下步骤处理输入的《材料清单》原文片段 "{text}"：

**1. 目标匹配判断**
- 匹配检查：识别文本是否包含所需的 "{target}" 类型信息
- 输出字段：match (布尔值)

**2. 内容分类评估**
判断信息复杂度（仅当 match=true 时进行）：
- ◆ simple: 20字符内的基础字段 » 名称/编号/日期等
- ◆ medium: ≈100字符的简述 » 概念说明/现状概括等
- ◆ complex: 不限长度的完整说明 » 详细分析/流程描述等
- 输出字段：type (三选一)

**3. 信息精确提取**（仅当 match=true 时执行）
- √ 严格遵循类型规范：
  - simple: 提炼关键值（可加工）
  - medium/complex: 必须返回原文（禁止修改）
- √ 需符合填表用途要求
- 输出字段：extracted_text

### 响应格式
请始终返回规范的JSON对象：
{{
  "match": Boolean,
  "type": String|null,
  "extracted_text": String|null
}}

### 示例响应
{{
  "match": true,
  "type": "medium",
  "extracted_text": "本产品Q3夏普比率为1.35，最大回撤控制在12%以内...（完整原文节选）"
}}
"""


def unwrap(query_result: Dict[str, str]):
    match = query_result.get("match", False)
    answer = query_result.get("extracted_text", None) if match else None

    return answer


def query(
    text: str, target: str, debug: bool = False, model_name: ModelName = "DeepSeek-V3"
) -> Dict[str, str]:
    template = prompt_template_debug if debug else prompt_template
    prompt = template.format(target=target, text=text)

    model = get_model(model_name)
    model = model.with_structured_output(
        ResponseFormatterDebug if debug else ResponseFormatter
    )

    input = [HumanMessage(prompt)]
    if model_name == "Qwen3-235B-A22B":
        input.insert(0, SystemMessage("/no_think"))

    response = model.invoke(input)
    return response.model_dump()


def query_single(
    doc_file: str,
    query: str,
    debug: bool = False,
    model_name: ModelName = "DeepSeek-V3",
):
    result = query_multi(doc_file, [query], debug, model_name)
    if not result:
        return None

    matches = [x for x in result[0]["matches"] if x["match"] == True]
    if not matches:
        return None

    return matches[0]["extracted_text"]


def query_multi(
    doc_file: str,
    queries: List[str],
    debug: bool = False,
    model_name: ModelName = "DeepSeek-V3",
):
    result = []

    for q in queries:
        if not q:
            continue

        matches = []

        document = Document(doc_file)
        elements = document.element.body.inner_content_elements
        text_list = [extract_text(x) for x in elements]
        overlap_size = 5000
        start = 0
        max_token = 20480
        text = ""
        while start < len(text_list):
            text = text[-overlap_size:]
            while len(text) < max_token and start < len(text_list):
                text = text + "\n" + text_list[start]
                start = start + 1

            response = query(text, q, debug, model_name)
            match = response.get("match", False)
            type = response.get("type", None)
            extracted_text = response.get("extracted_text", None)
            score = response.get("score", 0)
            reason = response.get("reason", [])

            if debug:
                print(
                    f"query: {q}, type: {type}, match: {match}, score: {score}, reason: {reason}"
                )
            else:
                print(f"query: {q}, type: {type}, match: {match}")

            print("-------------------------text---------------------------")
            print(text[:100] + "...")
            print("-----------------------text-end-------------------------")

            print("--------------------extracted-text----------------------")
            print(extracted_text)
            print("------------------extracted-text-end-------------------")

            if not debug:
                if not match:
                    continue

            matches.append(
                {
                    "match": match,
                    "type": type,
                    "extracted_text": extracted_text,
                    "text": text,
                    "score": score,
                    "reason": reason,
                }
            )

        result.append({"query": q, "matches": matches})

    return result
