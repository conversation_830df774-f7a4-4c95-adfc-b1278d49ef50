from utils.iter import chunk_generator
from utils.llm import ModelName, get_model
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from typing import List


class ResponseFormatter(BaseModel):
    match: bool = Field(description="是否匹配")
    extracted_file_list: List[str] = Field(description="匹配的文件列表")


class ResponseFormatterDebug(BaseModel):
    match: bool = Field(description="是否匹配")
    extracted_file_list: List[str] = Field(description="匹配的文件列表")
    score: int = Field(description="匹配得分")
    reason: str = Field(description="匹配原因")


def query(
    file_path: str, target: str, debug=False, model_name: ModelName = "DeepSeek-V3"
) -> bool:
    prompts = [
        "你是一个私募基金公司的文职人员。",
        "客户在投前、投中会对我司提出尽职调查需求，包括要求我司提供一些电子版的材料。通常客户会给一个《材料清单》，清单中会写明材料需求，包括尽调问卷，产品净值数据，产品绩效归因，及一些类似于营业执照的我司用印版材料。材料格式包括word、excel、ppt、pdf等。",
        f"以下为一个或多个文件的路径列表“{file_path}”，请：",
        f"1. 判断文件列表中是否包含需要提取的文件“{target}”，返回 match 字段；",
        f"2. 匹配的文件列表，要求一定要返回完整路径，不能有任何删减，返回 extracted_file_list 字段；",
    ]

    if debug:
        prompts.extend(
            [
                "2. 为匹配度打分 (0-100)，返回 score 字段；",
                "3. 如果判断为需要提取的文件，解释匹配度高的原因，返回 reason 字段。",
            ]
        )

    prompt = "\n\n".join(prompts)

    model = get_model(model_name)
    model = model.with_structured_output(
        ResponseFormatterDebug if debug else ResponseFormatter
    )

    input = [HumanMessage(prompt)]
    if model_name == "Qwen3-235B-A22B":
        input.insert(0, SystemMessage("/no_think"))

    response = model.invoke(input)
    return response.model_dump()


def query_multi(
    file_list: List[str],
    queries: List[str],
    debug: bool = False,
    model_name: ModelName = "DeepSeek-V3",
    chunk_size: int = 10,
):
    result = []

    for q in queries:
        if not q:
            continue

        matches = []

        for chunk in chunk_generator(file_list, chunk_size):
            file = "\n".join(chunk)

            response = query(file, q, debug, model_name)
            match = response.get("match", False)
            extracted_file_list = response.get("extracted_file_list", [])
            score = response.get("score", 0)
            reason = response.get("reason", None)

            if debug:
                print(f"query: {q}, match: {match}, score: {score}, reason: {reason}")
            else:
                print(f"query: {q}, match: {match}")

            print("-----------------------file-list------------------------")
            print(file)
            print("---------------------file-list-end----------------------")

            print("-------------------extracted-file-list------------------")
            print("\n".join(extracted_file_list))
            print("-----------------extracted-file-list-end----------------")

            if not debug:
                if not match:
                    continue

            matches.append(
                {
                    "match": match,
                    "file_list": chunk,
                    "extracted_file_list": extracted_file_list,
                    "score": score,
                    "reason": reason,
                }
            )

        result.append({"query": q, "matches": matches})

    return result
