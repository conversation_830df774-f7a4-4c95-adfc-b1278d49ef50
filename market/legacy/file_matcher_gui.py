import os
import sys
import threading
import tkinter as tk

from tkinter import ttk, scrolledtext, messagebox
from typing import List

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from market.legacy.file_matcher_v3 import query_single
from utils.llm import local_model_list


class MarketFileMatcherGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Market File Matcher")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # Configure grid weights for responsive design
        self.root.grid_rowconfigure(1, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        self.setup_ui()
        self.is_processing = False

    def setup_ui(self):
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_rowconfigure(3, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # Top section - Model selection
        self.setup_model_selection(main_frame)

        # Middle section - Text areas and button
        self.setup_input_section(main_frame)

        # Bottom section - Results display
        self.setup_results_section(main_frame)

    def setup_model_selection(self, parent):
        model_frame = ttk.LabelFrame(parent, text="Model Selection", padding="5")
        model_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        model_frame.grid_columnconfigure(1, weight=1)

        ttk.Label(model_frame, text="Model:").grid(
            row=0, column=0, sticky="w", padx=(0, 10)
        )

        self.model_var = tk.StringVar(value="DeepSeek-V3")
        self.model_combo = ttk.Combobox(
            model_frame,
            textvariable=self.model_var,
            values=local_model_list,
            state="readonly",
            width=30,
        )
        self.model_combo.grid(row=0, column=1, sticky="w")

    def setup_input_section(self, parent):
        input_frame = ttk.Frame(parent)
        input_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        input_frame.grid_rowconfigure(1, weight=1)
        input_frame.grid_columnconfigure(0, weight=1)
        input_frame.grid_columnconfigure(2, weight=1)

        # File list section (left)
        file_frame = ttk.LabelFrame(
            input_frame, text="File List (one file per line)", padding="5"
        )
        file_frame.grid(row=0, column=0, rowspan=2, sticky="nsew", padx=(0, 5))
        file_frame.grid_rowconfigure(0, weight=1)
        file_frame.grid_columnconfigure(0, weight=1)

        self.file_text = scrolledtext.ScrolledText(
            file_frame, wrap=tk.WORD, width=40, height=15, font=("Consolas", 10)
        )
        self.file_text.grid(row=0, column=0, sticky="nsew")

        # Center button
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=0, column=1, sticky="ns", padx=10)

        # Add some vertical spacing
        ttk.Label(button_frame, text="").grid(row=0, column=0, pady=50)

        self.run_button = ttk.Button(
            button_frame,
            text="Run Query",
            command=self.run_query,
            style="Accent.TButton",
        )
        self.run_button.grid(row=1, column=0, pady=10)

        # Progress bar
        self.progress_var = tk.StringVar(value="Ready")
        self.progress_label = ttk.Label(button_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=2, column=0, pady=5)

        self.progress_bar = ttk.Progressbar(
            button_frame, mode="indeterminate", length=100
        )
        self.progress_bar.grid(row=3, column=0, pady=5)

        # Target list section (right)
        target_frame = ttk.LabelFrame(
            input_frame, text="Target List (one target per line)", padding="5"
        )
        target_frame.grid(row=0, column=2, rowspan=2, sticky="nsew", padx=(5, 0))
        target_frame.grid_rowconfigure(0, weight=1)
        target_frame.grid_columnconfigure(0, weight=1)

        self.target_text = scrolledtext.ScrolledText(
            target_frame, wrap=tk.WORD, width=40, height=15, font=("Consolas", 10)
        )
        self.target_text.grid(row=0, column=0, sticky="nsew")

    def setup_results_section(self, parent):
        results_frame = ttk.LabelFrame(parent, text="Results", padding="5")
        results_frame.grid(row=3, column=0, sticky="nsew")
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(0, weight=1)

        # Create a frame for the text widget and scrollbars
        text_frame = ttk.Frame(results_frame)
        text_frame.grid(row=0, column=0, sticky="nsew")
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)

        self.results_text = scrolledtext.ScrolledText(
            text_frame, wrap=tk.WORD, height=12, font=("Consolas", 9), state=tk.DISABLED
        )
        self.results_text.grid(row=0, column=0, sticky="nsew")

        # Clear results button
        clear_frame = ttk.Frame(results_frame)
        clear_frame.grid(row=1, column=0, sticky="ew", pady=(5, 0))

        ttk.Button(clear_frame, text="Clear Results", command=self.clear_results).pack(
            side=tk.RIGHT
        )

    def get_file_list(self) -> List[str]:
        """Extract file list from the text area."""
        content = self.file_text.get("1.0", tk.END).strip()
        if not content:
            return []
        return [line.strip() for line in content.split("\n") if line.strip()]

    def get_target_list(self) -> List[str]:
        """Extract target list from the text area."""
        content = self.target_text.get("1.0", tk.END).strip()
        if not content:
            return []
        return [line.strip() for line in content.split("\n") if line.strip()]

    def append_result(self, text: str):
        """Append text to the results area."""
        self.results_text.config(state=tk.NORMAL)
        self.results_text.insert(tk.END, text + "\n")
        self.results_text.see(tk.END)
        self.results_text.config(state=tk.DISABLED)
        self.root.update_idletasks()

    def clear_results(self):
        """Clear the results area."""
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete("1.0", tk.END)
        self.results_text.config(state=tk.DISABLED)

    def update_progress(self, message: str, show_progress: bool = False):
        """Update progress message and progress bar."""
        self.progress_var.set(message)
        if show_progress:
            self.progress_bar.start()
        else:
            self.progress_bar.stop()
        self.root.update_idletasks()

    def run_query(self):
        """Run the query process in a separate thread."""
        if self.is_processing:
            return

        # Validate inputs
        file_list = self.get_file_list()
        target_list = self.get_target_list()

        if not file_list:
            messagebox.showerror(
                "Error", "Please enter at least one file in the file list."
            )
            return

        if not target_list:
            messagebox.showerror(
                "Error", "Please enter at least one target in the target list."
            )
            return

        # Start processing in a separate thread
        self.is_processing = True
        self.run_button.config(state=tk.DISABLED)

        thread = threading.Thread(
            target=self.process_queries,
            args=(file_list, target_list, self.model_var.get()),
        )
        thread.daemon = True
        thread.start()

    def process_queries(
        self, file_list: List[str], target_list: List[str], model_name: str
    ):
        """Process all queries and update results in real-time."""
        try:
            self.update_progress(f"Processing {len(target_list)} targets...", True)
            self.append_result(f"=== Starting Query Process ===")
            self.append_result(f"Model: {model_name}")
            self.append_result(f"Files: {len(file_list)} files")
            self.append_result(f"Targets: {len(target_list)} targets")
            self.append_result("")

            for i, target in enumerate(target_list, 1):
                self.update_progress(
                    f"Processing target {i}/{len(target_list)}: {target[:30]}...", True
                )
                self.append_result(f"--- Target {i}/{len(target_list)}: {target} ---")

                try:
                    result = query_single(
                        file_list=file_list, keyword=target, model_name=model_name
                    )

                    matches = result.get("matches", [])
                    if matches:
                        self.append_result(f"Found {len(matches)} matches:")
                        for match in matches:
                            self.append_result(
                                f"  • {match['matched_file']} (Score: {match['score']})"
                            )
                            self.append_result(f"    Reason: {match['reason']}")
                    else:
                        self.append_result("  No matches found (score >= 60)")

                except Exception as e:
                    self.append_result(f"  Error processing target: {str(e)}")

                self.append_result("")

            self.append_result("=== Query Process Completed ===")
            self.update_progress("Completed", False)

        except Exception as e:
            self.append_result(f"Fatal error: {str(e)}")
            self.update_progress("Error occurred", False)
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

        finally:
            self.is_processing = False
            self.run_button.config(state=tk.NORMAL)


def main():
    root = tk.Tk()
    app = MarketFileMatcherGUI(root)

    # Add some sample data for testing
    sample_files = """常用材料/营业执照副本.pdf
常用材料/组织机构代码证.pdf
常用材料/税务登记证.pdf
常用材料/开户许可证.pdf
常用材料/公司章程.pdf
常用材料/验资报告.pdf
常用材料/审计报告.pdf
常用材料/财务报表.xlsx
常用材料/银行流水.pdf
常用材料/征信报告.pdf"""

    sample_targets = """营业执照正/副本复印件
组织机构代码证
税务登记证明
银行开户许可证"""

    app.file_text.insert("1.0", sample_files)
    app.target_text.insert("1.0", sample_targets)

    root.mainloop()


if __name__ == "__main__":
    main()
