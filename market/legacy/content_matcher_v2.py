from docx import Document
from utils.doc_extract_util import extract_text
from utils.llm import get_model
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from typing import Dict, List, Literal
from langchain_core.output_parsers import PydanticOutputParser


class MatchedContent(BaseModel):
    type: Literal["simple", "medium", "complex"] = Field(
        description="提取文本的长度/复杂度"
    )
    answer: str = Field(description="问题答案")
    score: int = Field(description="匹配得分, 取值范围为0-100，得分越高，匹配度越高")
    reason: List[str] = Field(description="匹配原因, 说明匹配原因")


class MatchedContentList(BaseModel):
    matches: List[MatchedContent] = Field(description="匹配结果列表")


def query(
    text: str, target: str, debug: bool = False, model_name: str = "DeepSeek-V3"
) -> Dict[str, str]:
    parser = PydanticOutputParser(pydantic_object=MatchedContentList)
    prompt = f"""
你是一个专业的知识库问答助手。请根据以下知识库内容，回答用户的问题。

知识库内容:
{text}

用户问题: {target}

请根据知识库内容提供准确的回答。如果知识库中没有相关信息，请回答"抱歉，知识库中没有相关信息。"

回答要求：
1. 直接回答问题，不需要解释你是如何找到答案的
2. 如果知识库中有多个相关信息，请综合这些信息给出完整回答
3. 保持回答的准确性，不要添加知识库中没有的信息
4. 如果答案是表格形式，请以易于阅读的方式呈现
5. 如果答案是"是"或"否"这样的简短回答，可以适当补充相关信息，但要确保准确性

{parser.get_format_instructions()}
"""
    model = get_model(model_name)
    model = model.with_structured_output(MatchedContentList)

    input = [HumanMessage(prompt)]
    if model_name == "Qwen3-235B-A22B":
        input.insert(0, SystemMessage("/no_think"))

    response = model.invoke(input)
    return response.model_dump()


def extract_text_from_docx(doc_file: str):
    document = Document(doc_file)
    elements = document.element.body.inner_content_elements
    text_list = [extract_text(x) for x in elements]
    return text_list


def query_single(
    doc_file: str,
    keyword: str,
    debug: bool = False,
    model_name: str = "DeepSeek-V3",
    min_score: int = 60,
):
    result = []
    text_list = extract_text_from_docx(doc_file)
    overlap_size = 5000
    start = 0
    max_token = 20480
    text = ""
    while start < len(text_list):
        text = text[-overlap_size:]
        while len(text) < max_token and start < len(text_list):
            text = text + "\n" + text_list[start]
            start = start + 1
        response = query(text, keyword, debug, model_name)
        result.extend(response['matches'])
    result = [x for x in result if x['score'] >= min_score]
    return result


if __name__ == "__main__":
    result = query_single(
        "./tests/content_match/衍复尽调内部资料库 2025年一季度末.docx",
        "公司法定中文名称",
        debug=True,
        model_name="Qwen3-235B-A22B",
    )
    print(result)
