import asyncio
from llm_for_a.market.qa_agent.models.feishu_knowledge_base import FeishuKnowledgeBase
from llm_for_a.market.qa_agent.services.knowledge_base_service import KnowledgeBaseService
import toml
import os

if __name__ == "__main__":
    # 自动定位 config.toml 路径
    config_path = os.path.join(os.path.dirname(__file__), "config.toml")
    if not os.path.exists(config_path):
        # 尝试从项目根目录加载
        config_path = os.path.join(os.path.dirname(__file__), "../../config.toml")
    config = toml.load(config_path)
    service = KnowledgeBaseService(config)
    print("已加载知识库:", list(service.knowledge_bases.keys()))
    # 打印所有知识库详细信息
    for name, kb in service.knowledge_bases.items():
        print(f"{name}: {kb.to_dict()}")
    # 测试读取飞书知识库内容
    if any(kb.to_dict().get("type") == "feishu" for kb in service.knowledge_bases.values()):
        for name, kb in service.knowledge_bases.items():
            if kb.to_dict().get("type") == "feishu":
                print(f"\n飞书知识库 {name} 内容预览:")
                content = kb.read(with_file_name=True)
                print(content[:500])
        # 测试搜索
        async def run_search():
            results = await service.search_knowledge("注册资本", top_k=3)
            print("\n飞书知识库搜索结果:")
            for result in results:
                print(f"文档ID: {result['doc_id']}")
                print(f"内容: {result['content'][:200]}\n")
        asyncio.run(run_search())
    else:
        print("未检测到飞书知识库，请检查配置和加载逻辑。")
