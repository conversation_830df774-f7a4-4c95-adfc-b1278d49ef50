import json
import logging
import os
import sys
import traceback

from flask import request, Flask, jsonify, Response, stream_with_context
from flask_cors import CORS
from pydantic import BaseModel

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.llm import get_model

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 知识库路径
KB_DIR = "./knowledge_base.py/尽调报告/knowledge_bases"


# 加载知识库
def load_knowledge_bases():
    knowledge_bases = {}
    try:
        if not os.path.exists(KB_DIR):
            logger.warning(f"知识库目录不存在: {KB_DIR}")
            return knowledge_bases

        for file in os.listdir(KB_DIR):
            if file.endswith(".json"):
                kb_name = file.replace(".json", "")
                kb_path = os.path.join(KB_DIR, file)

                try:
                    with open(kb_path, 'r', encoding='utf-8') as f:
                        knowledge_bases[kb_name] = json.load(f)
                    logger.info(f"加载知识库: {kb_name}, 包含 {len(knowledge_bases[kb_name])} 个query-answer对")
                except Exception as e:
                    logger.error(f"加载知识库 {kb_name} 时出错: {str(e)}")
                    logger.error(traceback.format_exc())

        return knowledge_bases
    except Exception as e:
        logger.error(f"加载知识库时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {}


app = Flask(__name__)
CORS(app, supports_credentials=True, origins="*")  # 放宽为所有来源


@app.route('/api/get_knowledge', methods=['POST'])
def get_knowledge(question: str):
    """
    该方法用于为前端提供知识库。用户可以通过提供关键字来过滤知识库。
    """
    knowledges = load_knowledge_bases()
    knowledges_str = json.dumps(knowledges, ensure_ascii=False)
    return knowledges_str


@app.route('/api/fill', methods=['POST'])
def fill():
    model_name = request.json.get('model_name', "Qwen3-30B-A3B")
    temperature = request.json.get('temperature', 0)
    model = get_model(model_name, model_source="local", temperature=temperature)

    class Answer(BaseModel):
        answer: str
    model = model.with_structured_output(Answer)
    # TODO: 调用大模型API和问答接口
    context = request.json.get('context')
    knowledges = load_knowledge_bases()
    knowledges_str = json.dumps(knowledges['代销机构资管部门'], ensure_ascii=False)

    prompt = f"""/no_think
    你是一个专业的word文档编辑助手，用户会以html的形式传入上下文，请根据上下文判断占位符"{{question}}"位置应该填写的内容。

    要求:
    1. 以json形式返回结果，json格式为：{{"answer": "填写的内容"}}

    例如：
    User: <p>1+1={{question}}</p>
    则应该直接返回：{{"answer": "2"}}

    请根据以上规则，回答用户的问题。
    知识库：
    {knowledges_str}

    上下文：
    {context}

    """
    answer = model.invoke(prompt)
    return jsonify(answer.model_dump())


@app.route('/api/fill_stream', methods=['POST'])
def fill_stream():
    model_name = request.json.get('model_name', "Qwen3-30B-A3B")
    temperature = request.json.get('temperature', 0)
    model = get_model(model_name, model_source="local", temperature=temperature)
    context = request.json.get('context')
    knowledges = load_knowledge_bases()
    knowledges_str = json.dumps(knowledges['代销机构资管部门'], ensure_ascii=False)
    prompt = f"""/no_think
    你是一个专业的word文档编辑助手，用户会以html的形式传入上下文，请根据上下文判断占位符"{{question}}"位置应该填写的内容。

    要求:
    1. 直接返回填写的内容，不需要json格式

    例如：
    User: <p>1+1={{question}}</p>
    则应该直接返回：2

    请根据以上规则，回答用户的问题。
    知识库：
    {knowledges_str}

    上下文：
    {context}
    """

    def generate_test():
        # iterator = agent.model.stream(prompt)
        iterator = [{"content": x} for x in "衍复投资管理有限公司"]
        for chunk in iterator:
            print(chunk["content"])
            yield f"data: {json.dumps({'content': chunk["content"]}, ensure_ascii=False)}\n\n"

    def generate():
        iterator = model.stream(prompt)
        for chunk in iterator:
            if chunk.content == "" or chunk.content == "\n\n" or chunk.content == "</think>" or chunk.content == "<think>":
                continue

            print(chunk.content)
            yield f"data: {json.dumps({'content': chunk.content}, ensure_ascii=False)}\n\n"

    return Response(stream_with_context(generate()), mimetype='text/event-stream')


if __name__ == '__main__':
    app.run(host="0.0.0.0", port=5001)