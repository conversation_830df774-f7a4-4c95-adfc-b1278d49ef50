import json
import logging
import os
import sys
import toml
import traceback
import ssl

from flask import Flask, Response, jsonify, request, stream_with_context, make_response, render_template, send_from_directory
from flask_cors import CORS
from langchain_core.output_parsers import JsonOutputParser
from openai import APIStatusError
from pathlib import Path
import importlib.util

# 添加项目根目录到 Python 路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from llm_for_a.market.qa_agent.services.knowledge_base_service import KnowledgeBaseService
from llm_for_a.market.qa_agent.services.llm_service import LLMService
from llm_for_a.market.qa_agent.routes.feishu_auth import feishu_auth_bp
from llm_for_a.market.qa_agent.services.feishu_api import feishu_api
from llm_for_a.market.qa_agent.services.feishu_tree_kb import feishu_tree_kb

# 动态导入 word-auto-fill 中的 kb_bp
kb_path = os.path.join(root_dir, 'llm_for_a', 'market', 'word-auto-fill', 'src', 'api', 'knowledge_base.py')
spec = importlib.util.spec_from_file_location("kb_module", kb_path)
kb_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(kb_module)
kb_bp = kb_module.kb_bp

from llm_for_a.table_processor.main import process_document_with_queries
from typing import List, Tuple, Union


# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 获取配置文件的绝对路径
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.toml")
logger.info(f"加载配置文件: {config_path}")

try:
    config = toml.load(config_path)
    logger.info("配置文件加载成功")
except Exception as e:
    logger.error(f"配置文件加载失败: {str(e)}")
    sys.exit(1)

# 初始化服务 - 使用异常处理确保即使知识库加载失败也不会影响其他功能
try:
    kb_service = KnowledgeBaseService(config)
      # 打印已加载的知识库信息
    logger.info("========== 已加载的知识库列表 ==========")
    for name, kb in kb_service.knowledge_bases.items():
        try:
            # 检查知识库对象是否有效
            if hasattr(kb, 'to_dict') and callable(getattr(kb, 'to_dict')):
                kb_info = kb.to_dict()
                logger.info(f"知识库名称: {name}")
                logger.info(f"  - 类型: {kb_info.get('type', 'unknown')}")
                logger.info(f"  - 路径: {kb_info.get('path', 'unknown')}")
                logger.info(f"  - Token数量: {kb_info.get('token_size', 0)}")
                logger.info(f"  - 文档数量: {kb_info.get('doc_count', 0)}")
                logger.info("----------------------------------------")
            else:
                logger.warning(f"知识库 {name} 对象无效，跳过")
        except Exception as e:
            logger.error(f"获取知识库 {name} 信息时出错: {str(e)}")
            continue
except Exception as e:
    logger.error(f"初始化知识库服务失败: {str(e)}")
    logger.error(traceback.format_exc())
    logger.warning("将继续使用空的知识库服务，某些功能可能受限")
    # 创建一个空的知识库服务，确保其他功能不受影响
    kb_service = KnowledgeBaseService({"knowledge_bases": {}}, config_dir=os.path.dirname(config_path))

llm_service = LLMService(config)
llm_service.load_llms()

static_folder = Path(__file__).parent.parent.resolve() / "word-auto-fill" / "dist"
app = Flask(__name__, static_url_path="", static_folder=static_folder)
CORS(app, supports_credentials=True)  # 启用全局CORS，允许携带凭证

# Register blueprints - 确保正确注册路由
app.register_blueprint(feishu_auth_bp)
app.register_blueprint(kb_bp, url_prefix='/knowledge_base')
app.register_blueprint(feishu_api)  # 注册飞书API蓝图
app.register_blueprint(feishu_tree_kb)  # 注册飞书树状知识库API蓝图


def fill_knowledge_str(prompt: str, kl_ids: List[str]) -> str:
    knowledge_str = kb_service.read_knowledge_base_multiple(
        kl_ids, with_file_name=False
    )
    if isinstance(knowledge_str, str):
        prompt = prompt.replace("{knowledges_str}", knowledge_str)
    return prompt


@app.route("/api/word_analyze", methods=["GET"])
def word_analyze():
    try:
        file_path = request.args.get("file_path")
        if not file_path:
            return jsonify({"error": "Invalid request data"}), 400

        if not os.path.exists(file_path):
            return jsonify({"error": "File not found"}), 404
            
        output_path = os.path.join(os.path.dirname(file_path), os.path.basename(file_path).replace(".docx", "_with_placeholder.docx"))

        mode = request.args.get("mode", "balanced")

        logger.info(f"开始处理文档: {file_path}")
        logger.info(f"输出路径: {output_path}")
        logger.info(f"处理模式: {mode}")
        
        # 确保独立处理文档，即使出错也不影响响应
        try:
            result = process_document_with_queries(file_path, output_path, mode)
        except Exception as e:
            logger.error(f"处理文档时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                "data": {
                    "success": False,
                    "error": str(e),
                    "message": "文档处理失败，请检查文档格式或内容"
                }
            }), 200  # 返回200而不是500，避免被前端视为网络错误
            
        # 构造成功响应
        return jsonify(
            {
                "data": {
                    "success": result["success"],
                    "file_path": os.path.join(
                        os.path.dirname(
                            os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                        ),
                        result["file_path"],
                    ),
                }
            }
        )
    except Exception as e:
        # 捕获所有异常，确保返回有效响应而非500错误
        logger.error(f"word_analyze API出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "data": {
                "success": False,
                "error": "服务器处理错误",
                "message": str(e)
            }
        }), 200  # 返回200而不是500，避免被前端视为网络错误


@app.route("/api/query", methods=["POST"])
def query() -> Union[Response, Tuple[Response, int]]:
    """
    kl_ids: knowledge_list 返回的 key
    llm_config: get_model 的参数, dict 类型
    """
    if not request.json:
        return jsonify({"error": "Invalid JSON data"}), 400

    prompt = request.json.get("prompt")
    kl_ids = request.json.get("kl_ids")
    llm_config = request.json.get("llm_config")
    streamable = request.json.get("streamable", False)

    if not prompt or not kl_ids or not llm_config:
        return jsonify({"error": "Missing required parameters"}), 400

    prompt = fill_knowledge_str(prompt, kl_ids)
    model = llm_service.get_model(llm_config)

    if streamable:

        def generate():
            iterator = model.stream(prompt)
            for chunk in iterator:
                if (
                    chunk.content == ""
                    or chunk.content == "\n\n"
                    or chunk.content == "</think>"
                    or chunk.content == "<think>"
                ):
                    continue

                print(chunk.content)
                yield f"data: {json.dumps({'content': chunk.content}, ensure_ascii=False)}\n\n"

        return Response(stream_with_context(generate()), mimetype="text/event-stream")
    else:
        result = model.invoke(prompt)
        json_parser = JsonOutputParser()
        result = result.content.split("</think>")[-1]
        result = json_parser.parse(result)
        return jsonify({"data": result})


@app.route("/api/query_preview", methods=["POST"])
def query_preview() -> Union[Response, Tuple[Response, int]]:
    """
    kl_ids: knowledge_list 返回的 key
    """
    if not request.json:
        return jsonify({"error": "Invalid JSON data"}), 400

    prompt = request.json.get("prompt")
    kl_ids = request.json.get("kl_ids")

    if not prompt or not kl_ids:
        return jsonify({"error": "Missing required parameters"}), 400

    prompt = fill_knowledge_str(prompt, kl_ids)
    return jsonify({"data": {"prompt": prompt}})


@app.route('/api/llm_model_list', methods=['GET'])
def llm_model_list():
    # 返回对象格式，key为模型id，value为模型配置
    llms_config = config.get('llms', {})
    result = {}
    for key, value in llms_config.items():
        if isinstance(value, dict) and 'model' in value:
            result[key] = {
                'model': value.get('model', key),
                'api_key': value.get('api_key', ''),
                'base_url': value.get('base_url', ''),
                'max_tokens': value.get('max_tokens', 0)
            }
    return jsonify(result)

@app.route('/api/knowledge_list', methods=['GET'])
def knowledge_list():
    # 每次请求都热加载 config.toml，确保知识库列表和 token_size 最新
    global kb_service
    try:
        config = toml.load(config_path)
        kb_service = KnowledgeBaseService(config)
    except Exception as e:
        logger.error(f"知识库热加载失败: {str(e)}")
    result = {}
    for name, kb in kb_service.knowledge_bases.items():
        if hasattr(kb, 'to_dict') and callable(getattr(kb, 'to_dict')):
            info = kb.to_dict()
            # 新增文档总长度字段
            doc_length = 0
            if hasattr(kb, 'get_document_content') and callable(getattr(kb, 'get_document_content')):
                try:
                    # 获取所有文档内容长度
                    if hasattr(kb, '_doc_ids') and kb._doc_ids:
                        for doc_id in kb._doc_ids:
                            content = kb.get_document_content(doc_id)
                            if content:
                                doc_length += len(content)
                except Exception as e:
                    logger.warning(f"统计知识库 {name} 文档长度时出错: {str(e)}")
            result[name] = {
                "type": info.get("type", ""),
                "path": info.get("path", ""),
                "token_size": info.get("token_size", 0),
                "doc_length": doc_length
            }
    return jsonify({"data": result})


@app.route("/api/refresh_knowledge_bases", methods=["POST", "OPTIONS"])
def refresh_knowledge_bases():
    """Refresh knowledge bases after Feishu authorization"""
    # 处理CORS预检请求
    if request.method == "OPTIONS":
        response = make_response()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add("Access-Control-Allow-Headers", "Content-Type,Authorization")
        response.headers.add("Access-Control-Allow-Methods", "POST,OPTIONS")
        return response
    try:
        # 获取并验证授权令牌
        auth_header = request.headers.get('Authorization', '')
        access_token = None
        if auth_header.startswith('Bearer '):
            access_token = auth_header[7:]
            logger.info(f"收到有效的授权头: Bearer {access_token[:5]}...")
        else:
            logger.warning("未收到有效的授权头，将尝试加载本地知识库")
            
        # 重新加载知识库服务
        logger.info("开始重新加载知识库服务...")
        global kb_service
        
        # 如果收到了有效的access_token，则更新配置中的token
        if access_token:
            logger.info(f"使用前端提供的access_token初始化飞书知识库...")
            # 临时更新配置
            if 'feishu' in config['knowledge_bases']:
                config['knowledge_bases']['feishu']['user_access_token'] = access_token
                logger.info("已更新飞书知识库访问令牌")
                # 自动写回 config.toml
                try:
                    with open(config_path, 'w', encoding='utf-8') as f:
                        toml.dump(config, f)
                    logger.info(f"已自动保存 access_token 到 {config_path}")
                except Exception as e:
                    logger.error(f"写入 config.toml 失败: {str(e)}")
        kb_service = KnowledgeBaseService(config)
        logger.info(f"知识库服务重新加载完成，加载了 {len(kb_service.knowledge_bases)} 个知识库")
        
        # 对于飞书知识库，强制重新计算token数量
        for name, kb in kb_service.knowledge_bases.items():
            if hasattr(kb, 'recalculate_token_count') and callable(getattr(kb, 'recalculate_token_count')):
                try:
                    logger.info(f"正在重新计算知识库 {name} 的token数量...")
                    token_count = kb.recalculate_token_count()
                    logger.info(f"知识库 {name} token数量重新计算完成: {token_count}")
                except Exception as e:
                    logger.error(f"重新计算知识库 {name} token数量时出错: {str(e)}")
        
        # 获取知识库信息
        kb_info = []
        for name, kb in kb_service.knowledge_bases.items():
            try:                # 检查知识库对象是否有效
                if hasattr(kb, 'to_dict') and callable(getattr(kb, 'to_dict')):
                    info = kb.to_dict()
                    token_size = info.get("token_size", 0)
                    doc_count = info.get("doc_count", 0)
                    
                    kb_info.append({
                        "name": name,
                        "type": info.get("type", "unknown"),
                        "tokenCount": token_size,
                        "docCount": doc_count
                    })
                    
                    logger.info(f"知识库 {name}: {token_size} tokens, {doc_count} 文档")
                else:
                    # 如果知识库对象无效，记录警告但不添加到列表中
                    logger.warning(f"知识库 {name} 对象无效，跳过")
            except Exception as e:
                logger.error(f"获取知识库 {name} 信息时出错: {str(e)}")

        # 检查是否有非零token的知识库
        has_valid_kb = any(kb.get("tokenCount", 0) > 0 for kb in kb_info)
        if not has_valid_kb:
            logger.warning("警告: 所有知识库的token数量都为0")
        
        response = jsonify({
            "success": True,
            "knowledgeBases": kb_info,
            "totalTokenCount": sum(kb.get("tokenCount", 0) for kb in kb_info),
            "authStatus": "valid" if access_token else "missing"
        })
        
        # 设置CORS头部，确保跨域通信正常
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        
        return response

    except Exception as e:
        logger.error(f"刷新知识库时出错: {str(e)}")
        logger.error(traceback.format_exc())
        error_response = jsonify({
            "success": False,
            "error": str(e)
        })
        
        # 即使是错误也添加CORS头，确保前端可以正确接收响应
        error_response.headers.add('Access-Control-Allow-Origin', '*')
        return error_response, 500


@app.route("/feishu/auth/callback", methods=["GET"])
def feishu_callback_root():
    """根路径的飞书回调处理 - 重定向到正确的回调处理器"""
    # 使用已经导入的模块，避免重复导入
    from llm_for_a.market.qa_agent.routes.feishu_auth import feishu_callback
    return feishu_callback()
    
@app.route("/feishu_oauth_callback", methods=["GET"])
def feishu_oauth_callback():
    """兼容 Feishu OAuth 回调路径，统一处理"""
    # 复用原有 /feishu/auth/callback 的处理逻辑
    from llm_for_a.market.qa_agent.routes.feishu_auth import feishu_callback
    return feishu_callback()


@app.route("/feishu/tree_selector", methods=["GET"])
def feishu_tree_selector():
    """飞书知识库树状结构选择器页面"""
    return render_template("feishu_tree_selector.html")


@app.route("/feishu/auth/callback_debug", methods=["GET"])
def feishu_callback_debug():
    """调试用路由 - 输出所有请求信息"""
    result = {
        "args": dict(request.args),
        "headers": dict(request.headers),
        "path": request.path,
        "url": request.url,
        "method": request.method
    }
    return jsonify(result)


@app.route('/api/config/feishu', methods=['GET'])
def get_feishu_config():
    # 使用当前文件夹下的config.toml
    config_path = os.path.join(os.path.dirname(__file__), "config.toml")
    if not os.path.exists(config_path):
        return jsonify({"error": "配置文件不存在", "config_path": config_path}), 500
    config = toml.load(config_path)
    feishu_cfg = config.get("knowledge_bases", {}).get("feishu", {})
    return jsonify({
        "user_access_token": feishu_cfg.get("user_access_token"),
        "folder_token": feishu_cfg.get("folder_token")
    })


@app.errorhandler(Exception)
def handle_error(error):
    logger.error(f"发生错误: {str(error)}")
    logger.error(f"错误详情: {traceback.format_exc()}")

    if isinstance(error, FileNotFoundError):
        return jsonify({"error": "文件未找到", "message": str(error)}), 404

    if isinstance(error, APIStatusError):
        return jsonify(error.body), error.status_code

    return jsonify({"error": "服务器内部错误", "message": str(error)}), 500


if __name__ == "__main__":
    # 只用 http 启动，便于本地调试和代理
    app.run(host='localhost', port=5000, debug=True)
