from flask import Blueprint, request, jsonify, current_app
import lark_oapi as lark
from llm_for_a.market.qa_agent.models.feishu_tree_utils import fetch_feishu_tree
from llm_for_a.market.qa_agent.models.feishu_knowledge_base import FeishuKnowledgeBase
import asyncio
import os
import toml
import logging
import datetime

feishu_api = Blueprint('feishu_api', __name__)

def get_config():
    """从配置文件中读取飞书配置"""
    try:
        # 只从 market/qa_agent/ 目录下读取 config.toml
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.toml')
        if os.path.exists(config_path):
            config = toml.load(config_path)
            return config
        else:
            logging.warning(f"配置文件不存在: {config_path}")
            return {}
    except Exception as e:
        logging.error(f"读取配置文件出错: {str(e)}")
        return {}

def get_feishu_token():
    """从配置文件中获取飞书访问令牌和文件夹token"""
    config = get_config()
    feishu_config = config.get('knowledge_bases', {}).get('feishu', {})
    folder_token = feishu_config.get('folder_token', '')
    user_access_token = feishu_config.get('user_access_token', '')
    return folder_token, user_access_token

@feishu_api.route('/api/feishu/tree', methods=['GET'])
def get_feishu_tree():
    """获取飞书文件夹的树状结构或用户所有文件的列表
    
    可以通过URL参数传递:
    folder_token - 文件夹token，不提供则获取用户的所有文件
    user_access_token - 用户访问令牌（必需）
    include_types - 逗号分隔的文件类型列表，例如"doc,sheet,docx"
    exclude_types - 逗号分隔的要排除的文件类型列表
    max_depth - 最大递归深度，默认为10
    """# 优先从请求参数中获取，如果没有则从配置文件中读取
    folder_token = request.args.get('folder_token')
    user_access_token = request.args.get('user_access_token')

    # 优先用请求参数，其次查 feishu_tree 配置（不查 feishu 普通库的 folder_token），都没有就查根目录
    if not folder_token:
        config = get_config()
        feishu_tree_config = config.get('knowledge_bases', {}).get('feishu_tree', {})
        folder_token = feishu_tree_config.get('folder_token', None)
        if not folder_token:
            folder_token = None  # 明确为 None，查根目录
    logging.info(f"[feishu_tree] 请求参数: folder_token={folder_token}, user_access_token={'***' if user_access_token else None}")
    if not user_access_token:
        _, config_user_access_token = get_feishu_token()
        user_access_token = config_user_access_token
    if not user_access_token:
        logging.warning("[feishu_tree] 未提供有效的user_access_token，返回400")
        return jsonify({"error": "未提供有效的user_access_token"}), 400
    include_types_str = request.args.get('include_types')
    exclude_types_str = request.args.get('exclude_types')
    max_depth_str = request.args.get('max_depth')
    include_types = set(include_types_str.split(',')) if include_types_str else None
    exclude_types = set(exclude_types_str.split(',')) if exclude_types_str else None
    max_depth = int(max_depth_str) if max_depth_str and max_depth_str.isdigit() else 10
    logging.info(f"[feishu_tree] include_types={include_types}, exclude_types={exclude_types}, max_depth={max_depth}")
    client = lark.Client.builder().enable_set_token(True).log_level(lark.LogLevel.INFO).build()
    try:
        tree = fetch_feishu_tree(
            client, 
            folder_token, 
            user_access_token,
            max_depth=max_depth,
            include_types=include_types,
            exclude_types=exclude_types
        )
        logging.info(f"[feishu_tree] tree type: {type(tree)}, tree len: {len(tree) if tree else 0}")
        if tree and isinstance(tree, list) and len(tree) > 0:
            logging.info(f"[feishu_tree] tree sample: {tree[0]}")
        if tree is None:
            logging.warning("[feishu_tree] 获取文档树失败，返回401")
            return jsonify({"error": "获取文档树失败，请检查token是否有效"}), 401
        response_data = {
            "tree": tree,
            "metadata": {
                "total_files": sum(1 for item in tree if item["type"] != "folder"),
                "total_folders": sum(1 for item in tree if item["type"] == "folder"),
                "root_folder_token": folder_token,
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        logging.info(f"[feishu_tree] response_data keys: {list(response_data.keys())}, metadata: {response_data['metadata']}")
        return jsonify(response_data)
    except Exception as e:
        logging.error(f"获取文档树出错: {str(e)}")
        return jsonify({"error": f"获取文档树出错: {str(e)}"}), 500

@feishu_api.route('/api/feishu/file_content', methods=['POST'])
def get_feishu_file_content():
    """获取单个飞书文件的内容（markdown格式）
    
    需要在请求体中提供file_token，user_access_token是必须的，folder_token是可选的
    - file_token：要获取的文件ID
    - user_access_token：飞书用户访问令牌（必须）
    - folder_token：飞书文件夹token（可选，不提供也可以正常获取文件内容）
    """
    try:
        data = request.json
        file_token = data.get('file_token')
        
        # 如果没有提供file_token，返回错误
        if not file_token:
            return jsonify({"error": "未提供file_token"}), 400
        
        # 优先从请求参数中获取，如果没有则从配置文件中读取
        folder_token = data.get('folder_token')
        user_access_token = data.get('user_access_token')
        
        if not folder_token or not user_access_token:
            config_folder_token, config_user_access_token = get_feishu_token()
            folder_token = folder_token or config_folder_token
            user_access_token = user_access_token or config_user_access_token
          # 检查user_access_token是否有效
        if not user_access_token:
            return jsonify({"error": "未提供有效的user_access_token"}), 400
        
        # 创建临时知识库实例并获取文件内容
        # folder_token是可选的，只用于知识库实例的标识，不提供也能获取文件内容
        kb = FeishuKnowledgeBase(id='tmp', folder_token=folder_token or 'root', user_access_token=user_access_token)
        content = asyncio.run(kb.get_document_content(file_token))
        
        if content is None:
            return jsonify({"error": "获取文件内容失败，请检查token和file_token是否有效"}), 404
        
        return jsonify({'content': content})
    except Exception as e:
        logging.error(f"获取文件内容出错: {str(e)}")
        return jsonify({"error": f"获取文件内容出错: {str(e)}"}), 500
