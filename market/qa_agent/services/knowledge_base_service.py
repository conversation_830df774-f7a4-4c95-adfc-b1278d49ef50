# -*- coding: utf-8 -*-
import glob
import toml
import logging
import os
from typing import Dict, List, Optional
from models.knowledge_base_model import LocalFileKnowledgeBase
# FeishuKnowledgeBase 应该从 feishu_knowledge_base.py 导入
from models.feishu_knowledge_base import FeishuKnowledgeBase

logger = logging.getLogger(__name__)


class KnowledgeBaseService:
    def __init__(self, config: Dict, config_dir: str = None):
        self.config = config
        self.config_dir = config_dir or os.getcwd()
        self.knowledge_bases = {}
        # 自动加载知识库
        self.load_knowledge_bases()

    def load_knowledge_bases(self) -> Dict:
        knowledge_bases = self.config.get("knowledge_bases", {})
        
        for kb in knowledge_bases:
            kb_config = knowledge_bases[kb]
            logger.info(f"加载知识库 {kb}, 配置: {kb_config}")
            
            try:
                if kb_config["type"] == "local_file":
                    knowledge_base = LocalFileKnowledgeBase(
                        id=kb,
                        path=kb_config["path"],
                        config_dir=self.config_dir
                    )
                    self.knowledge_bases[kb] = knowledge_base
                    logger.info(f"本地知识库 {kb} 加载成功")
                elif kb_config["type"] == "feishu":
                    knowledge_base = FeishuKnowledgeBase(
                        id=kb,
                        folder_token=kb_config["folder_token"],
                        user_access_token=kb_config.get("user_access_token")
                    )
                    self.knowledge_bases[kb] = knowledge_base
                    logger.info(f"飞书知识库 {kb} 加载成功")
                else:
                    logger.warning(f"不支持的知识库类型: {kb_config['type']}")
            except Exception as e:
                logger.error(f"知识库 {kb} 加载失败: {str(e)}")
                # 不要在字典中存储错误字符串，而是跳过失败的知识库
                continue

        return self.knowledge_bases

    def read_knowledge_base(self, kb_id: str, with_file_name: bool = False):
        if kb_id not in self.knowledge_bases:
            raise ValueError(f"Knowledge base {kb_id} not found")
            
        kb = self.knowledge_bases[kb_id]
        
        # 检查是否为错误信息字符串
        if isinstance(kb, str):
            return f"错误: {kb}"
            
        if isinstance(kb, LocalFileKnowledgeBase):
            return kb.read(with_file_name=with_file_name)
        elif isinstance(kb, FeishuKnowledgeBase):
            return kb.read(with_file_name=with_file_name)
        else:
            return ""

    def read_knowledge_base_multiple(self, kb_ids: List[str], with_file_name: bool = False):
        knowledge_str = ""
        for kb_id in kb_ids:
            if kb_id not in self.knowledge_bases:
                logger.warning(f"知识库 {kb_id} 不存在")
                continue
                
            kb = self.knowledge_bases[kb_id]
            
            # 检查是否为错误信息字符串
            if isinstance(kb, str):
                logger.warning(f"跳过失败的知识库 {kb_id}: {kb}")
                continue
            
            try:
                knowledge_str += "\n"
                knowledge_str += "--------------------------------"
                knowledge_str += "Knowledge Base: " + kb_id
                knowledge_str += "--------------------------------"
                knowledge_str += "\n"
                knowledge_str += self.read_knowledge_base(kb_id, with_file_name=with_file_name)
                knowledge_str += "\n"
            except Exception as e:
                logger.error(f"读取知识库 {kb_id} 时出错: {str(e)}")
                continue
                
        return knowledge_str
