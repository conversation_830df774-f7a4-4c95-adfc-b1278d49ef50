import toml
from typing import Dict
from langchain.chat_models import init_chat_model


class LLMService:
    def __init__(self, config: Dict):
        self.config = config
        self.llms = {}

    def load_llms(self) -> Dict:
        llms = self.config["llms"]
        for llm in llms:
            self.llms[llm] = llms[llm]
        return self.llms

    def get_model_by_id(self, llm_id: str) -> Dict:
        return init_chat_model(
            model=self.llms[llm_id]["model"],
            model_provider="openai",
            base_url=self.llms[llm_id]["base_url"],
            api_key=self.llms[llm_id]["api_key"],
            temperature=self.llms[llm_id].get("temperature", 0),
        )

    def get_model(self, llm_config: Dict):
        return init_chat_model(
            model=llm_config["model"],
            model_provider="openai",
            base_url=llm_config["base_url"],
            api_key=llm_config["api_key"],
            temperature=llm_config.get("temperature", 0),
        )

    def query(self, llm_config: Dict, prompt: str) -> Dict:
        model = self.get_model(llm_config)
        return model.invoke(prompt)


if __name__ == "__main__":
    config = toml.load("market/config.toml")
    llm_service = LLMService(config)
    llm_service.load_llms()
    print(llm_service.llms)
