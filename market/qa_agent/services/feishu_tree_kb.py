"""
基于飞书树状结构选择文件，构建知识库的API
"""
import asyncio
import logging
import os
import sys
from typing import List, Dict, Any, Optional
import json
from flask import Blueprint, request, jsonify, current_app
import tomlkit
import re
import requests

# 添加项目根目录到 Python 路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from llm_for_a.market.qa_agent.models.feishu_knowledge_base import FeishuKnowledgeBase
from llm_for_a.market.qa_agent.models.feishu_tree_utils import fetch_feishu_tree
import lark_oapi as lark

# 创建蓝图
feishu_tree_kb = Blueprint('feishu_tree_kb', __name__)

# 设置日志
logger = logging.getLogger(__name__)

def get_feishu_token_type(token: str, user_access_token: str) -> str:
    """判断token类型: 'folder'/'file'/'doc'/'unknown'，支持云空间token，详细日志"""
    headers = {"Authorization": f"Bearer {user_access_token}"}
    url_drive = "https://open.feishu.cn/open-apis/drive/v1/files/meta"
    params_drive = {"file_token": token}
    try:
        resp = requests.get(url_drive, headers=headers, params=params_drive)
        if resp.status_code == 200:
            t = resp.json().get("data", {}).get("type")
            logger.info(f"[get_feishu_token_type] token={token} drive_type={t}")
            if t == "folder":
                return "folder"
            elif t in ("docx", "sheet", "bitable", "file", "pdf", "mindnote", "slide", "doc"):
                return "file"
        # 2. 云空间文件夹下的文件列表接口（判断是否为文件夹token）
        url_list = "https://open.feishu.cn/open-apis/drive/v1/files"
        params_list = {"folder_token": token, "page_size": 1}
        resp2 = requests.get(url_list, headers=headers, params=params_list)
        if resp2.status_code == 200:
            logger.info(f"[get_feishu_token_type] token={token} 可用作folder_token (drive/v1/files) resp2=200")
            return "folder"
        # 3. docs文档（知识库文档）
        url_doc = "https://open.feishu.cn/open-apis/docs/v1/documents/meta"
        params_doc = {"doc_token": token}
        resp3 = requests.get(url_doc, headers=headers, params=params_doc)
        if resp3.status_code == 200:
            logger.info(f"[get_feishu_token_type] token={token} docs_type=doc")
            return "doc"
        # 新增：如果文件夹和文档都不是，且 drive/meta 返回 400/404，但 token 还可能是文件，直接当 file 处理
        if resp.status_code in (400, 404) and resp2.status_code in (400, 404) and resp3.status_code in (400, 404):
            logger.warning(f"[get_feishu_token_type] token={token} 所有接口均未识别，直接当 file 处理")
            return "file"
        logger.warning(f"[get_feishu_token_type] token={token} status={resp.status_code}/{resp2.status_code}/{resp3.status_code} resp={resp.text}/{resp2.text}/{resp3.text}")
    except Exception as e:
        logger.warning(f"[get_feishu_token_type] 获取token类型失败: {e}")
    return "unknown"

def is_feishu_folder(token: str, user_access_token: str) -> bool:
    """兼容旧接口，True为文件夹，False为文件"""
    return get_feishu_token_type(token, user_access_token) == "folder"

def safe_section_name(name: str) -> str:
    """将文件名转为合法的section名（只保留中英文、数字、下划线）"""
    return re.sub(r'[^\w\u4e00-\u9fa5]', '_', name)

def get_config_path():
    """获取 config.toml 路径，保证为绝对路径"""
    return os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.toml")

def get_shortcut_target_token(file_token, user_access_token):
    url = "https://open.feishu.cn/open-apis/drive/v1/files/meta"
    headers = {"Authorization": f"Bearer {user_access_token}"}
    params = {"file_token": file_token}
    resp = requests.get(url, headers=headers, params=params)
    if resp.status_code == 200:
        data = resp.json().get("data", {})
        shortcut_info = data.get("shortcut_info", {})
        return shortcut_info.get("target_token"), data.get("type")
    return None, None

@feishu_tree_kb.route('/api/feishu/build_kb', methods=['POST'])
def build_knowledge_base():
    """
    基于选定的飞书文件构建知识库
    
    请求体参数：
    - file_tokens: List[str] - 选定的文件ID列表
    - user_access_token: str - 用户访问令牌
    - kb_name: str - 知识库名称
    
    返回:
    - 知识库创建状态信息
    """
    try:
        data = request.json
        file_tokens = data.get('file_tokens', [])
        user_access_token = data.get('user_access_token')
        kb_name = data.get('kb_name', 'feishu_kb')
        logger.info(f"[build_kb] Received user_access_token: {str(user_access_token)[:6]}*** (len={len(str(user_access_token)) if user_access_token else 0})")
        if not file_tokens:
            return jsonify({"error": "未提供文件ID列表"}), 400
        if not user_access_token:
            return jsonify({"error": "未提供用户访问令牌"}), 400
        logger.info(f"[build_kb] file_tokens count: {len(file_tokens)}, file_tokens: {file_tokens}")
        # 修正：用第一个 file_token 作为 folder_token 初始化知识库
        kb = FeishuKnowledgeBase(
            id=kb_name, 
            folder_token=file_tokens[0],  # 用真实 token
            user_access_token=user_access_token,
            file_tokens=file_tokens  # 传递所有 file_tokens，便于后续处理
        )
        total_files = len(file_tokens)
        processed_files = 0
        failed_files = []
        successful_files = []
        file_token_to_name = {}
        file_info_list = data.get('file_info_list', [])
        file_token_name_map = {}
        real_file_tokens = []
        for f in file_info_list:
            if f.get('type') == 'shortcut':
                target_token, target_type = None, None
                if f.get('shortcut_info') and f['shortcut_info'].get('target_token'):
                    target_token = f['shortcut_info']['target_token']
                    target_type = f['shortcut_info'].get('target_type')
                else:
                    target_token, target_type = get_shortcut_target_token(f['id'], user_access_token)
                if target_token:
                    token_type = target_type or get_feishu_token_type(target_token, user_access_token)
                    if token_type == 'folder':
                        from llm_for_a.market.qa_agent.models.feishu_tree_utils import fetch_feishu_tree
                        tree = fetch_feishu_tree(None, target_token, user_access_token, include_types={'docx', 'doc', 'sheet', 'file'})
                        def extract_doc_files(nodes):
                            files = []
                            if not nodes:
                                return files
                            for node in nodes:
                                if node is None:
                                    continue
                                if node.get('type') == 'shortcut' and node.get('shortcut_info') and node['shortcut_info'].get('target_token'):
                                    files.append(node['shortcut_info']['target_token'])
                                elif node.get('type') not in ('folder',):
                                    files.append(node['id'])
                                if 'children' in node and node['children']:
                                    files.extend(extract_doc_files(node['children']))
                            return files
                        doc_file_tokens = extract_doc_files(tree)
                        real_file_tokens.extend(doc_file_tokens)
                    else:
                        real_file_tokens.append(target_token)
                    file_token_name_map[target_token] = f['name']
                else:
                    logger.warning(f"无法获取 shortcut {f['id']} 的 target_token，尝试直接用原始 id 作为文档 token 处理")
                    real_file_tokens.append(f['id'])
                    file_token_name_map[f['id']] = f['name']
            else:
                real_file_tokens.append(f['id'])
                file_token_name_map[f['id']] = f['name']
        file_tokens = real_file_tokens
        logger.info(f"[build_kb] 处理后的 file_tokens: {file_tokens}")
        doc_token_size_map = {}  # 新增：每个文档单独统计 token_size
        for idx, file_token in enumerate(file_tokens):
            try:
                logger.info(f"[build_kb] ({idx+1}/{len(file_tokens)}) 正在处理 file_token: {file_token}")
                is_folder = is_feishu_folder(file_token, user_access_token)
                if is_folder:
                    logger.info(f"{file_token} 是文件夹token，递归查找其下所有文档")
                    from llm_for_a.market.qa_agent.models.feishu_tree_utils import fetch_feishu_tree
                    tree = fetch_feishu_tree(None, file_token, user_access_token, include_types={'docx', 'doc', 'sheet', 'file'})
                    def extract_doc_files(nodes):
                        files = []
                        if not nodes:
                            return files
                        for node in nodes:
                            if node is None:
                                continue
                            # 处理快捷方式（shortcut）
                            if node.get('type') == 'shortcut' and node.get('shortcut_info') and node['shortcut_info'].get('target_token'):
                                files.append(node['shortcut_info']['target_token'])
                            elif node.get('type') not in ('folder',):
                                files.append(node['id'])
                            if 'children' in node and node['children']:
                                files.extend(extract_doc_files(node['children']))
                        return files
                    doc_file_tokens = extract_doc_files(tree)
                    logger.info(f"文件夹 {file_token} 下共找到 {len(doc_file_tokens)} 个文档")
                    for doc_token in doc_file_tokens:
                        try:
                            content = kb.get_document_content(doc_token)
                            if content:
                                file_name = file_token_name_map.get(doc_token, kb_name)
                                successful_files.append(doc_token)
                                file_token_to_name[doc_token] = file_name
                                doc_token_size_map[doc_token] = len(content)  # 记录每个文档的 token_size
                            else:
                                failed_files.append({
                                    "token": doc_token,
                                    "reason": "获取内容失败"
                                })
                        except Exception as e:
                            logger.error(f"获取文件 {doc_token} 内容失败: {str(e)}")
                            failed_files.append({
                                "token": doc_token,
                                "reason": str(e)
                            })
                    continue
                # 是文件，直接用 docs API 获取内容
                content = kb.get_document_content(file_token)
                if content:
                    file_name = file_token_name_map.get(file_token, kb_name)
                    successful_files.append(file_token)
                    file_token_to_name[file_token] = file_name
                    doc_token_size_map[file_token] = len(content)  # 记录每个文档的 token_size
                else:
                    failed_files.append({
                        "token": file_token,
                        "reason": "获取内容失败"
                    })
            except Exception as e:
                logger.error(f"获取文件 {file_token} 内容失败: {str(e)}")
                failed_files.append({
                    "token": file_token,
                    "reason": str(e)
                })
            processed_files += 1
        logger.info(f"[build_kb] 处理完成: 成功 {len(successful_files)} 个, 失败 {len(failed_files)} 个")
        if failed_files:
            logger.info(f"[build_kb] 失败文件: {failed_files}")
        if successful_files:
            config_path = get_config_path()
            try:
                logger.info(f"准备写入 config.toml: {config_path}")
                with open(config_path, 'r', encoding='utf-8') as f:
                    doc = tomlkit.parse(f.read())
                kb_section = doc.get('knowledge_bases', tomlkit.table())
                # 获取主 section 的最新 token
                main_token = None
                if 'feishu' in kb_section and 'user_access_token' in kb_section['feishu']:
                    main_token = kb_section['feishu']['user_access_token']
                else:
                    main_token = user_access_token
                to_del = [k for k in kb_section.keys() if k.startswith('feishu.')]
                for k in to_del:
                    del kb_section[k]
                for file_token in successful_files:
                    file_name = file_token_to_name.get(file_token, kb_name)
                    # section名允许中文
                    section_name = f'feishu.{file_name}'
                    kb_config = tomlkit.table()
                    kb_config['type'] = 'feishu'
                    kb_config['folder_token'] = file_token
                    kb_config['user_access_token'] = main_token  # 强制用主 section 的 token
                    kb_config['name'] = file_name
                    kb_config['id'] = file_token  # id字段用file_token保证唯一
                    kb_config['token_size'] = doc_token_size_map.get(file_token, 0)  # 用每个文档自己的 token_size
                    kb_section[section_name] = kb_config
                doc['knowledge_bases'] = kb_section
                logger.info(f"写入后内容: {tomlkit.dumps(doc)}")
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(tomlkit.dumps(doc))
            except Exception as e:
                logger.error(f"写入 config.toml 失败: {e}")
                return jsonify({"error": f"写入 config.toml 失败: {e}"}), 500
            return jsonify({
                "success": True,
                "kb_name": kb_name,
                "total_files": total_files,
                "processed_files": processed_files,
                "successful_files": len(successful_files),
                "failed_files": failed_files,
                "token_size": kb._token_size or 0
            })
        else:
            return jsonify({
                "success": False,
                "error": "所有文件处理失败",
                "failed_files": failed_files
            }), 400
    except Exception as e:
        logger.error(f"构建知识库失败: {str(e)}")
        return jsonify({"error": f"构建知识库失败: {str(e)}"}), 500

@feishu_tree_kb.route('/api/feishu/recursive_files', methods=['POST'])
def get_recursive_files():
    """
    递归获取文件夹中的所有文件
    
    请求体参数:
    - folder_token: str - 文件夹ID
    - user_access_token: str - 用户访问令牌
    
    返回:
    - file_list: List[Dict] - 文件信息列表
    """
    try:
        data = request.json
        folder_token = data.get('folder_token')
        user_access_token = data.get('user_access_token')
        include_types_str = data.get('include_types')
        exclude_types_str = data.get('exclude_types')
        logger.info(f"[recursive_files] Received user_access_token: {str(user_access_token)[:6]}*** (len={len(str(user_access_token)) if user_access_token else 0})")
        
        if not folder_token:
            return jsonify({"error": "未提供文件夹ID"}), 400
            
        if not user_access_token:
            return jsonify({"error": "未提供用户访问令牌"}), 400
            
        include_types = set(include_types_str.split(',')) if include_types_str else None
        exclude_types = set(exclude_types_str.split(',')) if exclude_types_str else None
        
        # 创建Lark客户端
        client = lark.Client.builder().enable_set_token(True).log_level(lark.LogLevel.INFO).build()
        
        # 递归获取文件夹下的所有文件
        tree = fetch_feishu_tree(
            client, 
            folder_token, 
            user_access_token,
            include_types=include_types,
            exclude_types=exclude_types
        )
        
        if tree is None:
            return jsonify({"error": "获取文件列表失败"}), 500
        
        # 提取所有文件ID
        all_files = []
        
        def extract_files(nodes):
            if not nodes:
                return
            for node in nodes:
                if node['type'] != 'folder':
                    all_files.append({
                        'id': node['id'],
                        'name': node['name'],
                        'type': node['type']
                    })
                if 'children' in node and node['children']:
                    extract_files(node['children'])
        
        extract_files(tree)
        
        return jsonify({
            "folder_token": folder_token,
            "total_files": len(all_files),
            "file_list": all_files
        })
        
    except Exception as e:
        logger.error(f"递归获取文件失败: {str(e)}")
        return jsonify({"error": f"递归获取文件失败: {str(e)}"}), 500