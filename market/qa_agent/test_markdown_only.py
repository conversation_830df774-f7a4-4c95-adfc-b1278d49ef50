import asyncio
import json
import lark_oapi as lark
from lark_oapi.api.drive.v1 import *
from lark_oapi.api.docx.v1 import *
import mammoth
from docx import Document as DocxDocument
from io import BytesIO

async def test_feishu_markdown():
    # 飞书配置
    folder_token = "FMMufxN0ylK9PJdHcvWcOSornWd"
    user_access_token = "**********************************************"
    
    # 创建客户端
    client = lark.Client.builder() \
        .enable_set_token(True) \
        .log_level(lark.LogLevel.DEBUG) \
        .build()
    
    # 获取请求选项
    option = lark.RequestOption.builder() \
        .user_access_token(user_access_token) \
        .build()
    
    # 1. 获取文档列表
    print("=== 获取文档列表 ===")
    request = ListFileRequest.builder() \
        .folder_token(folder_token) \
        .order_by("EditedTime") \
        .direction("DESC") \
        .build()
    
    response = client.drive.v1.file.list(request, option)
    
    if not response.success():
        print(f"获取文档列表失败: {response.code}, {response.msg}")
        return
    
    doc_ids = []
    for file in response.data.files:
        if hasattr(file, 'shortcut_info') and file.shortcut_info is not None:
            doc_ids.append(file.shortcut_info.target_token)
        elif file.type == "doc":
            doc_ids.append(file.token)
    
    print(f"找到 {len(doc_ids)} 个文档")
    
    # 2. 下载并转换每个文档
    for i, doc_id in enumerate(doc_ids[:2]):  # 只处理前2个文档
        print(f"\n=== 处理文档 {i+1} (ID: {doc_id}) ===")
        
        # 尝试下载文档
        download_request = DownloadFileRequest.builder() \
            .file_token(doc_id) \
            .build()
        
        download_response = client.drive.v1.file.download(download_request, option)
        
        if download_response.success():
            print("成功下载文档")
            docx_content = download_response.file.read()
            print(f"文档大小: {len(docx_content)} 字节")
            
            # 转换为markdown
            try:
                # 使用mammoth转换
                result = mammoth.convert_to_markdown(docx_content)
                mammoth_markdown = result.value
                print(f"Mammoth转换结果长度: {len(mammoth_markdown)}")
                
                # 使用python-docx获取更多信息
                doc = DocxDocument(BytesIO(docx_content))
                print(f"文档段落数: {len(doc.paragraphs)}")
                print(f"文档表格数: {len(doc.tables)}")
                
                # 处理段落
                text_content = []
                for para in doc.paragraphs:
                    if para.text.strip():
                        if para.style and para.style.name.startswith('Heading'):
                            level = int(para.style.name[-1])
                            text_content.append(f"{'#' * level} {para.text}")
                        else:
                            # 检查格式
                            formatted_text = []
                            for run in para.runs:
                                text = run.text
                                if run.bold:
                                    text = f"**{text}**"
                                if run.italic:
                                    text = f"*{text}*"
                                formatted_text.append(text)
                            text_content.append("".join(formatted_text))
                
                # 处理表格
                for table in doc.tables:
                    text_content.append("\n| " + " | ".join(cell.text for cell in table.rows[0].cells) + " |")
                    text_content.append("|" + "|".join("---" for _ in table.rows[0].cells) + "|")
                    for row in table.rows[1:]:
                        text_content.append("| " + " | ".join(cell.text for cell in row.cells) + " |")
                
                final_markdown = "\n\n".join(text_content)
                
                print("\n=== Markdown 转换结果 ===")
                print(f"最终markdown长度: {len(final_markdown)}")
                print("\n前500字符:")
                print("-" * 50)
                print(final_markdown[:500])
                print("-" * 50)
                
            except Exception as e:
                print(f"转换失败: {str(e)}")
                
        else:
            print(f"下载失败: {download_response.code}, {download_response.msg}")
            
            # 尝试使用raw content API获取富文本
            print("尝试使用raw content API获取富文本...")
            raw_request = RawContentDocumentRequest.builder() \
                .document_id(doc_id) \
                .lang(0) \
                .build()
            raw_response = client.docx.v1.document.raw_content(raw_request, option)
            # 如需获取富文本/markdown内容，请使用 docs.v1.content.get API
            
            if raw_response.success():
                content = raw_response.data.content
                print(f"Rich text content长度: {len(content)}")
                print("\n前1000字符:")
                print("-" * 50)
                print(content[:1000])
                print("-" * 50)
                
                # 尝试解析富文本内容
                try:
                    import json
                    rich_data = json.loads(content)
                    print(f"\n富文本数据类型: {type(rich_data)}")
                    if isinstance(rich_data, dict):
                        print(f"富文本数据键: {list(rich_data.keys())}")
                except:
                    print("富文本内容不是JSON格式")
            else:
                print(f"Rich text也失败: {raw_response.code}, {raw_response.msg}")
                
                # 回退到普通文本
                print("回退到普通文本格式...")
                plain_request = RawContentDocumentRequest.builder() \
                    .document_id(doc_id) \
                    .lang(0) \
                    .build()
                
                plain_response = client.docx.v1.document.raw_content(plain_request, option)
                
                if plain_response.success():
                    content = plain_response.data.content
                    print(f"Plain text content长度: {len(content)}")
                    print("\n前500字符:")
                    print("-" * 50)
                    print(content[:500])
                    print("-" * 50)
                else:
                    print(f"Plain text也失败: {plain_response.code}, {plain_response.msg}")

if __name__ == "__main__":
    asyncio.run(test_feishu_markdown())