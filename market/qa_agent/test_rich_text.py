import asyncio
import toml
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from models.feishu_knowledge_base import FeishuKnowledgeBase

async def test_rich_text_extraction():
    # 加载配置
    config = toml.load("config.toml")
    feishu_config = config['knowledge_bases']['feishu']
    
    # 初始化飞书知识库
    kb = FeishuKnowledgeBase(
        folder_token=feishu_config['folder_token'],
        user_access_token=feishu_config['user_access_token']
    )
    
    print("=== 飞书知识库富文本测试 ===")
    
    # 获取文档列表
    doc_ids = kb.get_document_list()
    print(f"找到 {len(doc_ids)} 个文档")
    
    # 测试每个文档的富文本提取
    for i, doc_id in enumerate(doc_ids, 1):
        print(f"\n{'='*60}")
        print(f"测试文档 {i}: {doc_id}")
        print(f"{'='*60}")
        
        # 获取文档内容（这会触发富文本提取）
        content = await kb.get_document_content(doc_id)
        
        if content:
            print(f"✅ 成功获取文档内容")
            print(f"📄 内容长度: {len(content)} 字符")
            print(f"📝 内容预览（前800字符）:")
            print("-" * 50)
            print(content[:800])
            print("-" * 50)
            
            # 检查是否包含markdown格式
            markdown_indicators = ['#', '**', '*', '|', '```', '~~']
            found_markdown = []
            for indicator in markdown_indicators:
                if indicator in content:
                    found_markdown.append(indicator)
            
            if found_markdown:
                print(f"✅ 检测到markdown格式标记: {', '.join(found_markdown)}")
            else:
                print("⚠️  未检测到明显的markdown格式标记")
                
            # 统计内容结构
            lines = content.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            print(f"📊 内容统计:")
            print(f"   - 总行数: {len(lines)}")
            print(f"   - 非空行数: {len(non_empty_lines)}")
            print(f"   - 标题行数: {len([line for line in non_empty_lines if line.startswith('#')])}")
            print(f"   - 粗体文本: {content.count('**') // 2}")
            print(f"   - 斜体文本: {content.count('*') - content.count('**') * 2}")
            
        else:
            print("❌ 获取文档内容失败")
    
    print(f"\n{'='*60}")
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(test_rich_text_extraction()) 