import chardet
import glob
import sys
import os
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 推荐：绝对导入 utils.token
from llm_for_a.utils.token import get_num_tokens


class KnowledgeBase:
    def __init__(self):
        """初始化知识库基类"""
        pass

    def read(self, with_file_name: bool = False) -> str:
        """读取知识库内容
        
        Args:
            with_file_name: 是否包含文件名
            
        Returns:
            str: 知识库内容
        """
        raise NotImplementedError("Subclass must implement this method")

    def to_dict(self) -> Dict[str, Any]:
        """返回知识库信息的字典表示
        
        Returns:
            Dict[str, Any]: 包含知识库类型、路径、token数量和文档数量的字典
        """
        raise NotImplementedError("Subclass must implement this method")


class LocalFileKnowledgeBase(KnowledgeBase):
    def __init__(self, id: str, path: str, config_dir: str = None):
        super().__init__()
        self.id = id
          # 如果提供了配置目录，从配置目录解析相对路径
        if config_dir and not os.path.isabs(path):
            self.path = os.path.abspath(os.path.join(config_dir, path))
        else:
            # 将相对路径转换为绝对路径
            self.path = os.path.abspath(os.path.expanduser(path))
            
        print(f"[DEBUG] 初始化知识库 {id}")
        print(f"[DEBUG] 原始路径: {path}")
        print(f"[DEBUG] 配置目录: {config_dir}")
        print(f"[DEBUG] 解析后的绝对路径: {self.path}")
        self.files = self._get_files()
        print(f"[DEBUG] 找到的文件数量: {len(self.files)}")
        for f in self.files:
            print(f"[DEBUG] - {f}")
        
    def _get_files(self):
        """获取匹配的文件列表，并进行存在性检查"""
        try:
            # 规范化路径分隔符
            normalized_path = self.path.replace('/', os.sep).replace('\\', os.sep)
            
            # 如果是单个文件路径（不包含通配符），直接检查是否存在
            if '*' not in normalized_path and '?' not in normalized_path:
                if os.path.isfile(normalized_path):
                    print(f"[DEBUG] 找到单个文件: {normalized_path}")
                    return [normalized_path]
                elif os.path.isdir(normalized_path):
                    # 如果是目录，搜索所有文件
                    pattern = os.path.join(normalized_path, '*')
                    print(f"[DEBUG] 搜索目录下的文件: {pattern}")
                    files = glob.glob(pattern, recursive=True)
                    if files:
                        return sorted(files)
                else:
                    print(f"[DEBUG] 单个文件不存在: {normalized_path}")
                    raise FileNotFoundError(f"File not found: {normalized_path}")
            
            # 处理通配符模式
            print(f"[DEBUG] 搜索文件模式: {normalized_path}")
            files = glob.glob(normalized_path, recursive=True)
            
            if not files:
                base_dir = os.path.dirname(normalized_path)
                print(f"[DEBUG] 警告：未找到匹配的文件: {normalized_path}")
                if os.path.exists(base_dir):
                    print(f"[DEBUG] 目录 {base_dir} 中的文件:")
                    for f in os.listdir(base_dir):
                        print(f"[DEBUG]   - {f}")
                raise FileNotFoundError(f"No files found matching pattern: {normalized_path}")
            return sorted(files)
        except Exception as e:
            print(f"[DEBUG] 搜索文件时出错: {str(e)}")
            raise
    
    def read(self, with_file_name: bool = False):
        content = ""
        for file in self.files:
            try:
                with open(file, "r", encoding="utf-8") as f:
                    file_content = f.read()
                    if with_file_name:
                        content += f"\n[FILE: {os.path.basename(file)}]\n{file_content}"
                    else:
                        content += file_content
            except UnicodeDecodeError:
                try:
                    with open(file, "r", encoding="gb2312") as f:
                        file_content = f.read()
                        if with_file_name:
                            content += f"\n[FILE: {os.path.basename(file)}]\n{file_content}"
                        else:
                            content += file_content
                except UnicodeDecodeError:
                    print(f"[DEBUG] 尝试使用chardet检测编码: {file}")
                    with open(file, "rb") as f:
                        raw = f.read()
                        result = chardet.detect(raw)
                        print(f"[DEBUG] chardet检测结果: {result}")
                        if result["encoding"]:
                            try:
                                file_content = raw.decode(result["encoding"])
                                if with_file_name:
                                    content += f"\n[FILE: {os.path.basename(file)}]\n{file_content}"
                                else:
                                    content += file_content
                            except UnicodeDecodeError:
                                print(f"[WARNING] 无法使用检测到的编码 {result['encoding']} 解码文件: {file}")
                        else:
                            print(f"[WARNING] 无法检测文件编码: {file}")
        print(f"[DEBUG] 读取的内容总长度: {len(content)}")
        return content

    def to_dict(self):
        """返回知识库信息的字典表示"""
        content = self.read()
        token_count = get_num_tokens(content)
        print(f"[DEBUG] 计算得到的token数量: {token_count}")
        return {
            "type": "local_file",
            "path": self.path,
            "token_size": token_count,
            "doc_count": len(self.files)
        }
