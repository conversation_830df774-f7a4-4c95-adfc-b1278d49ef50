import json
import requests
import logging
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores.faiss import FAISS
from langchain.docstore.document import Document
import numpy as np
import time
from typing import List, Dict, Any, Optional, Tuple
from io import BytesIO
import mammoth
from docx import Document as DocxDocument
from llm_for_a.market.qa_agent.models.knowledge_base_model import KnowledgeBase
import traceback

logger = logging.getLogger(__name__)

class FeishuKnowledgeBase(KnowledgeBase):
    def __init__(self, id: str, folder_token: str, user_access_token: str, file_tokens: Optional[List[str]] = None, lazy_init: bool = False):
        """初始化飞书知识库
        
        Args:
            id: 知识库唯一标识
            folder_token: 飞书文件夹token
            user_access_token: 用户访问令牌
            file_tokens: 用户选择的文件token列表（可选）
            lazy_init: 是否延迟加载内容（只查树不查内容，适用于树状选择器）
        """
        super().__init__()
        self.id = id
        self.folder_token = folder_token
        self.user_access_token = user_access_token
        self.file_tokens = file_tokens
        self.lazy_init = lazy_init

        self.embeddings = OpenAIEmbeddings(
            openai_api_key="sk-ISyVIYc3933iApsiLaz-HQ",
            openai_api_base="http://192.168.211.230:10030/",
            model="gte_Qwen2-7B-instruct"  # 这里必须是 embedding 模型
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=50,
            separators=["\n\n", "\n", "。", "！", "？", ".", "!", "?"]
        )
        
        # 缓存
        self._doc_ids = None
        self._doc_contents = {}
        self._token_size = None
        
        # 测试文件夹访问权限并初始化缓存
        if not self.lazy_init:
            if not self._init_cache():
                raise PermissionError(f"无法访问飞书文件夹: {folder_token}，请检查文件夹权限设置")
            # 调试日志：初始化后缓存内容
            logger.info(f"[DEBUG] FeishuKnowledgeBase __init__ _doc_ids: {self._doc_ids}")
            logger.info(f"[DEBUG] FeishuKnowledgeBase __init__ _doc_contents keys: {list(self._doc_contents.keys())}")
            logger.info(f"[DEBUG] FeishuKnowledgeBase __init__ _token_size: {self._token_size}")
        else:
            # lazy_init: 只查文档列表，不查内容
            self._doc_ids = self.get_document_list()
            logger.info(f"[LAZY_INIT] 仅加载文档列表，不加载内容。文档数: {len(self._doc_ids) if self._doc_ids else 0}")

    def _init_cache(self) -> bool:
        """初始化缓存，支持 folder_token 为文件夹/文档/异常三种类型"""
        try:
            from llm_for_a.market.qa_agent.services.feishu_tree_kb import get_feishu_token_type
            token_type = get_feishu_token_type(self.folder_token, self.user_access_token)
            logger.info(f"[FeishuKnowledgeBase] folder_token={self.folder_token} type={token_type}")
            if token_type in ("doc", "file"):
                # folder_token 是文档或云空间文件，直接加载
                content = self.get_document_content(self.folder_token)
                if content:
                    self._doc_ids = [self.folder_token]
                    self._doc_contents = {self.folder_token: content}
                    self._token_size = len(content)
                    logger.info(f"[单文件模式] 直接加载文件 {self.folder_token}，token_size: {self._token_size}")
                    return True
                else:
                    self._doc_ids = []
                    self._doc_contents = {}
                    self._token_size = 0
                    logger.warning(f"[单文件模式] 文件 {self.folder_token} 获取内容失败")
                    return False
            elif token_type == "folder":
                # 文件夹逻辑
                if self.file_tokens is not None:
                    self._doc_ids = self.file_tokens
                    logger.info(f"使用前端选择的file_tokens: {self._doc_ids}")
                else:
                    self._doc_ids = self.get_document_list()
                if self._doc_ids:
                    logger.info(f"成功访问飞书文件夹/文件，找到 {len(self._doc_ids)} 个文档")
                    self._token_size = 0
                    for doc_id in self._doc_ids:
                        content = self.get_document_content(doc_id)
                        if isinstance(content, Exception):
                            logger.error(f"文档 {doc_id} 获取内容异常: {content}")
                            continue
                        if not isinstance(content, str):
                            logger.error(f"文档 {doc_id} 内容类型异常: {type(content)}，内容: {content}")
                            continue
                        self._doc_contents[doc_id] = content
                        assert isinstance(content, str), f"_init_cache: {doc_id} 内容类型异常: {type(content)}"
                        content_length = len(content)
                        logger.info(f"文档 {doc_id} 内容长度: {content_length}")
                        if content:
                            logger.info(f"文档 {doc_id} token数: {content_length}")
                            self._token_size += content_length
                    logger.info(f"成功初始化飞书知识库，文档数: {len(self._doc_ids)}, Token总数: {self._token_size}")
                    logger.info(f"[DEBUG] _init_cache end: _doc_contents keys: {list(self._doc_contents.keys())}")
                    logger.info(f"[DEBUG] _init_cache end: _token_size: {self._token_size}")
                    return True
                else:
                    logger.warning(f"飞书文件夹 {self.folder_token} 中没有找到文档")
                    return False
            else:
                logger.error(f"无法识别的 token 类型: {self.folder_token}，请检查 token 是否有效或有权限")
                return False
        except Exception as e:
            logger.error(f"_init_cache: 初始化缓存失败: {str(e)}")
            return False

    def get_document_list(self) -> List[str]:
        """获取文档列表，支持file_tokens优先"""
        if self.file_tokens is not None:
            return self.file_tokens
        if self._doc_ids is not None:
            return self._doc_ids
            
        headers = {"Authorization": f"Bearer {self.user_access_token}"}
        params = {
            "folder_token": self.folder_token,
            "order_by": "EditedTime",
            "direction": "DESC"
        }
        url = "https://open.feishu.cn/open-apis/drive/v1/files"
        resp = requests.get(url, headers=headers, params=params)
        if resp.status_code != 200:
            logger.error(f"获取文档列表失败, code: {resp.status_code}, msg: {resp.text}")
            return []
        data = resp.json()
        files = data.get("data", {}).get("files", [])
        doc_ids = []
        for file in files:
            if file.get("type") == "doc":
                doc_ids.append(file.get("token"))
            elif file.get("shortcut_info") and file["shortcut_info"].get("target_token"):
                doc_ids.append(file["shortcut_info"]["target_token"])
        self._doc_ids = doc_ids
        return doc_ids

    def get_document_content(self, document_id: str) -> Optional[str]:
        """获取文档内容，优先使用docs API获取markdown格式，并统计token数量"""
        # 如果内容已缓存，直接返回
        if document_id in self._doc_contents:
            return self._doc_contents[document_id]
            
        # 1. 优先尝试使用docs API获取markdown格式
        logger.info(f"开始使用docs API获取文档markdown内容: {document_id}")
        headers = {"Authorization": f"Bearer {self.user_access_token}"}
        url = f"https://open.feishu.cn/open-apis/docs/v1/content"
        params = {
            "doc_token": document_id,
            "doc_type": "docx",
            "content_type": "markdown",
            "lang": "zh"
        }
        resp = requests.get(url, headers=headers, params=params)
        
        if resp.status_code == 200:
            data = resp.json()
            content = data.get("data", {}).get("content", "")
            self._doc_contents[document_id] = content
            assert isinstance(content, str), f"get_document_content: {document_id} 内容类型异常: {type(content)}"
            
            content_length = len(content)
            logger.info(f"获取到markdown内容，长度: {content_length}")
            
            # 累加token数量（这里简单用字符数，可替换为分词/模型token数）
            if self._token_size is None:
                self._token_size = 0
            old_token_size = self._token_size
            self._token_size += content_length
            logger.info(f"文档token计数: +{content_length} (累计: {old_token_size} -> {self._token_size})")
            
            return content
        
        # 2. 回退到docx API获取普通文本格式
        logger.info("回退到docx API获取普通文本格式...")
        url2 = f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}/raw_content"
        resp2 = requests.get(url2, headers=headers)
        
        if resp2.status_code == 200:
            data2 = resp2.json()
            content = data2.get("data", {}).get("content", "")
            content_length = len(content)
            logger.info(f"成功获取普通文本内容，长度: {content_length}")
            
            # 对普通文本进行简单的markdown格式化
            formatted_content = self._format_plain_text_to_markdown(content)
            formatted_length = len(formatted_content)
            self._doc_contents[document_id] = formatted_content
            assert isinstance(formatted_content, str), f"get_document_content: {document_id} 内容类型异常: {type(formatted_content)}"
            
            # 累加token数量
            if self._token_size is None:
                self._token_size = 0
            old_token_size = self._token_size
            self._token_size += formatted_length
            logger.info(f"文档token计数: +{formatted_length} (累计: {old_token_size} -> {self._token_size})")
            
            return formatted_content

        logger.error(f"获取文档内容失败, code: {resp.status_code}, msg: {resp.text}")
        return None

    def _convert_docx_to_markdown(self, docx_bytes: bytes) -> Tuple[str, str]:
        """将docx内容转换为markdown格式
        
        Args:
            docx_bytes: docx文件的二进制内容
            
        Returns:
            Tuple[str, str]: (markdown格式内容, 纯文本内容)
        """
        try:
            # 1. 使用mammoth转换为markdown
            logger.info("开始使用mammoth转换文档为markdown...")
            result = mammoth.convert_to_markdown(docx_bytes)
            markdown_content = result.value
            
            if result.messages:  # 记录转换过程中的警告信息
                for message in result.messages:
                    logger.warning(f"Mammoth转换警告: {message}")
            
            # 2. 使用python-docx获取更多格式信息
            logger.info("使用python-docx获取额外的格式信息...")
            doc = DocxDocument(BytesIO(docx_bytes))
            
            # 3. 提取文本，保留段落结构
            text_content = []
            
            # 记录文档结构信息
            logger.info(f"文档段落数: {len(doc.paragraphs)}")
            logger.info(f"文档表格数: {len(doc.tables)}")
            
            for para in doc.paragraphs:
                if para.text.strip():
                    # 记录段落样式信息
                    style_name = para.style.name if para.style else "默认样式"
                    logger.debug(f"段落样式: {style_name}, 文本: {para.text[:50]}...")
                    
                    # 处理段落样式
                    if para.style and para.style.name.startswith('Heading'):
                        level = int(para.style.name[-1])
                        text_content.append(f"{'#' * level} {para.text}")
                    else:
                        # 检查是否有特殊格式
                        formatted_text = []
                        for run in para.runs:
                            text = run.text
                            if run.bold:
                                text = f"**{text}**"
                            if run.italic:
                                text = f"*{text}*"
                            formatted_text.append(text)
                        text_content.append("".join(formatted_text))
                        
            # 4. 处理表格
            for i, table in enumerate(doc.tables):
                logger.info(f"处理表格 {i+1}, 行数: {len(table.rows)}, 列数: {len(table.rows[0].cells)}")
                text_content.append("\n| " + " | ".join(cell.text for cell in table.rows[0].cells) + " |")
                text_content.append("|" + "|".join("---" for _ in table.rows[0].cells) + "|")
                for row in table.rows[1:]:
                    text_content.append("| " + " | ".join(cell.text for cell in row.cells) + " |")
            
            final_markdown = "\n\n".join(text_content)
            logger.info(f"文档转换完成，markdown长度: {len(final_markdown)}")
            
            # 返回两种格式
            return final_markdown, "\n\n".join(text_content)
            
        except Exception as e:
            logger.error(f"转换文档格式失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return "", ""

    def _convert_rich_text_to_markdown(self, rich_data: dict) -> str:
        """将富文本数据转换为markdown格式
        
        Args:
            rich_data: 富文本数据
            
        Returns:
            str: markdown格式的内容
        """
        try:
            markdown_parts = []
            
            # 处理富文本的不同部分
            if isinstance(rich_data, dict):
                # 如果有blocks字段，处理blocks
                if 'blocks' in rich_data:
                    for block in rich_data['blocks']:
                        block_text = self._process_rich_text_block(block)
                        if block_text:
                            markdown_parts.append(block_text)
                
                # 如果有其他字段，尝试提取文本
                elif 'content' in rich_data:
                    markdown_parts.append(str(rich_data['content']))
                else:
                    # 直接转换为字符串
                    markdown_parts.append(str(rich_data))
            
            return '\n\n'.join(markdown_parts) if markdown_parts else ""
            
        except Exception as e:
            logger.error(f"富文本转换失败: {str(e)}")
            return ""

    def _process_rich_text_block(self, block: dict) -> str:
        """处理富文本块
        
        Args:
            block: 富文本块数据
            
        Returns:
            str: 处理后的文本
        """
        try:
            block_type = block.get('type', '')
            
            if block_type == 'paragraph':
                return self._process_paragraph_block(block)
            elif block_type == 'heading':
                return self._process_heading_block(block)
            elif block_type == 'table':
                return self._process_table_block(block)
            elif block_type == 'list':
                return self._process_list_block(block)
            else:
                # 尝试提取文本内容
                if 'text' in block:
                    return block['text']
                elif 'content' in block:
                    return str(block['content'])
                else:
                    return str(block)
                    
        except Exception as e:
            logger.warning(f"处理富文本块失败: {str(e)}")
            return ""

    def _process_paragraph_block(self, block: dict) -> str:
        """处理段落块"""
        text_parts = []
        elements = block.get('elements', [])
        
        for element in elements:
            if element.get('type') == 'text_run':
                text = element.get('text', '')
                style = element.get('style', {})
                
                # 应用样式
                if style.get('bold'):
                    text = f"**{text}**"
                if style.get('italic'):
                    text = f"*{text}*"
                if style.get('strikethrough'):
                    text = f"~~{text}~~"
                    
                text_parts.append(text)
        
        return ''.join(text_parts)

    def _process_heading_block(self, block: dict) -> str:
        """处理标题块"""
        level = block.get('level', 1)
        text = self._process_paragraph_block(block)
        return f"{'#' * level} {text}"

    def _process_table_block(self, block: dict) -> str:
        """处理表格块"""
        # 简单的表格处理
        return "| 表格内容 | 待处理 |\n|---------|--------|\n"

    def _process_list_block(self, block: dict) -> str:
        """处理列表块"""
        # 简单的列表处理
        return "- 列表项"

    def _format_plain_text_to_markdown(self, content: str) -> str:
        """将普通文本格式化为markdown
        
        Args:
            content: 普通文本内容
            
        Returns:
            str: markdown格式的内容
        """
        lines = content.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是标题（以数字开头）
            if line and line[0].isdigit() and '.' in line[:10]:
                # 将数字标题转换为markdown标题
                parts = line.split('.', 1)
                if len(parts) == 2:
                    level = len(parts[0])
                    formatted_lines.append(f"{'#' * min(level, 6)} {parts[1].strip()}")
                    continue
            
            # 普通段落
            formatted_lines.append(line)
        
        return '\n\n'.join(formatted_lines)
        
    def read(self, with_file_name: bool = False) -> str:
        """读取所有文档内容
        
        Args:
            with_file_name: 是否包含文件名
            
        Returns:
            str: 组合后的文档内容
        """
        if not self._doc_contents:
            return ""
            
        contents = []
        for doc_id, content in self._doc_contents.items():
            if with_file_name:
                contents.append(f"# 文档ID: {doc_id}\n\n{content}")
            else:
                contents.append(content)
                
        return "\n\n".join(contents)

    def _format_content(self, content: str) -> str:
        """将文档内容格式化为markdown格式
        
        Args:
            content: 原始文档内容
            
        Returns:
            str: 格式化后的内容
        """
        # 1. 分割段落
        paragraphs = content.split('\n\n')
        
        # 2. 处理每个段落
        formatted_paragraphs = []
        for para in paragraphs:
            if not para.strip():
                continue
                
            # 移除多余的空白字符
            para = ' '.join(para.split())
            
            # 检查是否是标题（以数字开头的段落）
            if para[0].isdigit() and '.' in para[:10]:
                # 将数字标题转换为markdown标题
                title_parts = para.split('.', 1)
                if len(title_parts) == 2:
                    level = len(title_parts[0].split('.'))
                    formatted_paragraphs.append(f"{'#' * level} {title_parts[1].strip()}")
                    continue
            
            # 普通段落
            formatted_paragraphs.append(para)
        
        return '\n\n'.join(formatted_paragraphs)

    def to_dict(self) -> Dict[str, Any]:
        """返回知识库信息的字典表示
        
        Returns:
            Dict[str, Any]: 包含知识库类型、路径、token数量和文档数量的字典
        """
        try:
            # 统计所有已缓存文档内容的长度，修复 coroutine 问题
            import asyncio
            total_tokens = 0
            for doc_id, content in self._doc_contents.items():
                if hasattr(content, '__await__'):
                    logger.warning(f"to_dict: 文档 {doc_id} 内容为 coroutine，尝试 await...")
                    try:
                        content = asyncio.run(content)
                        self._doc_contents[doc_id] = content
                    except Exception as e:
                        logger.error(f"to_dict: await 文档 {doc_id} 内容失败: {str(e)}")
                        content = ''
                if not isinstance(content, str):
                    logger.error(f"to_dict: 文档 {doc_id} 内容类型异常: {type(content)}，内容: {repr(content)}")
                    import traceback
                    logger.exception(f"to_dict: 文档 {doc_id} 类型异常，堆栈如下：")
                    raise TypeError(f"to_dict: 文档 {doc_id} 内容类型异常: {type(content)}, 内容: {repr(content)}")
                total_tokens += len(content)
            doc_count = len(self._doc_contents)
            # 优先使用 config 里的 token_size 字段
            config_token_size = None
            if hasattr(self, 'config') and isinstance(self.config, dict):
                config_token_size = self.config.get('token_size')
            # 如果 _token_size 为 0 或 None，且 config 里有 token_size，则用 config 的
            if (self._token_size is None or self._token_size == 0) and config_token_size:
                self._token_size = config_token_size
            elif self._token_size is None or self._token_size == 0:
                self._token_size = total_tokens
            return {
                "type": "feishu",
                "path": f"飞书文件夹: {self.folder_token}",
                "token_size": self._token_size or 0,
                "doc_count": doc_count
            }
        except Exception as e:
            logger.error(f"to_dict: 生成知识库字典信息失败: {str(e)}")
            return {
                "type": "feishu",
                "path": f"飞书文件夹: {getattr(self, 'folder_token', '')}",
                "token_size": 0,
                "doc_count": 0
            }

    async def search(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索相关文档内容（仅关键词检索，无向量检索）"""
        return self.search_by_keyword(query, top_k)

    def search_by_keyword(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """用关键词查找相关文档内容

        Args:
            query: 查询关键词
            top_k: 返回结果数量

        Returns:
            List[Dict]: 包含相关内容的结果列表
        """
        doc_ids = self.get_document_list()
        if not doc_ids:
            return []

        results = []
        import asyncio
        for doc_id in doc_ids:
            content = self._doc_contents.get(doc_id)
            if hasattr(content, '__await__'):
                logger.warning(f"search_by_keyword: 文档 {doc_id} 内容为 coroutine，尝试 await...")
                try:
                    content = asyncio.run(content)
                    self._doc_contents[doc_id] = content
                except Exception as e:
                    logger.error(f"search_by_keyword: await 文档 {doc_id} 内容失败: {str(e)}")
                    continue
            if not content or not isinstance(content, str):
                continue
            for line in content.split('\n'):
                if query in line:
                    results.append({
                        "content": line.strip(),
                        "doc_id": doc_id
                    })
                    if len(results) >= top_k:
                        return results
        return results

    def recalculate_token_count(self) -> int:
        """重新计算所有文档的token数量
        
        Returns:
            int: 计算得到的总token数
        """
        try:
            total_tokens = 0
            import asyncio
            for doc_id, content in self._doc_contents.items():
                if hasattr(content, '__await__'):
                    logger.warning(f"recalculate_token_count: 文档 {doc_id} 内容为 coroutine，尝试 await...")
                    try:
                        content = asyncio.run(content)
                        self._doc_contents[doc_id] = content
                    except Exception as e:
                        logger.error(f"recalculate_token_count: await 文档 {doc_id} 内容失败: {str(e)}")
                        continue
                if not isinstance(content, str):
                    logger.error(f"recalculate_token_count: 文档 {doc_id} 内容类型异常: {type(content)}，内容: {content}")
                    continue
                doc_tokens = len(content)
                total_tokens += doc_tokens
                logger.info(f"文档 {doc_id} token数: {doc_tokens}")
            # 更新token_size
            old_size = self._token_size
            self._token_size = total_tokens
            logger.info(f"重新计算token数量: {old_size} -> {self._token_size}")
            return total_tokens
        except Exception as e:
            logger.error(f"重新计算token数量时出错: {str(e)}")
            return 0

# 使用示例
async def test_knowledge_base():
    # 初始化知识库
    kb = FeishuKnowledgeBase(
        folder_token="FMMufxN0ylK9PJdHcvWcOSornWd",
        user_access_token="u-cIJsQS0mRetWIDHZTEWbxrgllJX05kwNPgG01gW8GBI0"
    )
    
    # 测试搜索
    query = "注册资本"
    results = await kb.search(query, top_k=3)
    
    # 打印结果
    for result in results:
        print(f"文档ID: {result['doc_id']}")
        print(f"内容: {result['content']}\n")