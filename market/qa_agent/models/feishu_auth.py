import lark_oapi as lark
from typing import Optional
import time

class FeishuAuth:
    def __init__(self, app_id: str = None, app_secret: str = None, user_access_token: Optional[str] = None):
        """初始化飞书认证管理器
        
        Args:
            app_id: 应用ID（可选）
            app_secret: 应用密钥（可选）
            user_access_token: 用户访问令牌
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.user_access_token = user_access_token
        
        if not user_access_token:
            raise ValueError("必须提供 user_access_token")
            
        # 创建基础客户端，不在这里设置 token
        self.client = lark.Client.builder() \
            .enable_set_token(True) \
            .log_level(lark.LogLevel.DEBUG) \
            .build()
            
        lark.logger.info("使用 user_access_token 模式")
            
        self._access_token = None
        self._token_expires_at = 0
        
    def test_connection(self) -> bool:
        """测试飞书API连接和认证是否正常
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 如果有user_access_token，直接使用
            if self.user_access_token:
                option = self.get_request_option()
                if not option:
                    lark.logger.error("创建请求选项失败")
                    return False
                return True
                
            # 否则测试获取tenant_access_token
            token = self.get_access_token()
            if not token:
                lark.logger.error("获取访问令牌失败")
                return False
                
            lark.logger.info("飞书API连接测试成功")
            return True
            
        except Exception as e:
            lark.logger.error(f"飞书API连接测试失败: {str(e)}")
            return False
            
    def get_access_token(self) -> Optional[str]:
        """获取访问令牌
        
        Returns:
            Optional[str]: 访问令牌
        """
        return self.user_access_token
            
    def get_request_option(self) -> Optional[lark.RequestOption]:
        """获取请求选项，包含最新的访问令牌
        
        Returns:
            Optional[lark.RequestOption]: 请求选项
        """
        token = self.get_access_token()
        if not token:
            return None
            
        # 直接在请求选项中设置 user_access_token
        return lark.RequestOption.builder() \
            .user_access_token(token) \
            .build()
            
    def create_client(self) -> lark.Client:
        """创建配置好的飞书客户端
        
        Returns:
            lark.Client: 飞书客户端实例
        """
        return self.client 