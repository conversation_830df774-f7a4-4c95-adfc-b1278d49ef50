from fastapi import FastAP<PERSON>, HTTPException
from datetime import datetime, timedelta
import asyncio
import lark_oapi as lark
from typing import Optional

class TokenManager:
    def __init__(self, app_id: str, app_secret: str):
        self.app_id = app_id
        self.app_secret = app_secret
        self.user_access_token = None
        self.refresh_token = None
        self.token_expiry = None
        self.refresh_lock = asyncio.Lock()
        
    async def refresh_token(self):
        """刷新用户访问令牌"""
        async with self.refresh_lock:  # 防止并发刷新
            if not self.refresh_token:
                raise HTTPException(status_code=401, detail="No refresh token available")
                
            client = lark.Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .build()
                
            request = lark.RefreshUserAccessTokenRequest.builder() \
                .refresh_token(self.refresh_token) \
                .build()
                
            response = await client.auth.v3.token.refresh(request)
            
            if not response.success():
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to refresh token: {response.msg}"
                )
                
            self.user_access_token = response.data.access_token
            self.refresh_token = response.data.refresh_token  # 获取新的refresh_token
            self.token_expiry = datetime.now() + timedelta(seconds=response.data.expire_in)
            
    async def get_valid_token(self) -> str:
        """获取有效的访问令牌，如果即将过期则自动刷新"""
        if not self.user_access_token or \
           not self.token_expiry or \
           datetime.now() >= self.token_expiry - timedelta(minutes=5):  # 提前5分钟刷新
            await self.refresh_token()
        return self.user_access_token
        
    def set_initial_tokens(self, access_token: str, refresh_token: str, expire_in: int):
        """设置初始token信息"""
        self.user_access_token = access_token
        self.refresh_token = refresh_token
        self.token_expiry = datetime.now() + timedelta(seconds=expire_in)

# 全局token管理器实例
token_manager: Optional[TokenManager] = None

def init_token_manager(app_id: str, app_secret: str) -> TokenManager:
    """初始化token管理器"""
    global token_manager
    if token_manager is None:
        token_manager = TokenManager(app_id, app_secret)
    return token_manager

def get_token_manager() -> TokenManager:
    """获取token管理器实例"""
    if token_manager is None:
        raise RuntimeError("Token manager not initialized")
    return token_manager 