import lark_oapi as lark
from lark_oapi.api.drive.v1 import ListFileRequest
import os
import sys
import datetime
import logging
from typing import List, Dict, Any, Optional, Set

def format_time(time_obj):
    """格式化时间对象
    
    处理可能是datetime对象或字符串的时间值
    """
    if isinstance(time_obj, datetime.datetime):
        return time_obj.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(time_obj, str):
        return time_obj
    else:
        return str(time_obj) if time_obj else ""

def fetch_feishu_tree(
    client, 
    folder_token: Optional[str] = None, 
    user_access_token: str = None, 
    depth: int = 0, 
    max_depth: int = 10, 
    include_types: Optional[Set[str]] = None, 
    exclude_types: Optional[Set[str]] = None
) -> List[Dict[str, Any]]:
    """
    递归获取飞书文件夹下所有文件和文件夹，返回树状结构。
    
    Args:
        client: lark.Client 实例
        folder_token: 当前文件夹 token，传入"root"表示获取用户的根目录，不提供则获取用户所有文件
        user_access_token: 用户 access token
        depth: 当前递归深度
        max_depth: 最大递归深度，防止死循环
        include_types: 只包含这些类型的文件，例如 {"doc", "sheet"}，None 表示包含所有类型
        exclude_types: 排除这些类型的文件，例如 {"shortcut", "file"}，优先级低于 include_types
    
    Returns:
        List[Dict[str, Any]]: 树状结构的文件列表
    """
    if depth > max_depth:
        return None
    
    option = lark.RequestOption.builder().user_access_token(user_access_token).build()
    
    # 构建请求对象
    request_builder = ListFileRequest.builder()
    
    # 添加排序条件
    request_builder.order_by("EditedTime").direction("DESC")
    
    # 如果提供了folder_token，则获取指定文件夹内容
    if folder_token:
        request_builder.folder_token(folder_token)
    
    # 构建请求并发送
    request = request_builder.build()
    response = client.drive.v1.file.list(request, option)
    
    if not response.success():
        logging.error(f"获取文件夹 {folder_token} 列表失败: {response.code}, {response.msg}")
        return None
    
    nodes = []
    for file in response.data.files:
        # 根据类型过滤
        file_type = file.type
        
        if include_types is not None and file_type not in include_types:
            continue
        if exclude_types is not None and file_type in exclude_types:
            continue
          # 构建节点基本信息
        node = {
            "id": file.token,
            "name": file.name,
            "type": file_type,
            "parent_id": folder_token,
            "created_time": format_time(file.created_time) if hasattr(file, 'created_time') else "",
            "modified_time": format_time(file.modified_time) if hasattr(file, 'modified_time') else "",
            "creator": file.creator if hasattr(file, 'creator') else "",
            "description": file.description if hasattr(file, 'description') else "",
        }
        
        # 处理文件夹，递归获取子项
        if file_type == "folder":
            children = fetch_feishu_tree(
                client, 
                file.token, 
                user_access_token, 
                depth + 1, 
                max_depth,
                include_types,
                exclude_types
            )
            node["children"] = children if children is not None else []
            # 添加文件夹的额外信息
            node["is_empty"] = len(node["children"]) == 0
        
        # 处理特殊文件类型的额外信息
        elif file_type == "doc":
            node["editable"] = True
        elif file_type == "sheet":
            node["editable"] = True
        elif file_type == "docx":
            node["editable"] = True
            
        nodes.append(node)
    
    # 排序：文件夹优先，然后按修改时间倒序排列
    nodes.sort(key=lambda x: (0 if x["type"] == "folder" else 1, x.get("modified_time", ""), x.get("name", "")))
    
    return nodes

def print_tree(nodes, indent=0):
    """辅助打印树结构"""
    if not nodes:
        return
    for node in nodes:
        print("  " * indent + f"- [{node['type']}] {node['name']} ({node['id']})")
        if node.get("children"):
            print_tree(node["children"], indent+1)

def main():
    # 你需要替换为自己的 token
    folder_token = os.environ.get("FEISHU_FOLDER_TOKEN", "你的根文件夹token")
    user_access_token = os.environ.get("FEISHU_USER_ACCESS_TOKEN", "你的access_token")
    client = lark.Client.builder().enable_set_token(True).log_level(lark.LogLevel.INFO).build()
    print(f"开始递归获取飞书文件夹 {folder_token} 的树状结构...")
    tree = fetch_feishu_tree(client, folder_token, user_access_token)
    print("\n树状结构如下：")
    print_tree(tree)

if __name__ == "__main__":
    main()
