import toml
import os
import logging

def sync_all_feishu_tokens(new_token: str, config_path: str = None) -> bool:
    """
    将 config.toml 里所有分库的 user_access_token 字段同步为最新 token。
    Args:
        new_token: 最新的 user_access_token
        config_path: config.toml 路径，默认自动查找
    Returns:
        bool: 是否同步成功
    """
    if config_path is None:
        # 默认路径
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config.toml')
        config_path = os.path.abspath(config_path)
    if not os.path.exists(config_path):
        logging.error(f"config.toml 不存在: {config_path}")
        return False
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        changed = False
        for section in config:
            if isinstance(config[section], dict) and 'user_access_token' in config[section]:
                if config[section]['user_access_token'] != new_token:
                    config[section]['user_access_token'] = new_token
                    changed = True
        if changed:
            with open(config_path, 'w', encoding='utf-8') as f:
                toml.dump(config, f)
            logging.info(f"已同步所有分库 user_access_token 为最新 token")
        else:
            logging.info(f"所有分库 user_access_token 已是最新，无需同步")
        return True
    except Exception as e:
        logging.error(f"同步 user_access_token 失败: {e}")
        return False
