<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书知识库选择器</title>    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f9f9f9;
        }
        .instructions {
            background-color: #e8f5fe;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 5px solid #0066cc;
        }
        .container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
        }
        .file-tree {
            width: 50%;
            padding: 15px;
            border-right: 1px solid #eee;
            overflow-y: auto;
            height: calc(100vh - 100px);
        }
        .selected-files {
            width: 50%;
            padding: 15px;
            overflow-y: auto;
            height: calc(100vh - 100px);
        }
        .tree-node {
            margin: 5px 0;
            padding: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        .tree-node:hover {
            background-color: #f5f5f5;
        }
        .tree-folder {
            color: #0066cc;
            font-weight: bold;
        }
        .tree-file {
            color: #333;
        }
        .selected {
            background-color: #e1f5fe;
        }
        .children {
            margin-left: 20px;
            display: none;
        }
        .open > .children {
            display: block;
        }
        .folder-icon:before {
            content: '📁 ';
        }
        .open .folder-icon:before {
            content: '📂 ';
        }
        .file-icon:before {
            content: '📄 ';
        }
        .docx-icon:before {
            content: '📝 ';
        }
        .sheet-icon:before {
            content: '📊 ';
        }
        .buttons {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 5px;
        }
        button:hover {
            background-color: #0055aa;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .status-message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
        }
        .token-input {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .search-box {
            margin-bottom: 15px;
        }
        .search-box input {
            width: calc(100% - 22px);
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>飞书知识库文件选择器</h1>
    <div class="instructions">
        <p>通过这个页面，您可以浏览飞书云空间中的所有文件，选择需要加入知识库的文件。</p>
        <ol>
            <li>输入飞书访问令牌（可以从配置文件获取或通过飞书API获取）</li>
            <li>点击"加载文件树"按钮，将显示您的飞书云空间中所有文件</li>
            <li>点击文件夹可以展开/折叠文件夹内容</li>
            <li>点击文件可以选择/取消选择该文件</li>
            <li>输入知识库名称，点击"创建知识库"按钮完成创建</li>
        </ol>
    </div>
    
    <div id="token-form">
        <p>请输入飞书访问令牌</p>
        <input type="text" id="user-token" class="token-input" placeholder="粘贴飞书访问令牌...">
        <button id="load-tree-btn">加载文件树</button>
    </div>
    
    <div id="main-content" class="hidden">
        <div class="container">
            <div class="file-tree">
                <h2>文件树</h2>
                <div class="search-box">
                    <input type="text" id="search-files" placeholder="搜索文件...">
                </div>
                <div id="loading-tree" class="loading">
                    <p>正在加载文件树...</p>
                </div>
                <div id="file-tree"></div>
            </div>
            <div class="selected-files">
                <h2>已选择的文件</h2>
                <div id="selected-files-list"></div>
                <div class="buttons">
                    <input type="text" id="kb-name" class="token-input" placeholder="知识库名称...">
                    <button id="create-kb-btn" disabled>创建知识库</button>
                </div>
                <div id="status-message"></div>
            </div>
        </div>
    </div>
    
    <script>
        let userToken = '';
        let selectedFiles = new Map(); // 使用Map存储选择的文件，键为token，值为文件信息
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('load-tree-btn').addEventListener('click', loadFileTree);
            document.getElementById('create-kb-btn').addEventListener('click', createKnowledgeBase);
            document.getElementById('search-files').addEventListener('input', searchFiles);
        });
        
        // 加载文件树
        function loadFileTree() {
            userToken = document.getElementById('user-token').value.trim();
            if (!userToken) {
                alert('请输入飞书访问令牌');
                return;
            }
            
            // 显示主内容区域，隐藏令牌表单
            document.getElementById('token-form').classList.add('hidden');
            document.getElementById('main-content').classList.remove('hidden');
            
            document.getElementById('loading-tree').style.display = 'block';
            document.getElementById('file-tree').innerHTML = '';
            
            // 调用API获取文件树
            fetch(`/api/feishu/tree?user_access_token=${encodeURIComponent(userToken)}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading-tree').style.display = 'none';
                    if (data.tree) {
                        renderFileTree(data.tree);
                    } else {
                        document.getElementById('file-tree').innerHTML = '<p>无法加载文件树，请检查令牌是否有效。</p>';
                    }
                })
                .catch(error => {
                    document.getElementById('loading-tree').style.display = 'none';
                    document.getElementById('file-tree').innerHTML = `<p>加载文件树出错: ${error.message}</p>`;
                });
        }
        
        // 渲染文件树
        function renderFileTree(nodes) {
            const treeContainer = document.getElementById('file-tree');
            treeContainer.innerHTML = '';
            
            if (!nodes || nodes.length === 0) {
                treeContainer.innerHTML = '<p>没有找到文件</p>';
                return;
            }
            
            const ul = document.createElement('ul');
            ul.style.listStyle = 'none';
            ul.style.padding = '0';
            
            nodes.forEach(node => {
                const li = renderNode(node);
                ul.appendChild(li);
            });
            
            treeContainer.appendChild(ul);
            
            // 添加文件夹点击展开/折叠事件
            document.querySelectorAll('.folder-node').forEach(folderNode => {
                folderNode.addEventListener('click', function(e) {
                    if (e.target === this || e.target.classList.contains('folder-icon') || e.target.classList.contains('folder-name')) {
                        this.classList.toggle('open');
                        e.stopPropagation();
                    }
                });
            });
            
            // 添加文件点击选择事件
            document.querySelectorAll('.file-node').forEach(fileNode => {
                fileNode.addEventListener('click', function(e) {
                    const fileId = this.getAttribute('data-id');
                    const fileName = this.getAttribute('data-name');
                    const fileType = this.getAttribute('data-type');
                    
                    if (selectedFiles.has(fileId)) {
                        // 取消选择
                        selectedFiles.delete(fileId);
                        this.classList.remove('selected');
                    } else {
                        // 选择文件
                        selectedFiles.set(fileId, {
                            id: fileId,
                            name: fileName,
                            type: fileType
                        });
                        this.classList.add('selected');
                    }
                    
                    updateSelectedFilesList();
                    e.stopPropagation();
                });
            });
        }
        
        // 渲染单个节点
        function renderNode(node) {
            const li = document.createElement('li');
            li.className = 'tree-node';
            
            const nodeContent = document.createElement('div');
            
            if (node.type === 'folder') {
                // 文件夹节点
                li.classList.add('folder-node', 'tree-folder');
                
                const folderIcon = document.createElement('span');
                folderIcon.className = 'folder-icon';
                
                const folderName = document.createElement('span');
                folderName.className = 'folder-name';
                folderName.textContent = node.name;
                
                nodeContent.appendChild(folderIcon);
                nodeContent.appendChild(folderName);
                li.appendChild(nodeContent);
                
                // 添加子节点容器
                if (node.children && node.children.length > 0) {
                    const childrenContainer = document.createElement('div');
                    childrenContainer.className = 'children';
                    
                    const childrenUl = document.createElement('ul');
                    childrenUl.style.listStyle = 'none';
                    childrenUl.style.padding = '0';
                    
                    node.children.forEach(child => {
                        const childLi = renderNode(child);
                        childrenUl.appendChild(childLi);
                    });
                    
                    childrenContainer.appendChild(childrenUl);
                    li.appendChild(childrenContainer);
                }
            } else {
                // 文件节点
                li.classList.add('file-node', 'tree-file');
                li.setAttribute('data-id', node.id);
                li.setAttribute('data-name', node.name);
                li.setAttribute('data-type', node.type);
                
                let fileIcon;
                if (node.type === 'docx' || node.type === 'doc') {
                    fileIcon = document.createElement('span');
                    fileIcon.className = 'docx-icon';
                } else if (node.type === 'sheet') {
                    fileIcon = document.createElement('span');
                    fileIcon.className = 'sheet-icon';
                } else {
                    fileIcon = document.createElement('span');
                    fileIcon.className = 'file-icon';
                }
                
                const fileName = document.createElement('span');
                fileName.textContent = node.name;
                
                nodeContent.appendChild(fileIcon);
                nodeContent.appendChild(fileName);
                li.appendChild(nodeContent);
            }
            
            return li;
        }
        
        // 更新已选择的文件列表
        function updateSelectedFilesList() {
            const selectedFilesList = document.getElementById('selected-files-list');
            selectedFilesList.innerHTML = '';
            
            if (selectedFiles.size === 0) {
                selectedFilesList.innerHTML = '<p>未选择任何文件</p>';
                document.getElementById('create-kb-btn').disabled = true;
                return;
            }
            
            const ul = document.createElement('ul');
            
            selectedFiles.forEach((file, id) => {
                const li = document.createElement('li');
                
                const fileInfo = document.createElement('span');
                fileInfo.textContent = `${file.name} (${file.type})`;
                
                const removeBtn = document.createElement('button');
                removeBtn.textContent = '移除';
                removeBtn.style.marginLeft = '10px';
                removeBtn.style.padding = '2px 5px';
                removeBtn.style.fontSize = '12px';
                
                removeBtn.addEventListener('click', () => {
                    selectedFiles.delete(id);
                    updateSelectedFilesList();
                    
                    // 取消对应文件节点的选中状态
                    const fileNode = document.querySelector(`.file-node[data-id="${id}"]`);
                    if (fileNode) {
                        fileNode.classList.remove('selected');
                    }
                });
                
                li.appendChild(fileInfo);
                li.appendChild(removeBtn);
                ul.appendChild(li);
            });
            
            selectedFilesList.appendChild(ul);
            document.getElementById('create-kb-btn').disabled = false;
        }
        
        // 创建知识库
        function createKnowledgeBase() {
            const kbName = document.getElementById('kb-name').value.trim() || 'feishu_kb';
            const fileTokens = Array.from(selectedFiles.keys());
            
            if (fileTokens.length === 0) {
                alert('请至少选择一个文件');
                return;
            }
            
            const statusMessage = document.getElementById('status-message');
            statusMessage.className = 'status-message';
            statusMessage.textContent = '正在创建知识库...';
            
            // 调用API创建知识库
            fetch('/api/feishu/build_kb', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    file_tokens: fileTokens,
                    user_access_token: userToken,
                    kb_name: kbName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusMessage.className = 'status-message success';
                    statusMessage.innerHTML = `
                        <p>知识库创建成功</p>
                        <p>知识库名称: ${data.kb_name}</p>
                        <p>文件总数: ${data.total_files}</p>
                        <p>成功处理: ${data.successful_files}</p>
                        <p>Token数量: ${data.token_size}</p>
                    `;
                } else {
                    statusMessage.className = 'status-message error';
                    statusMessage.innerHTML = `
                        <p>知识库创建失败: ${data.error}</p>
                        <p>失败文件数: ${data.failed_files ? data.failed_files.length : 0}</p>
                    `;
                }
            })
            .catch(error => {
                statusMessage.className = 'status-message error';
                statusMessage.textContent = `创建知识库出错: ${error.message}`;
            });
        }
        
        // 搜索文件
        function searchFiles() {
            const searchTerm = document.getElementById('search-files').value.toLowerCase();
            const allFileNodes = document.querySelectorAll('.file-node');
            const allFolderNodes = document.querySelectorAll('.folder-node');
            
            if (!searchTerm) {
                // 恢复原始状态
                allFileNodes.forEach(node => {
                    node.style.display = '';
                });
                allFolderNodes.forEach(node => {
                    node.style.display = '';
                });
                return;
            }
            
            // 首先隐藏所有节点
            allFileNodes.forEach(node => {
                const fileName = node.getAttribute('data-name').toLowerCase();
                if (fileName.includes(searchTerm)) {
                    node.style.display = ''; // 显示匹配的文件
                    
                    // 确保父文件夹可见
                    let parent = node.parentElement;
                    while (parent) {
                        if (parent.classList && parent.classList.contains('children')) {
                            parent.style.display = 'block';
                            const parentFolder = parent.parentElement;
                            if (parentFolder && parentFolder.classList.contains('folder-node')) {
                                parentFolder.classList.add('open');
                                parentFolder.style.display = '';
                            }
                        }
                        parent = parent.parentElement;
                    }
                } else {
                    node.style.display = 'none'; // 隐藏不匹配的文件
                }
            });
            
            // 处理文件夹的可见性
            allFolderNodes.forEach(node => {
                const folderName = node.querySelector('.folder-name').textContent.toLowerCase();
                const hasVisibleChildren = node.querySelector('.children')?.querySelector('li[style="display: ;"]');
                
                if (folderName.includes(searchTerm) || hasVisibleChildren) {
                    node.style.display = '';
                    if (hasVisibleChildren) {
                        node.classList.add('open');
                    }
                    
                    // 确保父文件夹可见
                    let parent = node.parentElement;
                    while (parent) {
                        if (parent.classList && parent.classList.contains('children')) {
                            parent.style.display = 'block';
                            const parentFolder = parent.parentElement;
                            if (parentFolder && parentFolder.classList.contains('folder-node')) {
                                parentFolder.classList.add('open');
                                parentFolder.style.display = '';
                            }
                        }
                        parent = parent.parentElement;
                    }
                } else {
                    node.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
