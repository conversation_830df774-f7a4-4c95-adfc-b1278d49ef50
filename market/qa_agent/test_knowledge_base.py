import os
import sys
from models.knowledge_base_model import LocalFileKnowledgeBase

# 设置项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_knowledge_base():
    try:
        print("开始测试知识库加载...")
        kb = LocalFileKnowledgeBase(
            "test_base",
            "C:\\english_name\\market\\AI-learning\\llm_for_a\\knowledge_base.py\\尽调报告\\knowledge_bases\\*.json"
        )
        print("知识库对象创建成功")
        
        print("正在读取内容...")
        content = kb.read()
        print(f"成功读取内容，长度：{len(content)}")
        
        print("获取知识库信息...")
        info = kb.to_dict()
        print("\n知识库内容预览(前500字符):", content[:500])
        print("\n知识库信息:", info)
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_knowledge_base()
