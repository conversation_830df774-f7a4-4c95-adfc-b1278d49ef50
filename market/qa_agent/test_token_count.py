import asyncio
import logging
import toml
import os
from models.feishu_knowledge_base import FeishuKnowledgeBase

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_feishu_token_counting():
    try:
        # 加载配置
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.toml")
        config = toml.load(config_path)
        feishu_config = config['knowledge_bases']['feishu']
        
        logger.info("=== 测试飞书知识库token计数 ===")
        logger.info(f"使用配置: {feishu_config}")
        
        # 初始化飞书知识库
        kb = FeishuKnowledgeBase(
            folder_token=feishu_config['folder_token'],
            user_access_token=feishu_config['user_access_token']
        )
        
        # 1. 获取初始知识库信息
        kb_info = kb.to_dict()
        logger.info("初始知识库信息:")
        logger.info(f"类型: {kb_info.get('type')}")
        logger.info(f"路径: {kb_info.get('path')}")
        logger.info(f"Token数量: {kb_info.get('token_size')}")
        logger.info(f"文档数量: {kb_info.get('doc_count')}")
        
        # 2. 手动重新计算token
        logger.info("\n=== 重新计算token数量 ===")
        recalculated_tokens = kb.recalculate_token_count()
        logger.info(f"重新计算得到的token数量: {recalculated_tokens}")
        
        # 3. 再次获取知识库信息，验证更新
        kb_info_after = kb.to_dict()
        logger.info("\n重新计算后的知识库信息:")
        logger.info(f"Token数量: {kb_info_after.get('token_size')}")
        
        # 4. 验证文档内容
        total_chars = 0
        doc_ids = kb.get_document_list()
        logger.info(f"\n文档总数: {len(doc_ids)}")
        
        for i, doc_id in enumerate(doc_ids, 1):
            content = kb._doc_contents.get(doc_id)
            if content:
                doc_len = len(content)
                total_chars += doc_len
                logger.info(f"文档 {i} (ID: {doc_id}): {doc_len} 字符")
            else:
                logger.warning(f"文档 {i} (ID: {doc_id}): 未缓存内容")
        
        logger.info(f"\n文档内容总字符数: {total_chars}")
        logger.info(f"知识库报告的token数: {kb_info_after.get('token_size')}")
        
        # 5. 如果字符数与token数不匹配，尝试刷新
        if total_chars > 0 and kb_info_after.get('token_size', 0) == 0:
            logger.warning("警告: 文档内容非空但token数为0，尝试重新获取文档内容...")
            
            # 清除缓存重新获取
            kb._doc_contents = {}
            kb._token_size = 0
            
            for doc_id in doc_ids:
                content = await kb.get_document_content(doc_id)
                logger.info(f"重新获取文档 {doc_id}: {len(content) if content else 0} 字符")
            
            # 最终验证
            final_tokens = kb.recalculate_token_count()
            logger.info(f"最终token数: {final_tokens}")
            
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_feishu_token_counting())
