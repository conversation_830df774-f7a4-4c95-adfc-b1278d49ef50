# 飞书知识库树状结构选择器使用指南

## 概述

飞书知识库树状结构选择器允许用户浏览飞书云空间中的所有文件，并选择特定文件添加到知识库中。通过直观的树状结构界面，用户可以轻松地找到并选择需要的文档。

## 功能特点

- **树状结构展示**：以层级结构显示飞书云空间中的所有文件和文件夹
- **文件搜索**：支持按文件名搜索，快速找到需要的文件
- **文件多选**：可同时选择多个文件，批量添加到知识库
- **知识库命名**：自定义知识库名称
- **自动处理文档**：自动获取文档内容，转换为知识库可用的格式

## 使用步骤

1. **访问选择器页面**
   - 在浏览器中访问 http://localhost:5000/feishu/tree_selector

2. **输入飞书访问令牌**
   - 输入有效的飞书用户访问令牌
   - 点击"加载文件树"按钮

3. **浏览和选择文件**
   - 点击文件夹图标展开/折叠文件夹
   - 点击文件名选择/取消选择文件
   - 选中的文件会显示在右侧列表中

4. **搜索文件**
   - 在搜索框中输入文件名关键字
   - 搜索结果会自动过滤并高亮显示

5. **创建知识库**
   - 输入知识库名称
   - 点击"创建知识库"按钮
   - 等待处理完成，查看创建状态

## API参考

### 获取飞书文件树状结构

**接口**：`GET /api/feishu/tree`

**参数**：
- `user_access_token`：飞书用户访问令牌（必需）
- `folder_token`：文件夹ID（可选，不提供则获取所有文件）

**响应**：包含文件树状结构的JSON对象

### 获取单个文件内容

**接口**：`POST /api/feishu/file_content`

**请求体**：
```json
{
  "file_token": "文件ID",
  "user_access_token": "飞书用户访问令牌"
}
```

**响应**：文件内容（Markdown格式）

### 创建知识库

**接口**：`POST /api/feishu/build_kb`

**请求体**：
```json
{
  "file_tokens": ["文件ID1", "文件ID2", ...],
  "user_access_token": "飞书用户访问令牌",
  "kb_name": "知识库名称"
}
```

**响应**：知识库创建状态

## 启动服务

运行以下命令启动服务：

```bash
python test_feishu_tree_selector.py
```

然后在浏览器中访问 http://localhost:5000/feishu/tree_selector
