# 飞书知识库API文档

本文档介绍了用于飞书知识库集成的两个API接口，用于获取飞书文档的树状结构和文件内容。

## API概览

### 1. 获取飞书文档树状结构

**接口**：`GET /api/feishu/tree`

**说明**：获取飞书文件夹的树状结构，或者获取用户云空间中所有文件（不指定folder_token）。

**参数**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| folder_token | string | 否 | 飞书文件夹token，**不提供则获取用户云空间中所有文件**（类似飞书云空间根目录） |
| user_access_token | string | 是 | 飞书用户访问令牌 |
| include_types | string | 否 | 逗号分隔的文件类型列表，例如"doc,sheet,docx" |
| exclude_types | string | 否 | 逗号分隔的要排除的文件类型列表 |
| max_depth | integer | 否 | 最大递归深度，默认为10 |

**返回示例**：
```json
{
  "tree": [
    {
      "id": "fldcnQR7GfBENGxc1RMciWkAg2c",
      "name": "示例文件夹",
      "type": "folder",
      "parent_id": "root",
      "created_time": "2023-08-01 10:20:30",
      "modified_time": "2023-08-02 15:30:45",
      "creator": "ou_xxx",
      "description": "",
      "children": [
        {
          "id": "L1jGdlwcEoNdXjxYt8pcofPBnye",
          "name": "示例文档.docx",
          "type": "docx",
          "parent_id": "fldcnQR7GfBENGxc1RMciWkAg2c",
          "created_time": "2023-08-01 10:25:30",
          "modified_time": "2023-08-01 11:30:45",
          "creator": "ou_xxx",
          "description": "",
          "editable": true
        }
      ],
      "is_empty": false
    }
  ],
  "metadata": {
    "total_files": 1,
    "total_folders": 1,
    "root_folder_token": "root",
    "timestamp": "2023-08-03 12:30:45"
  }
}
```

### 2. 获取单个飞书文件内容

**接口**：`POST /api/feishu/file_content`

**说明**：获取单个飞书文件的内容（markdown格式）

**请求体**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file_token | string | 是 | 要获取的文件ID |
| folder_token | string | 否 | 飞书文件夹token（可选，不提供也可以正常获取文件内容） |
| user_access_token | string | 是 | 飞书用户访问令牌 |

**返回示例**：
```json
{
  "content": "# 示例文档\n\n这是一个示例文档的内容..."
}
```

## 使用示例

### 获取文档树结构

#### 示例1：获取用户云空间中所有文件

```python
import requests

# 用户访问令牌
user_access_token = "u-xxxx..."

# 不指定folder_token，获取用户云空间中所有文件
url = f"http://localhost:5000/api/feishu/tree?user_access_token={user_access_token}"
response = requests.get(url)

if response.status_code == 200:
    data = response.json()
    # 处理返回的树状结构
    tree = data["tree"]
    print(f"获取到的根节点数量: {len(tree)}")
    print(f"获取到的文件数: {data['metadata']['total_files']}")
    print(f"获取到的文件夹数: {data['metadata']['total_folders']}")
    
    # 输出前几个文件/文件夹
    for item in tree[:5]:
        print(f"- {item.get('name')} ({item.get('type')})")
```

#### 示例2：获取特定文件夹的内容

```python
import requests

# 用户访问令牌和文件夹token
user_access_token = "u-xxxx..."
folder_token = "fldcnxxx..."

# 指定folder_token，获取特定文件夹内容
url = f"http://localhost:5000/api/feishu/tree?folder_token={folder_token}&user_access_token={user_access_token}"
response = requests.get(url)

if response.status_code == 200:
    data = response.json()
    # 处理返回的树状结构
    print(f"获取到的文件数: {data['metadata']['total_files']}")
    print(f"获取到的文件夹数: {data['metadata']['total_folders']}")
```

### 获取单个文件内容

```python
import requests

# 用户访问令牌和文件ID
user_access_token = "u-xxxx..."
file_token = "L1jxxx..."

# 请求文件内容
url = "http://localhost:5000/api/feishu/file_content"
payload = {
    "file_token": file_token,
    "user_access_token": user_access_token
}

response = requests.post(url, json=payload)

if response.status_code == 200:
    data = response.json()
    content = data.get("content", "")
    print(f"文件内容长度: {len(content)} 字符")
    # 处理文件内容...
```

## 注意事项

1. `user_access_token` 是必须的，用于验证用户身份和获取文件权限
2. 如果服务器配置了默认的 `folder_token` 和 `user_access_token`，可以不在请求中提供
3. 文件内容会以markdown格式返回，适合直接展示或进一步处理
4. 大文件获取可能需要较长时间，请设置合理的超时时间
