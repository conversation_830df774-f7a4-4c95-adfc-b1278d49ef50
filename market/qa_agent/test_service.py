import os
import sys

from typing import Dict, List

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.llm import local_model_list

class Service:
    def __init__(self) -> None:
        pass

    def word_analyze(self, file_path: str) -> Dict:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File {file_path} not found")

        return {
            "success": True,
            "file_path": file_path.replace(".docx", "_modified.docx") + ".docx"
        }

    def query_preview(self, prompt: str, kl_ids: List[str]) -> Dict:
        """
        kl_ids: knowledge_list 返回的 key
        """
        prompt = prompt.replace("{{knowledges_str}}", "knowledge")
        return {"prompt": prompt}

    def query(self, prompt: str, kl_ids: List[str], llm_config: Dict, streamable: bool = False) -> Dict:
        """
        kl_ids: knowledge_list 返回的 key
        llm_config: get_model 的参数, dict 类型
        """
        return {"公司中文名称": "上海衍复投资管理有限公司"}

    def knowledge_list(self) -> Dict:
        return {
            "3908c61972db551eac282f49bd1c1eb4": {
                "type": "local_file",
                "path": "/knowledge_base.py/尽调报告/knowledge_bases/代销机构代销业务.json",
            },
            "3219568e3804fb63991a6764c4b999e2": {
                "type": "local_file",
                "path": "/knowledge_base.py/尽调报告/knowledge_bases/代销机构资管部门.json",
            },
            "c8b6375e080e3a4f99228f366942ba3d": {
                "type": "local_file",
                "path": "/knowledge_base.py/尽调报告/knowledge_bases/直销客户.json",
            },
        }

    def llm_model_list(self) -> Dict:
        return {x: {"model_name": x} for x in local_model_list}

service = Service()
