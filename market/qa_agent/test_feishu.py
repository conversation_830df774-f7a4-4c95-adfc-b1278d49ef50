import asyncio
import logging
import toml
from llm_for_a.market.qa_agent.models.feishu_knowledge_base import FeishuKnowledgeBase

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_feishu_kb():
    try:
        # 初始化飞书知识库
        kb = FeishuKnowledgeBase(
            folder_token="FMMufxN0ylK9PJdHcvWcOSornWd",
            user_access_token="u-dDNV30mERdaq4_GbwKKuOy1gl2Gg112hUO00lkE88yOk"
        )
        
        # 测试to_dict方法
        kb_info = kb.to_dict()
        logger.info("知识库信息:")
        logger.info(f"类型: {kb_info.get('type')}")
        logger.info(f"路径: {kb_info.get('path')}")
        logger.info(f"Token数量: {kb_info.get('token_size')}")
        logger.info(f"文档数量: {kb_info.get('doc_count')}")
        
        # 测试read方法
        content = kb.read(with_file_name=True)
        logger.info(f"\n文档内容预览 (前500字符):\n{content[:500]}")
        
        # 测试搜索功能
        query = "公司简介"
        results = await kb.search(query, top_k=2)
        logger.info(f"\n搜索结果 (查询: {query}):")
        for i, result in enumerate(results, 1):
            logger.info(f"\n结果 {i}:")
            logger.info(f"文档ID: {result['doc_id']}")
            logger.info(f"相关度: {result['score']}")
            logger.info(f"内容片段: {result['content'][:200]}")
            
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)

async def test_markdown_conversion():
    # 加载配置
    config = toml.load("config.toml")
    feishu_config = config['knowledge_bases']['feishu']
    
    # 初始化飞书知识库
    kb = FeishuKnowledgeBase(
        folder_token=feishu_config['folder_token'],
        user_access_token=feishu_config['user_access_token']
    )
    
    # 获取文档列表
    doc_ids = kb.get_document_list()
    print(f"\n找到 {len(doc_ids)} 个文档:")
    
    # 获取每个文档的内容
    for i, doc_id in enumerate(doc_ids, 1):
        print(f"\n=== 文档 {i} (ID: {doc_id}) ===")
        content = await kb.get_document_content(doc_id)
        if content:
            print("\n文档内容预览（前1000字符）:")
            print("-" * 50)
            print(content[:1000])
            print("-" * 50)
            print(f"文档总长度: {len(content)} 字符")
        else:
            print("获取文档内容失败")

if __name__ == "__main__":
    asyncio.run(test_feishu_kb())
    asyncio.run(test_markdown_conversion())