import logging
import traceback

from flask import Flask, Response, jsonify, request, stream_with_context
from test_service import service


logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


app = Flask(__name__)


@app.route("/api/query", methods=["POST"])
def query():
    data = request.json
    if not data:
        return jsonify({"error": "Invalid request data"}), 400

    prompt = data.get("prompt")
    kl_ids = data.get("kl_ids")
    llm_config = data.get("llm_config")
    streamable = data.get("streamable", False)

    return jsonify({"data": service.query(prompt, kl_ids, llm_config, streamable)})

@app.route("/api/query_preview", methods=["POST"])
def query_preview():
    data = request.json
    if not data:
        return jsonify({"error": "Invalid request data"}), 400
    
    prompt = data.get("prompt")
    kl_ids = data.get("kl_ids")

    return jsonify({"data": service.query_preview(prompt, kl_ids)})


@app.route("/api/llm_model_list", methods=["GET"])
def llm_model_list():
    return jsonify({"data": service.llm_model_list()})


@app.route("/api/knowledge_list", methods=["GET"])
def knowledge_list():
    return jsonify({"data": service.knowledge_list()})


@app.route("/api/word_analyze", methods=["GET"])
def word_analyze():
    file_path = request.args.get("file_path")
    if not file_path:
        return jsonify({"error": "Invalid request data"}), 400

    return jsonify(service.word_analyze(file_path))


@app.errorhandler(Exception)
def handle_error(error):
    logger.error(f"发生错误: {str(error)}")
    logger.error(f"错误详情: {traceback.format_exc()}")

    if isinstance(error, FileNotFoundError):
        return jsonify({"error": "文件未找到", "message": str(error)}), 404

    return jsonify({"error": "服务器内部错误", "message": str(error)}), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=3000)
