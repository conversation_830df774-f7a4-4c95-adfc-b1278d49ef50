import json
import urllib.parse
import datetime
import os
import toml
from flask import Blueprint, request, jsonify, make_response
import requests
from llm_for_a.market.qa_agent.models.feishu_token_util import sync_all_feishu_tokens

feishu_auth_bp = Blueprint('feishu_auth', __name__, url_prefix='')

FEISHU_APP_ID = "cli_a8fe2f0835bdd00c"
FEISHU_APP_SECRET = "GawFcmgUgkp6JN4464iRSb6G4arh5r4J"  # 实际的APP Secret
FEISHU_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
FEISHU_USER_INFO_URL = "https://open.feishu.cn/open-apis/authen/v1/user_info"

# 配置文件路径
config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.toml")

@feishu_auth_bp.route('/api/feishu/auth/callback', methods=['GET'])
def feishu_callback():
    """处理飞书OAuth回调"""
    code = request.args.get('code')
    if not code:
        return jsonify({"error": "No authorization code provided"}), 400

    try:
        # 1. 获取app_access_token（可选，部分接口用）
        app_token_resp = requests.post(
            FEISHU_TOKEN_URL,
            json={
                "app_id": FEISHU_APP_ID,
                "app_secret": FEISHU_APP_SECRET
            }
        )
        print("飞书 app_access_token 响应原文：", app_token_resp.text)  # 新增日志
        app_token_data = app_token_resp.json()
        app_access_token = app_token_data.get('app_access_token')
        
        if not app_access_token:
            return jsonify({"error": "Failed to get app access token"}), 500
        
        # 2. 用 code 换 user_access_token
        user_token_resp = requests.post(
            "https://open.feishu.cn/open-apis/authen/v1/access_token",
            headers={
                "Authorization": f"Bearer {app_access_token}",
                "Content-Type": "application/json"
            },
            json={
                "grant_type": "authorization_code",
                "code": code
            }
        )
        print("飞书 user_access_token 响应原文：", user_token_resp.text)
        user_token_data = user_token_resp.json()
        user_access_token = user_token_data.get('data', {}).get('access_token')
        if not user_access_token:
            return jsonify({"error": "Failed to get user access token", "raw": user_token_data}), 500

        # 3. 用 user_access_token 获取 user_info
        user_info_resp = requests.get(
            FEISHU_USER_INFO_URL,
            headers={
                "Authorization": f"Bearer {user_access_token}",
                "Content-Type": "application/json"
            }
        )
        print("飞书 user_info 响应原文：", user_info_resp.text)
        user_info = user_info_resp.json()
        # 合并 user_access_token 到 user_info['data']
        if 'data' in user_info:
            user_info['data']['access_token'] = user_access_token

        if user_info.get('code') != 0:
            return jsonify({"error": "Failed to get user info", "detail": user_info}), 500
            
        # 【新增】4. 直接更新配置文件中的 user_access_token
        try:
            print("\n========== 开始更新配置文件中的 token ==========")
            # 读取配置文件
            config = toml.load(config_path)
            # 更新飞书token
            if 'knowledge_bases' in config and 'feishu' in config['knowledge_bases']:
                old_token = config['knowledge_bases']['feishu'].get('user_access_token', '')
                # 确保使用正确的 token 字段 (从 user_access_token 改为实际返回的 access_token)
                config['knowledge_bases']['feishu']['user_access_token'] = user_access_token
                
                # 写回配置文件
                with open(config_path, 'w', encoding='utf-8') as f:
                    toml.dump(config, f)
                print(f"成功更新配置文件 {config_path}")
                print(f"飞书 token 更新: {old_token[:5] if old_token else '(无)'} → {user_access_token[:5]}...")
                
                # 重新读取配置文件确认更新成功
                try:
                    updated_config = toml.load(config_path)
                    updated_token = updated_config.get('knowledge_bases', {}).get('feishu', {}).get('user_access_token', '')
                    if updated_token == user_access_token:
                        print(f"✅ 验证成功: config.toml 已包含最新 token: {updated_token[:5]}...")
                    else:
                        print(f"⚠️ 验证失败: config.toml 中的 token 未更新: {updated_token[:5] if updated_token else '(空)'}")
                except Exception as verify_err:
                    print(f"验证 config.toml 更新失败: {str(verify_err)}")
                
                # 【新增】5. 主动刷新知识库
                try:
                    print("\n========== 主动刷新知识库 ==========")
                    # 内部调用刷新知识库接口
                    refresh_url = 'http://localhost:5000/api/refresh_knowledge_bases'
                    refresh_headers = {'Authorization': f'Bearer {user_access_token}'}
                    refresh_resp = requests.post(refresh_url, headers=refresh_headers)
                    print(f"\n主动刷新知识库结果: HTTP {refresh_resp.status_code}")
                    print(f"响应内容: {refresh_resp.text[:200]}...")
                except Exception as refresh_err:
                    print(f"刷新知识库失败: {str(refresh_err)}")
            else:
                print("\n警告: 配置文件中没有找到 knowledge_bases.feishu 节点")
        except Exception as config_err:
            print(f"更新配置文件失败: {str(config_err)}")
        
        # 【新增】4.1 自动同步所有 feishu 分库 token
        try:
            sync_all_feishu_tokens(user_access_token, config_path)
            print(f"已自动同步所有 feishu 分库 user_access_token 为最新 token: {user_access_token[:5]}...")
        except Exception as sync_err:
            print(f"自动同步所有 feishu 分库 token 失败: {str(sync_err)}")
        
        # 3. 返回登录成功响应
        # 使用HTTPS和3000端口
        origin = request.headers.get('Origin', 'https://localhost:3000')
        callback_url = f"{origin}/feishu_oauth_callback.html"
        # 将用户信息编码到URL参数中
        user_data = json.dumps(user_info)
        encoded_data = urllib.parse.quote(user_data)
        redirect_url = f"{callback_url}?data={encoded_data}"
        
        # 输出详细的调试信息
        print("\n\n========== 飞书授权回调详细信息 ==========")
        print(f"时间: {datetime.datetime.now()}")
        print(f"接收到的临时授权码(code): {code}")
        print(f"完整的请求URL: {request.url}")
        print(f"App Token 响应: {json.dumps(app_token_data, indent=2, ensure_ascii=False)}")
        print(f"用户信息API响应: {json.dumps(user_info, indent=2, ensure_ascii=False)}")
        print(f"重定向URL: {redirect_url}")
        
        # 记录关键的令牌信息
        print("\n========== 用户认证信息详情 ==========")
        print(f"access_token: {user_info.get('data', {}).get('access_token', '未获取到')}")
        print(f"refresh_token: {user_info.get('data', {}).get('refresh_token', '未获取到')}")
        print(f"token类型: {user_info.get('data', {}).get('token_type', '未获取到')}")
        print(f"过期时间: {user_info.get('data', {}).get('expires_in', '未获取到')} 秒")
        print(f"open_id: {user_info.get('data', {}).get('open_id', '未获取到')}")
        print(f"tenant_key: {user_info.get('data', {}).get('tenant_key', '未获取到')}")
        print(f"user_id: {user_info.get('data', {}).get('user_id', '未获取到')}")
        
        # 确保 data 中包含必要的 token 和 open_id
        if not user_info.get('data', {}).get('access_token') or not user_info.get('data', {}).get('open_id'):
            print(f"警告：用户信息缺少必要的 token 或 open_id: {user_info}")
            # 尝试补充信息
            if user_info.get('data') and not user_info.get('data').get('access_token'):
                user_info['data']['access_token'] = app_access_token  # 备用方案
                print("已使用app_access_token作为备用access_token")
        
        response = make_response(f'''
            <!DOCTYPE html>
            <html>
            <head>
                <title>飞书授权成功</title>
                <script>
                    // 添加成功消息到控制台
                    console.log("飞书授权成功，用户数据:", {json.dumps(user_info)});
                    // 通过 postMessage 通知父窗口
                    try {{
                        if (window.opener) {{
                            window.opener.postMessage({{
                                type: "feishu-login-success",
                                data: {json.dumps(user_info)}
                            }}, "*");
                            console.log("已向父窗口发送消息，准备关闭弹窗");
                            setTimeout(function() {{ window.close(); }}, 500); // 延迟关闭，确保消息送达
                        }} else {{
                            // 非弹窗环境，直接跳转回 feishu_oauth_callback.html
                            window.location.href = "{redirect_url}";
                        }}
                    }} catch(e) {{
                        console.error("向父窗口发送消息失败:", e);
                        window.location.href = "{redirect_url}";
                    }}
                </script>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding-top: 100px; }}
                    .success {{ color: green; font-size: 18px; }}
                </style>
            </head>
            <body>
                <div class="success">授权成功，正在返回应用...</div>
                <p>如果页面没有自动跳转，请<a href="{redirect_url}" id="manual-redirect">点击这里</a>手动返回。</p>
                <script>
                    // 5秒后如果还在此页面，尝试再次通过点击触发跳转
                    setTimeout(function() {{
                        if (!window.opener) {{
                            document.getElementById('manual-redirect').click();
                        }}
                    }}, 5000);
                </script>
            </body>
            </html>
        ''')
          # 设置CORS头部，确保跨域通信正常
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response
        
    except Exception as e:
        print("\n\n========== 飞书授权回调异常 ==========")
        print(f"时间: {datetime.datetime.now()}")
        print(f"异常类型: {type(e).__name__}")
        print(f"异常信息: {str(e)}")
        print(f"请求URL: {request.url}")
        print(f"请求参数: {dict(request.args)}")
        print(f"临时授权码(code): {code}")
        import traceback
        print("详细堆栈信息:")
        print(traceback.format_exc())
          # 创建一个带有错误信息的HTML页面
        error_message = str(e).replace('"', '\\"')
        quoted_error = urllib.parse.quote(str(e))
        error_html = f'''
            <!DOCTYPE html>
            <html>
            <head>
                <title>飞书授权失败</title>                <script>
                    console.error("飞书授权处理失败:", "{error_message}");
                    
                    // 尝试重定向回应用并携带错误信息
                    const origin = "https://localhost:3000";
                    const errorUrl = origin + "/taskpane.html?feishu_auth=error&error_message={quoted_error}";
                    
                    // 3秒后重定向
                    setTimeout(function() {{
                        window.location.href = errorUrl;
                    }}, 3000);
                </script>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding-top: 100px; }}
                    .error {{ color: red; font-size: 18px; }}
                </style>
            </head>
            <body>
                <div class="error">授权处理失败，正在返回应用...</div>
                <p>错误信息: {str(e).replace('<', '&lt;').replace('>', '&gt;')}</p>                <p>如果页面没有自动跳转，请<a href="https://localhost:3000/taskpane.html">点击这里</a>手动返回。</p>
            </body>
            </html>
        '''
        
        error_response = make_response(error_html)
        
        # 设置CORS头部
        error_response.headers.add('Access-Control-Allow-Origin', '*')
        error_response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        error_response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        
        return error_response

@feishu_auth_bp.route('/feishu_oauth_callback', methods=['GET'])
def feishu_oauth_callback():
    """处理飞书OAuth回调 - 备用路由"""
    # 记录请求信息以便调试
    print(f"收到飞书OAuth回调请求: {request.url}")
    print(f"请求参数: {dict(request.args)}")
    print(f"请求头: {dict(request.headers)}")
    
    # 直接重用原有的回调逻辑
    return feishu_callback()
