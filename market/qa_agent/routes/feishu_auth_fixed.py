import json
import urllib.parse
from flask import Blueprint, request, jsonify, make_response
import requests

feishu_auth_bp = Blueprint('feishu_auth', __name__, url_prefix='')

FEISHU_APP_ID = "cli_a8fe2f0835bdd00c"
FEISHU_APP_SECRET = "yE5tLm1MTMWY6mRE3SqvJhBzMn3EdzgX"  # 实际的APP Secret
FEISHU_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
FEISHU_USER_INFO_URL = "https://open.feishu.cn/open-apis/authen/v1/user_info"

@feishu_auth_bp.route('/api/feishu/auth/callback', methods=['GET'])
def feishu_callback():
    """处理飞书OAuth回调"""
    code = request.args.get('code')
    if not code:
        return jsonify({"error": "No authorization code provided"}), 400

    try:
        # 1. 获取app_access_token
        app_token_resp = requests.post(
            FEISHU_TOKEN_URL,
            json={
                "app_id": FEISHU_APP_ID,
                "app_secret": FEISHU_APP_SECRET
            }
        )
        app_token_data = app_token_resp.json()
        app_access_token = app_token_data.get('app_access_token')
        
        if not app_access_token:
            return jsonify({"error": "Failed to get app access token"}), 500
        
        # 2. 使用授权码获取用户信息
        user_info_resp = requests.get(
            FEISHU_USER_INFO_URL,
            headers={
                "Authorization": f"Bearer {app_access_token}",
                "Content-Type": "application/json"
            },
            params={"code": code}
        )
        user_info = user_info_resp.json()

        if user_info.get('code') != 0:
            return jsonify({"error": "Failed to get user info"}), 500
        
        # 3. 返回登录成功响应
        # 统一使用HTTPS和3000端口
        origin = request.headers.get('Origin', 'https://localhost:3000')
        taskpane_url = f"{origin}/taskpane.html"
        
        # 将用户信息编码到URL参数中
        user_data = json.dumps(user_info)
        encoded_data = urllib.parse.quote(user_data)
        
        redirect_url = f"{taskpane_url}?feishu_auth=success&data={encoded_data}"
        
        # 输出调试信息
        print(f"重定向URL: {redirect_url}")
        print(f"用户信息: {user_info}")
        
        # 确保 data 中包含必要的 token 和 open_id
        if not user_info.get('data', {}).get('access_token') or not user_info.get('data', {}).get('open_id'):
            print(f"警告：用户信息缺少必要的 token 或 open_id: {user_info}")
            # 尝试补充信息
            if user_info.get('data') and not user_info.get('data').get('access_token'):
                user_info['data']['access_token'] = app_access_token  # 备用方案
        
        response = make_response(f'''
            <!DOCTYPE html>
            <html>
            <head>
                <title>飞书授权成功</title>
                <script>
                    // 添加成功消息到控制台
                    console.log("飞书授权成功，用户数据:", {json.dumps(user_info)});
                    
                    // 首先尝试直接通过 postMessage 与父窗口通信
                    try {{
                        if (window.opener) {{
                            window.opener.postMessage({{
                                type: "feishu-login-success",
                                data: {json.dumps(user_info)}
                            }}, "*");
                            console.log("已向父窗口发送消息");
                        }}
                    }} catch(e) {{
                        console.error("向父窗口发送消息失败:", e);
                    }}
                    
                    // 然后无论如何都执行重定向
                    window.location.href = "{redirect_url}";
                </script>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding-top: 100px; }}
                    .success {{ color: green; font-size: 18px; }}
                </style>
            </head>
            <body>
                <div class="success">授权成功，正在返回应用...</div>
                <p>如果页面没有自动跳转，请<a href="{redirect_url}" id="manual-redirect">点击这里</a>手动返回。</p>
                <script>
                    // 5秒后如果还在此页面，尝试再次通过点击触发跳转
                    setTimeout(function() {{
                        document.getElementById('manual-redirect').click();
                    }}, 5000);
                </script>
            </body>
            </html>
        ''')
        
        # 设置CORS头部，确保跨域通信正常
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response

    except Exception as e:
        print(f"授权回调处理异常: {str(e)}")
        import traceback
        print(traceback.format_exc())
        
        # 创建一个带有错误信息的HTML页面
        error_html = f'''
            <!DOCTYPE html>
            <html>
            <head>
                <title>飞书授权失败</title>
                <script>
                    console.error("飞书授权处理失败:", "{str(e).replace('"', '\\"')}");
                    
                    // 尝试重定向回应用并携带错误信息
                    const origin = "https://localhost:3000";
                    const errorUrl = origin + "/taskpane.html?feishu_auth=error&error_message={urllib.parse.quote(str(e))}";
                    
                    // 3秒后重定向
                    setTimeout(function() {{
                        window.location.href = errorUrl;
                    }}, 3000);
                </script>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding-top: 100px; }}
                    .error {{ color: red; font-size: 18px; }}
                </style>
            </head>
            <body>
                <div class="error">授权处理失败，正在返回应用...</div>
                <p>错误信息: {str(e).replace('<', '&lt;').replace('>', '&gt;')}</p>
                <p>如果页面没有自动跳转，请<a href="https://localhost:3000/taskpane.html">点击这里</a>手动返回。</p>
            </body>
            </html>
        '''
        
        error_response = make_response(error_html)
        
        # 设置CORS头部
        error_response.headers.add('Access-Control-Allow-Origin', '*')
        error_response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        error_response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        
        return error_response

@feishu_auth_bp.route('/feishu_oauth_callback', methods=['GET'])
def feishu_oauth_callback():
    """处理飞书OAuth回调 - 备用路由"""
    # 记录请求信息以便调试
    print(f"收到飞书OAuth回调请求: {request.url}")
    print(f"请求参数: {dict(request.args)}")
    print(f"请求头: {dict(request.headers)}")
    
    # 直接重用原有的回调逻辑
    return feishu_callback()
