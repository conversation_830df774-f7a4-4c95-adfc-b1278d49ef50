import os
import rumps


from pathlib import Path
from threading import Thread
from werkzeug.serving import make_server


config_folder_path = Path.home() / ".word_auto_fill"
config_path = config_folder_path / "config.toml"


class ServerThread(Thread):
    def __init__(self, app):
        Thread.__init__(self)
        self.server = make_server("0.0.0.0", 5002, app)
        self.ctx = app.app_context()
        self.ctx.push()

    def run(self):
        self.server.serve_forever()

    def shutdown(self):
        self.server.shutdown()


@rumps.clicked("启动")
def start(_):
    from market.qa_agent.server_v2 import app

    global server
    server = ServerThread(app)
    server.start()


@rumps.clicked("停止")
def stop(_):
    global server
    server.shutdown()


icon = (
    Path(__file__).parent.resolve()
    / "market"
    / "word-auto-fill"
    / "dist"
    / "favicon.ico"
)
app = rumps.App(
    "Word 自动填充", icon=str(icon), menu=["启动", "停止"], quit_button="退出"
)

if __name__ == "__main__":
    if not os.path.exists(str(config_folder_path)):
        os.mkdir(str(config_folder_path))

    if not os.path.exists(str(config_path)):
        with open(str(config_path), "w+") as file:
            file.write("[]")

    app.run()
