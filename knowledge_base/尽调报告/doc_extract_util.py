import docx
import pandas as pd
from docx import Document
import re
from docx.oxml.text.paragraph import CT_P
from docx.oxml.table import CT_Tbl, CT_Row, CT_Tc
from docx.text.run import Run
from typing import Optional, Dict, Any, List


def find_table_title(doc, table_index: int) -> str:
    """
    查找表格的标题
    """
    # 在表格前的段落中查找标题
    max_lookback = 3  # 最多往前看3个段落
    paragraphs = list(doc.paragraphs)
    table_paragraph_index = -1
    
    # 找到表格所在的段落位置
    for i, paragraph in enumerate(paragraphs):
        if any(run._element.getnext() is not None and run._element.getnext().tag.endswith('tbl') 
               for run in paragraph.runs):
            table_paragraph_index = i
            break
    
    if table_paragraph_index == -1:
        return ""
        
    # 往前查找标题
    for i in range(max_lookback):
        if table_paragraph_index - i - 1 < 0:
            break
        text = paragraphs[table_paragraph_index - i - 1].text.strip()
        # 如果段落包含"表"字且长度合适，认为是标题
        if "表" in text and len(text) < 50:
            return text
    
    return ""


def extract_table_content(table, title: str = "") -> Dict[str, Any]:
    """
    提取表格内容，保持表格结构
    """
    table_data = {
        "title": title,
        "columns": [],
        "rows": []
    }
    
    # 提取表头
    if table.rows:
        header_row = table.rows[0]
        table_data["columns"] = [cell.text.strip() for cell in header_row.cells if cell.text.strip()]
        
        # 提取数据行
        for row in table.rows[1:]:
            row_data = {}
            for i, cell in enumerate(row.cells):
                if i < len(table_data["columns"]):
                    header = table_data["columns"][i]
                    value = cell.text.strip()
                    if value:  # 只添加非空值
                        row_data[header] = value
            if row_data:  # 只添加非空行
                table_data["rows"].append(row_data)
    
    return table_data


def clean_text(text: str) -> str:
    """清理文本，去除重复内容和无用字符"""
    # 去除连续的相同内容
    lines = text.split('\n')
    cleaned_lines = []
    for line in lines:
        # 去除行内重复
        parts = line.split('|')
        unique_parts = []
        for part in parts:
            part = part.strip()
            if part and part not in unique_parts:
                unique_parts.append(part)
        cleaned_line = ' | '.join(unique_parts)
        
        # 如果这一行不是前一行的重复，就添加它
        if not cleaned_lines or cleaned_line != cleaned_lines[-1]:
            cleaned_lines.append(cleaned_line)
    
    text = '\n'.join(cleaned_lines)
    
    # 去除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    return text


def extract_word_content(doc_path: str) -> Dict[str, Any]:
    """
    从Word文档中提取文本内容，保持结构化信息
    """
    try:
        doc = Document(doc_path)
        content = {
            "text_content": [],  # 普通文本内容
            "tables": []         # 表格内容
        }
        
        # 提取段落文本
        for para in doc.paragraphs:
            text = para.text.strip()
            if text:
                content["text_content"].append(clean_text(text))
        
        # 提取表格内容
        for i, table in enumerate(doc.tables):
            title = find_table_title(doc, i)
            table_data = extract_table_content(table, title)
            if table_data["columns"] and table_data["rows"]:  # 只添加非空表格
                content["tables"].append(table_data)
        
        return content
        
    except Exception as e:
        print(f"提取文档内容时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return {"text_content": [], "tables": []}


def convert_to_json(index, element):
    """
    将word文件转换为json，并保留位置信息。
    args:
        index: 当前元素的索引
        element: 当前元素
    """
    json_data = {}
    json_data["index"] = index
    if isinstance(element, docx.document.Document):
        json_data["type"] = "Document"
        json_data["content"] = [convert_to_json(index, element) for index, element in enumerate(element.element.body.inner_content_elements)]
    elif isinstance(element, CT_P):
        json_data["type"] = "Paragraph"
        json_data["content"] = element.text
    elif isinstance(element, CT_Tbl):
        json_data["type"] = "Table"
        json_data["content"] = [convert_to_json(tri, tr) for tri, tr in enumerate(element.tr_lst)]
    elif isinstance(element, CT_Row):
        json_data["type"] = "Row"
        json_data["content"] = [convert_to_json(tci, tc) for tci, tc in enumerate(element.tc_lst)]
    elif isinstance(element, CT_Tc):
        json_data["type"] = "Cell"
        json_data["content"] = [convert_to_json(index, element) for index, element in enumerate(element.inner_content_elements)]
    else:
        json_data["type"] = "Unknown element"
        json_data["content"] = "Unknown element"
    return json_data


def convert_to_json_with_xpath(element, xpath=""):
    """
    将word文件转换为json，并保留位置信息。
    args:
        index: 当前元素的索引
        element: 当前元素
        xpath: 节点的xpath
    """
    json_data = {}
    json_data['xpath'] = xpath
    if isinstance(element, docx.document.Document):
        json_data["type"] = "Document"
        json_data["content"] = [convert_to_json_with_xpath(element, f"{xpath}.[{index}]") for index, element in enumerate(element.element.body.inner_content_elements)]
    elif isinstance(element, CT_P):
        json_data["type"] = "Paragraph"
        json_data["content"] = element.text
    elif isinstance(element, CT_Tbl):
        json_data["type"] = "Table"
        json_data["content"] = [convert_to_json_with_xpath(tr, f"{xpath}.[{tri}]") for tri, tr in enumerate(element.tr_lst)]
    elif isinstance(element, CT_Row):
        json_data["type"] = "Row"
        json_data["content"] = [convert_to_json_with_xpath(tc, f"{xpath}.[{tci}]") for tci, tc in enumerate(element.tc_lst)]
    elif isinstance(element, CT_Tc):
        json_data["type"] = "Cell"
        json_data["content"] = [convert_to_json_with_xpath(element, f"{xpath}.[{index}]") for index, element in enumerate(element.inner_content_elements)]
    else:
        json_data["type"] = "Unknown element"
        json_data["content"] = "Unknown element"
    return json_data


def get_element(parent_element, position):
    """
    根据位置信息获取元素
    args:
        parent_element: 当前节点
        position: 目标节点相对当前节点的位置 {"child_position": {"index": 1, "child_position": None}}
    """
    if position is None:
        return parent_element
    if isinstance(parent_element, docx.document.Document):
        child_element = parent_element.element.body.inner_content_elements[position["index"]]
    elif isinstance(parent_element, CT_Tbl):
        child_element = parent_element.tr_lst[position["index"]]
    elif isinstance(parent_element, CT_Row):
        child_element = parent_element.tc_lst[position["index"]]
    elif isinstance(parent_element, CT_Tc):
        child_element = parent_element.inner_content_elements[position["index"]]
    else:
        return None
    return get_element(child_element, position["child_position"])


def get_element_by_xpath(element, xpath):
    """
    根据xpath获取元素
    args:
        element: 当前节点
        xpath: 目标节点的xpath
    """
    if not xpath:
        return element
    if isinstance(element, docx.document.Document):
        child_element = element.element.body.inner_content_elements[int(xpath[1:-1])]
    elif isinstance(element, CT_Tbl):
        child_element = element.tr_lst[int(xpath[1:-1])]
    elif isinstance(element, CT_Row):
        child_element = element.tc_lst[int(xpath[1:-1])]
    elif isinstance(element, CT_Tc):
        child_element = element.inner_content_elements[int(xpath[1:-1])]
    else:
        return None
    return get_element_by_xpath(child_element, xpath[xpath.find("].")+2:] if "]." in xpath else "")


def extract_text(element, final_text=True):
    """
    提取元素中的文本
    args:
        element: 当前节点
        final_text: 是否只返回最终文本
    """
    if isinstance(element, docx.document.Document):
        text = []
        for child_element in element.element.body.inner_content_elements:
            text.append(extract_text(child_element, final_text))
        return "\n".join(text) if final_text else text
    elif isinstance(element, CT_P):
        return element.text
    elif isinstance(element, CT_Tbl):
        text = []
        for tr in element.tr_lst:
            text.append(extract_text(tr, final_text))
        return "\n".join(text) if final_text else text
    elif isinstance(element, CT_Row):
        text = []
        for tc in element.tc_lst:
            text.append(extract_text(tc, final_text))
        return "\t".join(text) if final_text else text
    elif isinstance(element, CT_Tc):
        text = []
        for child_element in element.inner_content_elements:
            text.append(extract_text(child_element, final_text))
        return "\n".join(text) if final_text else text
    else:
        return ""


def get_table_elements(document):
    """
    获取文档中的所有表格元素
    """
    return [element for element in document.element.body.inner_content_elements if isinstance(element, CT_Tbl)]


def write_by_xpath(document, xpath, content):
    """
    根据xpath写入内容
    args:
        document: 文档对象
        xpath: 目标节点的xpath
        content: 要写入的内容
    """
    element = get_element_by_xpath(document, xpath)
    if isinstance(element, CT_P):
        element.text = content
    elif isinstance(element, CT_Tc):
        element.text = content 