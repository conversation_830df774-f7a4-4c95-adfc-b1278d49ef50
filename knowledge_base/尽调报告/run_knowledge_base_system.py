import os
import argparse
import subprocess
import logging
import traceback
from knowledge_base_generator import main as generate_knowledge_bases

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    try:
        parser = argparse.ArgumentParser(description='运行知识库系统')
        parser.add_argument('--generate', action='store_true', help='生成知识库')
        parser.add_argument('--run-app', action='store_true', help='运行Web应用')
        parser.add_argument('--test', action='store_true', help='运行测试')
        
        args = parser.parse_args()
        
        if not (args.generate or args.run_app or args.test):
            logger.warning("未指定任何操作，请使用 --generate, --run-app 或 --test 参数")
            parser.print_help()
            return
        
        if args.generate:
            logger.info("正在生成知识库...")
            try:
                generate_knowledge_bases()
                logger.info("知识库生成完成")
            except Exception as e:
                logger.error(f"生成知识库时出错: {str(e)}")
                logger.error(traceback.format_exc())
        
        if args.test:
            logger.info("正在运行测试...")
            try:
                result = subprocess.run(['python', 'test_knowledge_base.py'], capture_output=True, text=True)
                if result.returncode != 0:
                    logger.error(f"测试失败，返回码: {result.returncode}")
                    logger.error(f"错误输出: {result.stderr}")
                else:
                    logger.info("测试完成")
            except Exception as e:
                logger.error(f"运行测试时出错: {str(e)}")
                logger.error(traceback.format_exc())
        
        if args.run_app:
            logger.info("正在启动Web应用...")
            try:
                subprocess.run(['python', 'knowledge_base_app.py'])
            except Exception as e:
                logger.error(f"启动Web应用时出错: {str(e)}")
                logger.error(traceback.format_exc())
    except Exception as e:
        logger.error(f"运行知识库系统时出错: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
