import os
import json
import docx
import traceback
from typing import List, Dict, Any, Set
import logging
from doc_extract_util import extract_word_content
from llm import LLMClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeBaseGenerator:
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
        self.knowledge_bases = {
            "代销机构代销业务": [],
            "代销机构资管部门": [],
            "直销客户": []
        }
        self.query_sets = {
            "代销机构代销业务": set(),
            "代销机构资管部门": set(),
            "直销客户": set()
        }
    
    def process_document(self, doc_path: str, kb_name: str) -> None:
        """处理单个Word文档，提取内容并添加到知识库"""
        logger.info(f"处理文档: {doc_path}")
        try:
            # 提取文档内容
            content = extract_word_content(doc_path)
            
            # 使用LLM生成query-answer对
            qa_pairs = self._generate_qa_pairs(content, doc_path)
            
            # 更新知识库
            self._update_knowledge_base(qa_pairs, kb_name)
            
            logger.info(f"文档处理完成: {doc_path}")
        except Exception as e:
            logger.error(f"处理文档时出错 {doc_path}: {str(e)}")
            logger.error(traceback.format_exc())
    
    def _generate_qa_pairs(self, content: Dict[str, Any], doc_path: str) -> List[Dict[str, Any]]:
        """使用LLM从文档内容生成query-answer对"""
        # 构建提示词
        prompt = self._build_prompt(content, doc_path)
        
        # 调用LLM生成回答
        response = self.llm_client.generate(prompt)
        
        # 解析LLM回答为query-answer对
        try:
            # 处理可能包含Markdown代码块的响应
            content_str = response["content"]
            # 移除可能的Markdown代码块标记
            if "```json" in content_str:
                content_str = content_str.replace("```json", "").replace("```", "")
            elif "```" in content_str:
                content_str = content_str.replace("```", "")
            
            # 清理内容，确保是有效的JSON
            content_str = content_str.strip()
            
            # 尝试解析JSON
            qa_pairs = json.loads(content_str)
            return qa_pairs
        except json.JSONDecodeError as e:
            logger.error(f"无法解析LLM回答为JSON: {response['content'][:200]}...")
            logger.error(f"JSON解析错误: {str(e)}")
            return []
    
    def _build_prompt(self, content: Dict[str, Any], doc_path: str) -> str:
        """构建提示词，指导LLM生成query-answer对"""
        text_content = "\n".join(content["text_content"])
        
        tables_str = ""
        for table in content["tables"]:
            tables_str += f"表格标题: {table['title']}\n"
            tables_str += f"列: {', '.join(table['columns'])}\n"
            for row in table['rows']:
                row_str = ", ".join([f"{k}: {v}" for k, v in row.items()])
                tables_str += f"行: {row_str}\n"
            tables_str += "\n"
        
        prompt = f"""
你是一个专业的文档分析助手。请从以下文档内容中提取关键信息，并生成query-answer对。

文档路径: {doc_path}

文本内容:
{text_content}

表格内容:
{tables_str}

请根据上述内容，生成一系列query-answer对，格式如下:
[
  {{
    "query": "问题1",
    "answer": "回答1"
  }},
  {{
    "query": "问题2",
    "answer": "回答2或表格内容"
  }}
]

特别注意：
1. 请仔细分析文档中的问答结构，例如"公司法人、股东、控制人及主要投研人员是否担任或曾经担任其他公司高管或股东（持股5%以上）"这样的问题及其对应的回答。
2. 对于文档中的编号问题（如(1)、(2)、(3)等），请将问题和回答分别提取为query和answer。
3. 对于简短的回答如"是"、"否"等，也要完整提取。
4. 确保不遗漏任何问答内容，即使它们没有明确的"问题："和"回答："标记。

对于表格内容，请保持其结构，格式如下:
{{
  "title": "表格标题",
  "columns": ["列1", "列2", ...],
  "rows": [
    {{"列1": "值1", "列2": "值2", ...}},
    ...
  ]
}}

重要：请直接返回纯JSON格式，不要包含任何Markdown代码块标记（如```json或```）。确保生成的JSON格式正确，并尽可能提取文档中的所有关键信息。
"""
        return prompt
    
    def _update_knowledge_base(self, qa_pairs: List[Dict[str, Any]], kb_name: str) -> None:
        """更新知识库，处理相同query的覆盖和不同query的添加"""
        for qa_pair in qa_pairs:
            query = qa_pair["query"]
            
            # 检查是否已存在相同的query
            existing_index = None
            for i, existing_qa in enumerate(self.knowledge_bases[kb_name]):
                if existing_qa["query"] == query:
                    existing_index = i
                    break
            
            # 如果存在相同query，覆盖answer；否则添加新的qa_pair
            if existing_index is not None:
                self.knowledge_bases[kb_name][existing_index] = qa_pair
                logger.info(f"覆盖已存在的query: {query}")
            else:
                self.knowledge_bases[kb_name].append(qa_pair)
                self.query_sets[kb_name].add(query)
                logger.info(f"添加新的query: {query}")
    
    def process_directory(self, dir_path: str, kb_name: str) -> None:
        """处理目录中的所有Word文档"""
        logger.info(f"处理目录: {dir_path}")
        
        # 获取目录中的所有Word文档
        doc_files = []
        for file in os.listdir(dir_path):
            if file.endswith(".docx") or file.endswith(".doc"):
                doc_files.append(os.path.join(dir_path, file))
        
        # 按文件名排序
        doc_files.sort()
        
        # 处理每个文档
        for doc_path in doc_files:
            self.process_document(doc_path, kb_name)
    
    def save_knowledge_base(self, output_dir: str) -> None:
        """保存知识库到JSON文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        for kb_name, qa_pairs in self.knowledge_bases.items():
            output_path = os.path.join(output_dir, f"{kb_name}.json")
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(qa_pairs, f, ensure_ascii=False, indent=2)
            
            logger.info(f"知识库已保存: {output_path}")
            logger.info(f"知识库 {kb_name} 包含 {len(qa_pairs)} 个query-answer对")


def main():
    # 初始化LLM客户端
    llm_client = LLMClient()
    
    # 初始化知识库生成器
    kb_generator = KnowledgeBaseGenerator(llm_client)
    
    # 处理三个目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    kb_generator.process_directory(os.path.join(base_dir, "代销机构代销业务"), "代销机构代销业务")
    kb_generator.process_directory(os.path.join(base_dir, "代销机构资管部门"), "代销机构资管部门")
    kb_generator.process_directory(os.path.join(base_dir, "直销客户"), "直销客户")
    
    # 保存知识库
    kb_generator.save_knowledge_base(os.path.join(base_dir, "knowledge_bases"))


if __name__ == "__main__":
    main()
