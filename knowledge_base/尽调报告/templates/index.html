
<!DOCTYPE html>
<html>
<head>
    <title>尽调报告知识库</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            margin-top: 30px;
        }
        select, input, button {
            padding: 10px;
            margin: 10px 0;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #answer {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .loading {
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <h1>尽调报告知识库</h1>
    
    <div class="container">
        <label for="kb-select">选择知识库:</label>
        <select id="kb-select">
            {% for kb_name in kb_names %}
            <option value="{{ kb_name }}">{{ kb_name }}</option>
            {% endfor %}
        </select>
        
        <label for="question">输入问题:</label>
        <input type="text" id="question" placeholder="请输入您的问题...">
        
        <button id="submit-btn">提交问题</button>
        
        <div class="loading" id="loading">
            <p>正在查询，请稍候...</p>
        </div>
        
        <div id="answer"></div>
    </div>
    
    <script>
        document.getElementById('submit-btn').addEventListener('click', async function() {
            const kbName = document.getElementById('kb-select').value;
            const question = document.getElementById('question').value;
            const answerDiv = document.getElementById('answer');
            const loadingDiv = document.getElementById('loading');
            
            if (!question) {
                answerDiv.textContent = '请输入问题';
                return;
            }
            
            answerDiv.textContent = '';
            loadingDiv.style.display = 'block';
            
            try {
                const response = await fetch('/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        kb_name: kbName,
                        question: question
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    answerDiv.textContent = data.answer;
                } else {
                    answerDiv.textContent = `错误: ${data.error}`;
                }
            } catch (error) {
                answerDiv.textContent = `请求出错: ${error.message}`;
            } finally {
                loadingDiv.style.display = 'none';
            }
        });
    </script>
</body>
</html>
