import os
import json
import logging
import traceback
from knowledge_base_generator import KnowledgeBaseGenerator
from llm import LLMClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_document():
    """测试处理单个文档"""
    try:
        # 初始化LLM客户端
        llm_client = LLMClient()
        
        # 初始化知识库生成器
        kb_generator = KnowledgeBaseGenerator(llm_client)
        
        # 处理单个文档
        base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 尝试找到一个存在的文档
        doc_files = []
        for file in os.listdir(os.path.join(base_dir, "直销客户")):
            if file.endswith(".docx") or file.endswith(".doc"):
                doc_files.append(os.path.join(base_dir, "直销客户", file))
        
        if not doc_files:
            logger.error("没有找到任何Word文档")
            return
        
        # 使用第一个文档
        doc_path = doc_files[0]
        logger.info(f"使用文档: {doc_path}")
        
        # 处理文档
        kb_generator.process_document(doc_path, "测试")
        
        # 保存知识库
        test_dir = os.path.join(base_dir, "test_output")
        os.makedirs(test_dir, exist_ok=True)
        
        output_path = os.path.join(test_dir, "test_single_document.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(kb_generator.knowledge_bases["测试"], f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试结果已保存: {output_path}")
        logger.info(f"知识库包含 {len(kb_generator.knowledge_bases['测试'])} 个query-answer对")
    except Exception as e:
        logger.error(f"测试处理单个文档时出错: {str(e)}")
        logger.error(traceback.format_exc())

def test_query_answer():
    """测试问答功能"""
    try:
        # 加载测试知识库
        base_dir = os.path.dirname(os.path.abspath(__file__))
        test_file = os.path.join(base_dir, "test_output", "test_single_document.json")
        
        if not os.path.exists(test_file):
            logger.error(f"测试文件不存在: {test_file}")
            logger.error("请先运行 test_single_document() 函数")
            return
        
        with open(test_file, 'r', encoding='utf-8') as f:
            kb_content = json.load(f)
        
        logger.info(f"加载测试知识库，包含 {len(kb_content)} 个query-answer对")
        
        # 初始化LLM客户端
        llm_client = LLMClient()
        
        # 测试问题
        test_questions = [
            "公司的法定中文名称是什么？",
            "公司的主要高管有哪些？",
            "公司的投资策略是什么？"
        ]
        
        for question in test_questions:
            logger.info(f"测试问题: {question}")
            
            # 构建提示词
            prompt = f"""
你是一个专业的知识库问答助手。请根据以下知识库内容，回答用户的问题。

知识库内容:
{json.dumps(kb_content, ensure_ascii=False)}

用户问题: {question}

请根据知识库内容提供准确的回答。如果知识库中没有相关信息，请回答"抱歉，知识库中没有相关信息。"
"""
            
            # 调用LLM生成回答
            response = llm_client.generate(prompt)
            
            logger.info(f"问题: {question}")
            logger.info(f"回答: {response['content']}")
    except Exception as e:
        logger.error(f"测试问答功能时出错: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    try:
        logger.info("开始测试知识库系统")
        
        # 测试处理单个文档
        logger.info("测试处理单个文档")
        test_single_document()
        
        # 测试问答功能
        logger.info("测试问答功能")
        test_query_answer()
        
        logger.info("测试完成")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
