import os
import json
import logging
import traceback
from typing import List, Dict, Any
from llm import LLMClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeBaseMCP:
    def __init__(self):
        """初始化知识库MCP"""
        self.llm_client = LLMClient()
        self.kb_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "knowledge_bases")
        self.knowledge_bases = self._load_knowledge_bases()

    def _load_knowledge_bases(self) -> Dict[str, Any]:
        """加载所有知识库"""
        knowledge_bases = {}
        try:
            if not os.path.exists(self.kb_dir):
                logger.warning(f"知识库目录不存在: {self.kb_dir}")
                return knowledge_bases
            
            for file in os.listdir(self.kb_dir):
                if file.endswith(".json"):
                    kb_name = file.replace(".json", "")
                    kb_path = os.path.join(self.kb_dir, file)
                    
                    try:
                        with open(kb_path, 'r', encoding='utf-8') as f:
                            knowledge_bases[kb_name] = json.load(f)
                        logger.info(f"加载知识库: {kb_name}, 包含 {len(knowledge_bases[kb_name])} 个query-answer对")
                    except Exception as e:
                        logger.error(f"加载知识库 {kb_name} 时出错: {str(e)}")
                        logger.error(traceback.format_exc())
            
            return knowledge_bases
        except Exception as e:
            logger.error(f"加载知识库时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {}

    def _query_knowledge_base(self, kb_content: Dict[str, Any], question: str) -> str:
        """查询知识库内容"""
        try:
            prompt = f"""
你是一个专业的知识库问答助手。请根据以下知识库内容，回答用户的问题。

知识库内容:
{json.dumps(kb_content, ensure_ascii=False)}

用户问题: {question}

请根据知识库内容提供准确的回答。如果知识库中没有相关信息，请回答"抱歉，知识库中没有相关信息。"

回答要求：
1. 直接回答问题，不需要解释你是如何找到答案的
2. 如果知识库中有多个相关信息，请综合这些信息给出完整回答
3. 保持回答的准确性，不要添加知识库中没有的信息
4. 如果答案是表格形式，请以易于阅读的方式呈现
5. 如果答案是"是"或"否"这样的简短回答，可以适当补充相关信息，但要确保准确性
"""
            
            response = self.llm_client.generate(prompt)
            return response["content"]
        except Exception as e:
            logger.error(f"查询知识库时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return f"查询出错: {str(e)}"

    def get_available_knowledge_bases(self) -> List[str]:
        """
        获取所有可用的知识库名称
        
        Returns:
            List[str]: 知识库名称列表
        """
        return list(self.knowledge_bases.keys())

    def query(self, kb_name: str, question: str) -> str:
        """
        查询指定知识库
        
        Args:
            kb_name: 知识库名称
            question: 用户问题
            
        Returns:
            str: 回答内容
        """
        try:
            if not kb_name or not question:
                return "错误：缺少知识库名称或问题"
            
            if kb_name not in self.knowledge_bases:
                return f"错误：知识库 {kb_name} 不存在"
            
            return self._query_knowledge_base(self.knowledge_bases[kb_name], question)
        except Exception as e:
            logger.error(f"处理查询请求时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return f"处理查询请求时出错: {str(e)}"

# 创建MCP实例
knowledge_base_mcp = KnowledgeBaseMCP()

def get_knowledge_bases() -> List[str]:
    """
    MCP接口：获取所有可用的知识库名称
    
    Returns:
        List[str]: 知识库名称列表
    """
    return knowledge_base_mcp.get_available_knowledge_bases()

def query_knowledge_base(kb_name: str, question: str) -> str:
    """
    MCP接口：查询指定知识库
    
    Args:
        kb_name: 知识库名称
        question: 用户问题
        
    Returns:
        str: 回答内容
    """
    return knowledge_base_mcp.query(kb_name, question)

if __name__ == "__main__":
    # 测试代码
    kb_list = get_knowledge_bases()
    print(f"可用的知识库: {kb_list}")
    
    if kb_list:
        test_kb = kb_list[0]  # 使用第一个知识库：代销机构资管部门
        test_question = "请提供私募基金管理人的公司名称"  # 使用一个实际的问题
        result = query_knowledge_base(test_kb, test_question)
        print(f"\n测试查询:")
        print(f"知识库: {test_kb}")
        print(f"问题: {test_question}")
        print(f"回答: {result}")
        
        # 测试第二个知识库
        test_kb2 = kb_list[1]  # 使用第二个知识库：代销机构代销业务
        test_question2 = "代销机构需要满足哪些条件？"
        result2 = query_knowledge_base(test_kb2, test_question2)
        print(f"\n第二个测试查询:")
        print(f"知识库: {test_kb2}")
        print(f"问题: {test_question2}")
        print(f"回答: {result2}") 