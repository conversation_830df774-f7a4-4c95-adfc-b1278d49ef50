from typing import Literal, Dict, Any, List
import time
import httpx
import traceback
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

ModelName = Literal[
    "DeepSeek-R1", "DeepSeek-V3", "Qwen3-235B-A22B", "Qwen2_5-VL-32B-Instruct"
]


def get_model(
    model_name: ModelName = "Qwen3-235B-A22B",
    base_url: str = "http://llm.yanfuinvest.com/v1",
    api_key: str = "sk-ISyVIYc3933iApsiLaz-HQ",
):
    http_client = httpx.Client(
        timeout=httpx.Timeout(600.0),
        follow_redirects=True
    )
    
    async_http_client = httpx.AsyncClient(
        timeout=httpx.Timeout(600.0),
        follow_redirects=True
    )
    
    return ChatOpenAI(
        model=model_name,
        base_url=base_url,
        api_key=api_key,
        temperature=0,
        max_retries=1,
        request_timeout=600,
        http_client=http_client,
        async_client=async_http_client
    )


class LLMClient:
    def __init__(self, model_name: ModelName = "Qwen3-235B-A22B"):
        self._model = get_model(model_name)
        self.model_name = model_name
        self.max_retries = 5
        self.retry_delay = 10
        logger.info("初始化LLM客户端")
    
    def generate(self, prompt: str, max_tokens: int = 2000) -> Dict[str, Any]:
        """
        生成回答
        """
        try:
            logger.info("开始生成回答")
            logger.info(f"提示词长度: {len(prompt)}")
            logger.info(f"提示词前100个字符: {prompt[:100]}")
            
            messages = [
                SystemMessage(content="""你是一个专业的文档分析助手。你的任务是从文档中提取信息并按照指定格式输出JSON。

重要规则：
1. 严格按照要求的格式输出
2. 保持原文内容不变，不要改写或解释
3. 确保输出的JSON格式正确
4. 对于表格，保持完整的结构信息
5. 如果遇到不确定的内容，保持原文格式
6. 直接返回纯JSON格式，不要包含任何Markdown代码块标记（如```json或```）
7. 不要在JSON前后添加任何额外的文本或解释
8. 特别注意识别文档中的问答结构，包括：
   - 编号问题（如(1)、(2)、(3)等）及其对应回答
   - 问题后面跟着的简短回答（如"是"、"否"等）
   - 没有明确标记为"问题："和"回答："的内容
9. 确保不遗漏任何问答内容，即使它们格式不规范"""),
                HumanMessage(content=prompt)
            ]
            
            for attempt in range(self.max_retries):
                try:
                    response = self._model.invoke(messages)
                    logger.info("生成回答完成")
                    return {
                        "content": response.content,
                        "key_points": [],
                        "source": "LLM"
                    }
                except Exception as e:
                    if attempt == self.max_retries - 1:
                        logger.error(f"调用LLM时发生错误: {str(e)}")
                        logger.error(traceback.format_exc())
                        return {
                            "content": "",
                            "key_points": [],
                            "source": f"发生错误: {str(e)}"
                        }
                    logger.error(f"发生错误，{self.retry_delay}秒后重试 (第{attempt + 1}次): {str(e)}")
                    logger.error(traceback.format_exc())
                    time.sleep(self.retry_delay)
            
        except Exception as e:
            logger.error(f"生成回答时发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "content": "",
                "key_points": [],
                "source": f"发生错误: {str(e)}"
            }
