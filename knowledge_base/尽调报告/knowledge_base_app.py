import os
import json
import logging
import traceback
from flask import Flask, render_template, request, jsonify
from llm import LLMClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
llm_client = LLMClient()

# 知识库路径
KB_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "knowledge_bases")

# 加载知识库
def load_knowledge_bases():
    knowledge_bases = {}
    try:
        if not os.path.exists(KB_DIR):
            logger.warning(f"知识库目录不存在: {KB_DIR}")
            return knowledge_bases
        
        for file in os.listdir(KB_DIR):
            if file.endswith(".json"):
                kb_name = file.replace(".json", "")
                kb_path = os.path.join(KB_DIR, file)
                
                try:
                    with open(kb_path, 'r', encoding='utf-8') as f:
                        knowledge_bases[kb_name] = json.load(f)
                    logger.info(f"加载知识库: {kb_name}, 包含 {len(knowledge_bases[kb_name])} 个query-answer对")
                except Exception as e:
                    logger.error(f"加载知识库 {kb_name} 时出错: {str(e)}")
                    logger.error(traceback.format_exc())
        
        return knowledge_bases
    except Exception as e:
        logger.error(f"加载知识库时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {}

# 查询知识库
def query_knowledge_base(kb_content, question):
    try:
        # 构建提示词
        prompt = f"""
你是一个专业的知识库问答助手。请根据以下知识库内容，回答用户的问题。

知识库内容:
{json.dumps(kb_content, ensure_ascii=False)}

用户问题: {question}

请根据知识库内容提供准确的回答。如果知识库中没有相关信息，请回答"抱歉，知识库中没有相关信息。"

回答要求：
1. 直接回答问题，不需要解释你是如何找到答案的
2. 如果知识库中有多个相关信息，请综合这些信息给出完整回答
3. 保持回答的准确性，不要添加知识库中没有的信息
4. 如果答案是表格形式，请以易于阅读的方式呈现
5. 如果答案是"是"或"否"这样的简短回答，可以适当补充相关信息，但要确保准确性
"""
        
        # 调用LLM生成回答
        response = llm_client.generate(prompt)
        return response["content"]
    except Exception as e:
        logger.error(f"查询知识库时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return f"查询出错: {str(e)}"

# 路由
@app.route('/')
def index():
    try:
        knowledge_bases = load_knowledge_bases()
        kb_names = list(knowledge_bases.keys())
        return render_template('index.html', kb_names=kb_names)
    except Exception as e:
        logger.error(f"渲染首页时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return f"出错: {str(e)}"

@app.route('/query', methods=['POST'])
def query():
    try:
        data = request.json
        kb_name = data.get('kb_name')
        question = data.get('question')
        
        if not kb_name or not question:
            return jsonify({"error": "缺少知识库名称或问题"}), 400
        
        knowledge_bases = load_knowledge_bases()
        if kb_name not in knowledge_bases:
            return jsonify({"error": f"知识库 {kb_name} 不存在"}), 404
        
        answer = query_knowledge_base(knowledge_bases[kb_name], question)
        return jsonify({"answer": answer})
    except Exception as e:
        logger.error(f"处理查询请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": f"处理查询请求时出错: {str(e)}"}), 500

# 创建模板目录
def create_templates():
    try:
        templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "templates")
        os.makedirs(templates_dir, exist_ok=True)
        
        # 创建index.html模板
        index_html = """
<!DOCTYPE html>
<html>
<head>
    <title>尽调报告知识库</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            margin-top: 30px;
        }
        select, input, button {
            padding: 10px;
            margin: 10px 0;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #answer {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .loading {
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <h1>尽调报告知识库</h1>
    
    <div class="container">
        <label for="kb-select">选择知识库:</label>
        <select id="kb-select">
            {% for kb_name in kb_names %}
            <option value="{{ kb_name }}">{{ kb_name }}</option>
            {% endfor %}
        </select>
        
        <label for="question">输入问题:</label>
        <input type="text" id="question" placeholder="请输入您的问题...">
        
        <button id="submit-btn">提交问题</button>
        
        <div class="loading" id="loading">
            <p>正在查询，请稍候...</p>
        </div>
        
        <div id="answer"></div>
    </div>
    
    <script>
        document.getElementById('submit-btn').addEventListener('click', async function() {
            const kbName = document.getElementById('kb-select').value;
            const question = document.getElementById('question').value;
            const answerDiv = document.getElementById('answer');
            const loadingDiv = document.getElementById('loading');
            
            if (!question) {
                answerDiv.textContent = '请输入问题';
                return;
            }
            
            answerDiv.textContent = '';
            loadingDiv.style.display = 'block';
            
            try {
                const response = await fetch('/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        kb_name: kbName,
                        question: question
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    answerDiv.textContent = data.answer;
                } else {
                    answerDiv.textContent = `错误: ${data.error}`;
                }
            } catch (error) {
                answerDiv.textContent = `请求出错: ${error.message}`;
            } finally {
                loadingDiv.style.display = 'none';
            }
        });
    </script>
</body>
</html>
"""
        
        with open(os.path.join(templates_dir, "index.html"), 'w', encoding='utf-8') as f:
            f.write(index_html)
        
        logger.info(f"模板创建完成: {templates_dir}")
    except Exception as e:
        logger.error(f"创建模板时出错: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    try:
        # 创建模板
        create_templates()
        
        # 启动应用
        logger.info("启动Web应用...")
        app.run(debug=True, host='0.0.0.0', port=5001)
    except Exception as e:
        logger.error(f"启动Web应用时出错: {str(e)}")
        logger.error(traceback.format_exc())
