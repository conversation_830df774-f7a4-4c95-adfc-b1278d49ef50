# 尽调报告知识库系统

这是一个自动提取尽调报告内容并构建可视化问答知识库的系统。系统可以从Word文档中提取内容，生成query-answer对，并提供Web界面进行问答交互。

## 功能特点

- 自动从Word文档中提取文本和表格内容
- 使用大语言模型生成结构化的query-answer对
- 支持多个知识库的管理（代销机构代销业务、代销机构资管部门、直销客户）
- 提供Web界面进行知识库问答
- 完善的错误处理和日志记录

## 系统架构

系统由以下几个主要组件构成：

1. **文档内容提取工具**（doc_extract_util.py）：负责从Word文档中提取文本和表格内容
2. **大语言模型客户端**（llm.py）：负责与大语言模型API交互，生成query-answer对
3. **知识库生成器**（knowledge_base_generator.py）：负责处理文档内容，生成知识库
4. **知识库应用程序**（knowledge_base_app.py）：提供Web界面进行知识库问答
5. **系统运行脚本**（run_knowledge_base_system.py）：提供命令行接口运行系统
6. **测试脚本**（test_knowledge_base.py）：用于测试系统功能

## 安装步骤

1. 确保已安装Python 3.8或更高版本
2. 安装所需依赖：

```bash
pip install python-docx flask langchain-openai httpx
```

## 使用方法

### 生成知识库

将Word文档放置在以下目录中：

- `尽调报告/代销机构代销业务/`：存放代销机构代销业务相关的Word文档
- `尽调报告/代销机构资管部门/`：存放代销机构资管部门相关的Word文档
- `尽调报告/直销客户/`：存放直销客户相关的Word文档

然后运行以下命令生成知识库：

```bash
python run_knowledge_base_system.py --generate
```

生成的知识库将保存在`尽调报告/knowledge_bases/`目录中。

### 运行Web应用

运行以下命令启动Web应用：

```bash
python run_knowledge_base_system.py --run-app
```

然后在浏览器中访问`http://localhost:5000`即可使用知识库问答系统。

### 运行测试

运行以下命令测试系统功能：

```bash
python run_knowledge_base_system.py --test
```

## 知识库格式

知识库以JSON格式存储，每个知识库包含一系列query-answer对，格式如下：

```json
[
  {
    "query": "问题1",
    "answer": "回答1"
  },
  {
    "query": "问题2",
    "answer": "回答2或表格内容"
  }
]
```

对于表格内容，answer字段的格式如下：

```json
{
  "title": "表格标题",
  "columns": ["列1", "列2", ...],
  "rows": [
    {"列1": "值1", "列2": "值2", ...},
    ...
  ]
}
```

## 工作流程

1. 系统从指定目录读取Word文档
2. 使用doc_extract_util.py提取文档内容
3. 使用llm.py调用大语言模型生成query-answer对
4. 将生成的query-answer对保存到知识库中
5. 用户通过Web界面选择知识库并提问
6. 系统使用大语言模型根据知识库内容回答问题

## 注意事项

- 确保Word文档格式规范，以便系统能够正确提取内容
- 大语言模型API需要网络连接
- 知识库生成过程可能需要一些时间，取决于文档数量和大小
- 系统会自动处理相同query的覆盖和不同query的添加
- 系统特别优化了对文档中问答结构的识别，包括：
  - 编号问题（如(1)、(2)、(3)等）及其对应回答
  - 问题后面跟着的简短回答（如"是"、"否"等）
  - 没有明确标记为"问题："和"回答："的内容

## 日志记录

系统会记录详细的日志信息，包括：

- 文档处理过程
- 知识库生成过程
- Web应用运行状态
- 错误和异常信息

日志信息会输出到控制台，可以通过重定向保存到文件中。

## 错误处理

系统实现了完善的错误处理机制，包括：

- 文档读取错误处理
- 大语言模型API调用错误处理
- 知识库生成错误处理
- Web应用运行错误处理

当发生错误时，系统会记录详细的错误信息和堆栈跟踪，以便调试和修复。
