import requests

def test_api():
    url = "http://llm.yanfuinvest.com/v1/models"
    headers = {
        "Authorization": f"Bearer sk-ISyVIYc3933iApsiLaz-HQ"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_api()
