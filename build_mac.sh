rm -rf dist
cd ./market/word-auto-fill && npm run build
cd ../..
pyinstaller --add-data "market/word-auto-fill/dist:market/word-auto-fill/dist" --onefile --hidden-import=tiktoken_ext.openai_public --hidden-import=tiktoken_ext entry.py
mv ./dist/entry ./dist/word-auto-fill
cp ./market/word-auto-fill/dist/manifest.xml ./dist/word-auto-fill.xml
echo "cp word-auto-fill.xml ~/Library/Containers/com.microsoft.Word/Data/Documents/wef" > ./dist/install.sh
echo "rm ~/Library/Containers/com.microsoft.Word/Data/Documents/wef/word-auto-fill.xml" > ./dist/uninstall.sh
chmod +x ./dist/install.sh
chmod +x ./dist/uninstall.sh
cp ./knowledge_base.py/尽调报告/knowledge_bases/第一季度_knowledge_base_min.md ./dist/第一季度_knowledge_base.md
cp ./config_min.toml ./dist/config.toml
cp ./table_processor/data/尽调报告/尽职调查报告_min.docx ./dist/尽职调查报告.docx
mkdir release
cd ./dist && tar czvf ../release/release_mac.tar.gz .