import docx
import pandas as pd
from docx.oxml.text.paragraph import CT_P
from docx.oxml.table import CT_Tbl, CT_Row, CT_Tc
from docx.text.run import Run


def convert_to_json(index, element):
    """
    将word文件转换为json，并保留位置信息。
    args:
        index: 当前元素的索引
        element: 当前元素
    """
    json_data = {}
    json_data["index"] = index
    if isinstance(element, docx.document.Document):
        json_data["type"] = "Document"
        json_data["content"] = [convert_to_json(index, element) for index, element in enumerate(element.element.body.inner_content_elements)]
    elif isinstance(element, CT_P):
        json_data["type"] = "Paragraph"
        json_data["content"] = element.text
    elif isinstance(element, CT_Tbl):
        json_data["type"] = "Table"
        json_data["content"] = [convert_to_json(tri, tr) for tri, tr in enumerate(element.tr_lst)]
    elif isinstance(element, CT_Row):
        json_data["type"] = "Row"
        json_data["content"] = [convert_to_json(tci, tc) for tci, tc in enumerate(element.tc_lst)]
    elif isinstance(element, CT_Tc):
        json_data["type"] = "Cell"
        json_data["content"] = [convert_to_json(index, element) for index, element in enumerate(element.inner_content_elements)]
    else:
        json_data["type"] = "Unknown element"
        json_data["content"] = "Unknown element"
    return json_data


def convert_to_json_with_xpath(element, xpath=""):
    """
    将word文件转换为json，并保留位置信息。
    args:
        index: 当前元素的索引
        element: 当前元素
        xpath: 节点的xpath
    """
    json_data = {}
    json_data['xpath'] = xpath
    if isinstance(element, docx.document.Document):
        json_data["type"] = "Document"
        json_data["content"] = [convert_to_json_with_xpath(element, f"{xpath}.[{index}]") for index, element in enumerate(element.element.body.inner_content_elements)]
    elif isinstance(element, CT_P):
        json_data["type"] = "Paragraph"
        json_data["content"] = element.text
    elif isinstance(element, CT_Tbl):
        json_data["type"] = "Table"
        json_data["content"] = [convert_to_json_with_xpath(tr, f"{xpath}.[{tri}]") for tri, tr in enumerate(element.tr_lst)]
    elif isinstance(element, CT_Row):
        json_data["type"] = "Row"
        json_data["content"] = [convert_to_json_with_xpath(tc, f"{xpath}.[{tci}]") for tci, tc in enumerate(element.tc_lst)]
    elif isinstance(element, CT_Tc):
        json_data["type"] = "Cell"
        json_data["content"] = [convert_to_json_with_xpath(element, f"{xpath}.[{index}]") for index, element in enumerate(element.inner_content_elements)]
    else:
        json_data["type"] = "Unknown element"
        json_data["content"] = "Unknown element"
    return json_data


def get_element(parent_element, position):
    """
    根据位置信息获取元素
    args:
        parent_element: 当前节点
        position: 目标节点相对当前节点的位置 {"child_position": {"index": 1, "child_position": None}}
    """
    child_position = position.get('child_position')
    if isinstance(parent_element, CT_P):
        if child_position:
            raise Exception("paragraph 不能有子节点")
        return parent_element
    if child_position and not isinstance(parent_element, CT_P):
        if isinstance(parent_element, docx.document.Document):
            child_element = parent_element.element.body.inner_content_elements[child_position['index']]
        elif isinstance(parent_element, CT_Tbl):
            child_element = parent_element.tr_lst[child_position['index']]
        elif isinstance(parent_element, CT_Row):
            child_element = parent_element.tc_lst[child_position['index']]
        elif isinstance(parent_element, CT_Tc):
            child_element = parent_element.inner_content_elements[child_position['index']]
        return get_element(child_element, child_position)
    else:
        return parent_element


def get_element_by_xpath(element, xpath):
    """
    根据xpath获取对应element
    """
    if xpath == ".":
        return element
    xpath = xpath.lstrip(" ").lstrip(".")
    paths = xpath.split('.')

    path = paths[0]
    assert path.startswith('[') and path.endswith(']'), f"{path} 不合法"
    index = int(path[1:-1])
    if isinstance(element, docx.document.Document):
        child_element = element.element.body.inner_content_elements[index]
    elif isinstance(element, CT_Tbl):
        child_element = element.tr_lst[index]
    elif isinstance(element, CT_Row):
        child_element = element.tc_lst[index]
    elif isinstance(element, CT_Tc):
        child_element = element.inner_content_elements[index]
    else:
        raise Exception("Not supported element")

    child_xpath = xpath.lstrip(path)
    if len(child_xpath) == 0:
        return child_element
    else:
        return get_element_by_xpath(child_element, child_xpath)


def extract_text(element, final_text=True):
    """
    提取元素中的文本信息， 段落间使用\n拼接， table转换为markdown
    args:
        element: 目标元素
        final_text: 是否返回最终的文本，对于table，我们往往希望整个table一起序列化，所以需要这个参数进行控制row不要直接进行拼接
    """
    if isinstance(element, CT_P):
        text = element.text
    elif isinstance(element, docx.document.Document):
        texts = [extract_text(element) for element in element.element.body.inner_content_elements]
        if final_text:
            text = '\n'.join(texts)
        else:
            text = texts
    elif isinstance(element, CT_Tbl):
        texts = [extract_text(tr, final_text=False) for tr in element.tr_lst]
        df = pd.DataFrame(texts)
        text = df.to_markdown()
    elif isinstance(element, CT_Row):
        texts = [extract_text(tc) for tc in element.tc_lst]
        if final_text:
            text = '\t'.join(texts)
        else:
            text = [extract_text(tc) for tc in element.tc_lst]
    elif isinstance(element, CT_Tc):
        text = '\n'.join([extract_text(tc_element) for tc_element in element.inner_content_elements])
    else:
        text = "Unknown element"
    return text


def get_table_elements(document):
    """
    获取document中的所有table元素
    """
    return [element for element in document.element.body.inner_content_elements if isinstance(element, CT_Tbl)]


def write_by_xpath(document, xpath, content):
    """
    根据xpath写入内容
    args:
        xpath: 目标xpath
        content: 写入的内容
    """
    element = get_element_by_xpath(document, xpath)
    if isinstance(element, CT_Tc):
        element = element.inner_content_elements[0]
    assert isinstance(element, CT_P), "不支持写入非paragraph类型"
    element.clear_content()
    r = element.add_r()
    run = Run(r, element)
    run.text = content
    return True

    
if __name__ == "__main__":
    print(".".lstrip(" ").lstrip("."))
