from langchain.chat_models import init_chat_model


local_model_list = ["DeepSeek-R1", "DeepSeek-V3", "Qwen3-235B-A22B", "Qwen2_5-VL-32B-Instruct", "Qwen3-30B-A3B", "gpt-oss-120b"]


def get_local_model(
    model_name: str = "DeepSeek-R1",
    base_url: str = "https://llm.yanfuinvest.com/v1",
    api_key: str = "sk-ISyVIYc3933iApsiLaz-HQ",
    temperature: float = 0,
):
    assert model_name in local_model_list, f"Model {model_name} is not supported"
    return init_chat_model(
        model=model_name,
        model_provider="openai",
        base_url=base_url,
        api_key=api_key,
        temperature=temperature,
        max_retries=1,
        request_timeout=600
    )


def get_openreuter_model(
    model_name: str = "DeepSeek-R1",
    base_url: str = "https://openrouter.ai/api/v1",
    api_key: str = "xxxx",
    temperature: float = 0,
):
    return init_chat_model(
        model=model_name,
        model_provider="openai",
        base_url=base_url,
        api_key=api_key,
        temperature=temperature,
        max_retries=1,
        request_timeout=600
    )


def get_model(
    model_name: str = "DeepSeek-R1",
    model_source: str = "local",
    temperature: float = 0,
):
    if model_source == "local":
        return get_local_model(model_name, temperature=temperature)
    elif model_source == "openreuter":
        return get_openreuter_model(model_name, temperature=temperature)
    else:
        raise ValueError(f"Model source {model_source} is not supported")